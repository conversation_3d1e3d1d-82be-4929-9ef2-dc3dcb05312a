Analis<PERSON> koordinat berikut dan hasilkan rekomendasi ide bisnis terbaik berbasis rubrik.

{ 
  "koordinat": { "lat": {latitude}, "lng": {longitude} },
  "lokasi_nama": "{location_name}", 
  "radius_km": {radius_km}, 
  "preferensi": {
    "modal": "{rendah|sedang|tinggi}",
    "tipe_bisnis_diutamakan": ["ritel", "kuliner", "jasa", "kesehatan", "edukasi", "pertanian", "digital", "lainnya"],
    "hindari": ["{opsional}"]
  },
  "batasan": {
    "maks_ide": 5,
    "output_bahasa": "id"
  }
}


Instruksi khusus:
1. <PERSON><PERSON> 1–5 ide terbaik dalam array ide_bisnis, urut dari skor tertinggi.
2. Tampilkan tabel skor komponen per ide pada field skor_komponen.
3. <PERSON><PERSON> estimasi sederhana biaya awal (range) & waktu balik modal (bulan) pada estimasi.
4. <PERSON><PERSON> butuh data yang tidak pasti, pakai asumsi wajar dan tulis di asumsi.
5. Kembalikan hanya JSON sesuai skema berikut.