<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\GoogleAuthController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\PaymentMethodController;
use App\Http\Controllers\SubscriptionPackageController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\BusinessIdeasController;
use App\Http\Controllers\WorksheetController;
use App\Http\Controllers\HppAiController;
use App\Http\Controllers\FixPresetController;
use App\Http\Controllers\HppExportController;
use App\Http\Controllers\HppRecordController;
use App\Http\Controllers\AdsImageGenController;
use App\Http\Controllers\PhotoEnhancerController;
use App\Http\Controllers\ImageBlenderController;
use App\Http\Controllers\TestimonialController;
use App\Http\Controllers\MarketingContentController;
use App\Http\Controllers\VoiceGeneratorController;
use App\Http\Controllers\VideoGeneratorController;

// Public authentication routes
Route::post('/auth/register', [AuthController::class, 'register']);
Route::post('/auth/login', [AuthController::class, 'login']);
Route::get('/auth/check', [AuthController::class, 'check']);

// Google OAuth routes
Route::get('/auth/google', [GoogleAuthController::class, 'redirectToGoogle']);
Route::get('/auth/google/callback', [GoogleAuthController::class, 'handleGoogleCallback']);
Route::post('/auth/google/verify', [GoogleAuthController::class, 'handleFrontendCallback']);

// Public routes for payment methods
Route::get('/payment-methods/active', [PaymentMethodController::class, 'getActive']);

// Public routes for subscription packages
Route::get('/subscription-packages/active', [SubscriptionPackageController::class, 'getActive']);

// Protected routes requiring authentication
Route::middleware(['auth:sanctum', 'auth.api'])->group(function () {
    Route::get('/auth/me', [AuthController::class, 'me']);
    Route::post('/auth/logout', [AuthController::class, 'logout']);

    // User management routes (admin only)
    Route::get('/users', [UserController::class, 'index']);
    Route::post('/users', [UserController::class, 'store']);
    Route::put('/users/{id}', [UserController::class, 'update']);
    Route::delete('/users/{id}', [UserController::class, 'destroy']);
    Route::patch('/users/{id}/toggle-status', [UserController::class, 'toggleStatus']);

    // Payment method management routes (admin only)
    Route::get('/payment-methods', [PaymentMethodController::class, 'index']);
    Route::post('/payment-methods', [PaymentMethodController::class, 'store']);
    Route::get('/payment-methods/{id}', [PaymentMethodController::class, 'show']);
    Route::put('/payment-methods/{id}', [PaymentMethodController::class, 'update']);
    Route::delete('/payment-methods/{id}', [PaymentMethodController::class, 'destroy']);
    Route::patch('/payment-methods/{id}/toggle-status', [PaymentMethodController::class, 'toggleStatus']);

    // Subscription package management routes (admin only)
    Route::get('/subscription-packages', [SubscriptionPackageController::class, 'index']);
    Route::post('/subscription-packages', [SubscriptionPackageController::class, 'store']);
    Route::get('/subscription-packages/{id}', [SubscriptionPackageController::class, 'show']);
    Route::put('/subscription-packages/{id}', [SubscriptionPackageController::class, 'update']);
    Route::delete('/subscription-packages/{id}', [SubscriptionPackageController::class, 'destroy']);
    Route::patch('/subscription-packages/{id}/toggle-status', [SubscriptionPackageController::class, 'toggleStatus']);

    // Admin Invoice routes
    Route::get('/admin/invoices/pending-verification', [InvoiceController::class, 'getPendingVerificationInvoices']);
    Route::post('/admin/invoices/{id}/verify', [InvoiceController::class, 'verifyPayment']);

    // Invoice routes (user)
    Route::post('/invoices/subscription', [InvoiceController::class, 'createSubscriptionInvoice']);
    Route::get('/invoices', [InvoiceController::class, 'getUserInvoices']);
    Route::get('/invoices/{id}', [InvoiceController::class, 'show']);
    Route::post('/invoices/{id}/submit-payment', [InvoiceController::class, 'submitPayment']);
    Route::post('/invoices/{id}/pay', [InvoiceController::class, 'simulatePayment']);
});

Route::post('/business-ideas', [BusinessIdeasController::class, 'suggest']);
Route::post('/business-ideas/fast', [BusinessIdeasController::class, 'suggestFast']);

// HPP AI endpoints
Route::post('/ads/suggestions', [HppAiController::class, 'adsSuggestions']);
Route::post('/quick/estimate', [HppAiController::class, 'quickEstimate']);
Route::post('/ritel/estimate', [HppAiController::class, 'ritelEstimate']);
Route::post('/price/suggest', [HppAiController::class, 'priceSuggest']);
Route::post('/manufaktur/estimate', [HppAiController::class, 'manufakturEstimate']);

Route::post('/jasa/estimate', [HppAiController::class, 'jasaEstimate']);

Route::post('/turunan/estimate', [HppAiController::class, 'turunanEstimate']);


Route::get('/competitors', [BusinessIdeasController::class, 'competitors']);

Route::get('/worksheets', [WorksheetController::class, 'index']);
Route::post('/worksheets', [WorksheetController::class, 'store']);
Route::put('/worksheets/{worksheet}', [WorksheetController::class, 'update']);
Route::delete('/worksheets/{worksheet}', [WorksheetController::class, 'destroy']);

// Fix cost presets
Route::get('/fix-presets', [FixPresetController::class, 'index']);
Route::post('/fix-presets', [FixPresetController::class, 'store']);
Route::delete('/fix-presets/{preset}', [FixPresetController::class, 'destroy']);

// HPP Records (riwayat simpan perhitungan)
Route::get('/hpp-records', [HppRecordController::class, 'index']);
Route::post('/hpp-records', [HppRecordController::class, 'store']);
Route::delete('/hpp-records/{record}', [HppRecordController::class, 'destroy']);

// Export XLSX
Route::post('/ritel/export-xlsx', [HppExportController::class, 'exportRitel']);
Route::post('/manufaktur/export-xlsx', [HppExportController::class, 'exportManufaktur']);
Route::post('/jasa/export-xlsx', [HppExportController::class, 'exportJasa']);

Route::post('/turunan/export-xlsx', [HppExportController::class, 'exportTurunan']);

// Ads Image Generator
Route::post('/ads-image-gen/generate', [AdsImageGenController::class, 'generate']);

// Photo Enhancer
Route::post('/photo-enhancer/generate', [PhotoEnhancerController::class, 'generate']);

// Image Blender
Route::post('/image-blender/generate', [ImageBlenderController::class, 'generate']);

// Testimonial Generator
Route::post('/generate-testimonial', [TestimonialController::class, 'generateTestimonial']);

// Marketing Content Generator
Route::post('/marketing-content/generate-text', [MarketingContentController::class, 'generateText']);
Route::post('/marketing-content/generate-voice', [VoiceGeneratorController::class, 'generateVoice']);
Route::post('/marketing-content/generate-video-step1', [VideoGeneratorController::class, 'generateNarasiAndConcept']);
Route::post('/marketing-content/generate-video-step2', [VideoGeneratorController::class, 'generateVoiceOver']);
Route::post('/marketing-content/generate-video-step3', [VideoGeneratorController::class, 'generateSilentVideo']);
Route::post('/marketing-content/generate-video-step4', [VideoGeneratorController::class, 'combineVideoWithAudio']);
Route::post('/marketing-content/check-video-status', [VideoGeneratorController::class, 'checkVideoStatus']);
Route::get('/marketing-content/test-ffmpeg', [VideoGeneratorController::class, 'testFFmpeg']);

// Serve storage files with CORS headers
Route::get('/storage/{path}', function (Request $request, $path) {
    $fullPath = storage_path('app/public/' . $path);

    if (!file_exists($fullPath)) {
        abort(404);
    }

    $mimeType = mime_content_type($fullPath);
    $content = file_get_contents($fullPath);

    $origin = $request->headers->get('Origin');
    $allowedOrigins = [
        'http://localhost:5173',
        'http://localhost:5174',
        'http://127.0.0.1:5173',
        'http://127.0.0.1:5174',
        'https://app.gooap.com',
        'https://api.gooap.com',
        'https://gooap.com',
        'https://www.gooap.com',
    ];

    $allowOrigin = in_array($origin, $allowedOrigins) ? $origin : '*';

    return response($content)
        ->header('Content-Type', $mimeType)
        ->header('Cache-Control', 'public, max-age=3600')
        ->header('Access-Control-Allow-Origin', $allowOrigin)
        ->header('Access-Control-Allow-Methods', 'GET, OPTIONS')
        ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        ->header('Access-Control-Allow-Credentials', 'true');
})->where('path', '.*');
