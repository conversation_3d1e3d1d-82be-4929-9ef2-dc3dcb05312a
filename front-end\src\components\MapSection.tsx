import React from 'react';
import { MapPin, Star } from 'lucide-react';

const MapSection: React.FC = () => {
  const businessSuggestions = [
    {
      title: "Kedai Kopi Spesialis & Jajanan Modern",
      description: "<PERSON>jadi salah satu ide komoditas dengan bagi bagi kualitas, su<PERSON><PERSON> nyaman, dan camilan kekinian."
    },
    {
      title: "Pusat Belajar & Co-working Mini dengan Internet Cepat",
      description: "Memanfaatkan mahasiswa/pelajar disekitar dengan konsep nyaman, internet cepat, dan lingkungan produktif."
    },
    {
      title: "Warung Makan Sehat & Katering Online untuk Komunitas",
      description: "Menyediakan makanan sehat, menyasar mahasiswa hingga pekerja yang kerja sibuk di Karangwidoro."
    }
  ];

  return (
    <div className="space-y-6">
      {/* Map Container */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="p-4 border-b border-gray-200">
          <h3 className="font-medium text-gray-800 mb-1">Lok<PERSON>aha</h3>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <MapPin className="w-4 h-4" />
            <span>Karangwidoro, Malang Regency, East Java, Indonesia</span>
          </div>
        </div>

        {/* Mock Map Area */}
        <div className="h-[420px] md:h-[560px] bg-gradient-to-br from-green-100 to-blue-100 relative">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="bg-white rounded-lg shadow-lg p-4 max-w-sm">
              <div className="flex items-center gap-2 mb-2">
                <MapPin className="w-5 h-5 text-red-500" />
                <span className="font-medium text-gray-800">Lokasi Terpilih</span>
              </div>
              <p className="text-sm text-gray-600">Karangwidoro, Malang Regency</p>
              <div className="mt-2 flex items-center gap-1">
                <Star className="w-4 h-4 text-yellow-500 fill-current" />
                <span className="text-sm text-gray-600">Potensi Tinggi</span>
              </div>
            </div>
          </div>

          {/* Mock map elements */}
          <div className="absolute top-4 left-4 bg-blue-500 text-white px-2 py-1 rounded text-xs">
            Malang MTV3
          </div>
          <div className="absolute bottom-4 right-4 bg-white px-2 py-1 rounded text-xs shadow">
            Farid olshop
          </div>
          <div className="absolute top-1/2 right-1/3 bg-white px-2 py-1 rounded text-xs shadow">
            Eyelash by Luna
          </div>
        </div>
      </div>

      {/* Business Suggestions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="font-semibold text-gray-800 mb-4">Saran Ide Bisnis dari AI</h3>
        <div className="space-y-4">
          {businessSuggestions.map((suggestion, index) => (
            <div key={index} className="border-l-4 border-green-500 pl-4 py-2">
              <h4 className="font-medium text-gray-800 mb-1">{suggestion.title}</h4>
              <p className="text-sm text-gray-600">{suggestion.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MapSection;