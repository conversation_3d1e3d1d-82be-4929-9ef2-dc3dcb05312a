<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class GoogleAuthController extends Controller
{
    /**
     * Redirect to Google OAuth
     */
    public function redirectToGoogle()
    {
        try {
            // Build Google OAuth URL manually for better control
            $clientId = config('services.google.client_id');
            $redirectUri = config('services.google.redirect');
            $scope = 'openid profile email';
            $state = Str::random(40);

            // Store state in cache for verification (optional)
            cache()->put('google_oauth_state_' . $state, true, 600); // 10 minutes

            $googleUrl = 'https://accounts.google.com/o/oauth2/auth?' . http_build_query([
                'client_id' => $clientId,
                'redirect_uri' => $redirectUri,
                'scope' => $scope,
                'response_type' => 'code',
                'state' => $state,
                'access_type' => 'offline',
                'prompt' => 'consent'
            ]);

            return response()->json([
                'success' => true,
                'redirect_url' => $googleUrl
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to redirect to Google',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle Google OAuth callback
     */
    public function handleGoogleCallback(Request $request)
    {
        try {
            // Verify state parameter (optional security check)
            $state = $request->get('state');
            if ($state && !cache()->has('google_oauth_state_' . $state)) {
                throw new \Exception('Invalid state parameter');
            }

            // Get authorization code
            $code = $request->get('code');
            if (!$code) {
                throw new \Exception('Authorization code not provided');
            }

            // Exchange code for access token
            $tokenResponse = $this->getGoogleAccessToken($code);

            // Get user info from Google
            $googleUser = $this->getGoogleUserInfo($tokenResponse['access_token']);

            // Check if user already exists with this Google ID
            $user = User::where('google_id', $googleUser->id)->first();

            if ($user) {
                // User exists, update their info
                $user->update([
                    'name' => $googleUser->name,
                    'email' => $googleUser->email,
                    'avatar' => $googleUser->avatar,
                ]);
            } else {
                // Check if user exists with same email
                $existingUser = User::where('email', $googleUser->email)->first();

                if ($existingUser) {
                    // Link Google account to existing user
                    $existingUser->update([
                        'google_id' => $googleUser->id,
                        'avatar' => $googleUser->avatar,
                    ]);
                    $user = $existingUser;
                } else {
                    // Create new user
                    $user = User::create([
                        'name' => $googleUser->name,
                        'email' => $googleUser->email,
                        'google_id' => $googleUser->id,
                        'avatar' => $googleUser->avatar,
                        'password' => Hash::make(Str::random(24)), // Random password
                        'role' => 'user',
                        'is_active' => true,
                        'has_subscription' => false,
                        'subscription_status' => null,
                    ]);
                }
            }

            // Create token for API authentication
            $token = $user->createToken('auth-token')->plainTextToken;

            // Return success response with redirect URL for frontend
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5173');
            $redirectUrl = $frontendUrl . '/auth/google/callback?token=' . $token . '&user=' . urlencode(json_encode([
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'is_active' => $user->is_active,
                'avatar' => $user->avatar,
                'has_subscription' => $user->has_subscription,
                'subscription_start' => $user->subscription_start,
                'subscription_end' => $user->subscription_end,
                'subscription_package_id' => $user->subscription_package_id,
                'subscription_status' => $user->subscription_status,
            ]));

            return redirect($redirectUrl);
        } catch (\Exception $e) {
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5173');
            $errorUrl = $frontendUrl . '/auth/google/callback?error=' . urlencode($e->getMessage());
            return redirect($errorUrl);
        }
    }

    /**
     * Handle frontend callback (for SPA)
     */
    public function handleFrontendCallback(Request $request)
    {
        try {
            $token = $request->query('token');
            $error = $request->query('error');

            if ($error) {
                return response()->json([
                    'success' => false,
                    'message' => 'Google authentication failed',
                    'error' => $error
                ], 400);
            }

            if (!$token) {
                return response()->json([
                    'success' => false,
                    'message' => 'No authentication token provided'
                ], 400);
            }

            // Verify token and get user
            $user = Auth::guard('sanctum')->user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid token'
                ], 401);
            }

            return response()->json([
                'success' => true,
                'message' => 'Google authentication successful',
                'token' => $token,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'is_active' => $user->is_active,
                    'avatar' => $user->avatar,
                    'has_subscription' => $user->has_subscription,
                    'subscription_start' => $user->subscription_start,
                    'subscription_end' => $user->subscription_end,
                    'subscription_package_id' => $user->subscription_package_id,
                    'subscription_status' => $user->subscription_status,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication verification failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Exchange authorization code for access token
     */
    private function getGoogleAccessToken($code)
    {
        $response = Http::post('https://oauth2.googleapis.com/token', [
            'client_id' => config('services.google.client_id'),
            'client_secret' => config('services.google.client_secret'),
            'redirect_uri' => config('services.google.redirect'),
            'grant_type' => 'authorization_code',
            'code' => $code,
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to exchange code for token');
        }

        return $response->json();
    }

    /**
     * Get user info from Google using access token
     */
    private function getGoogleUserInfo($accessToken)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
        ])->get('https://www.googleapis.com/oauth2/v2/userinfo');

        if (!$response->successful()) {
            throw new \Exception('Failed to get user info from Google');
        }

        $userData = $response->json();

        // Create a simple object that mimics Socialite user
        return (object) [
            'id' => $userData['id'],
            'name' => $userData['name'],
            'email' => $userData['email'],
            'avatar' => $userData['picture'] ?? null,
        ];
    }
}
