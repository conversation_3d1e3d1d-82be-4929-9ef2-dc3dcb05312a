import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  Megaphone,
  ShoppingCart,
  Wand2,
  Utensils,
  Factory,
  Layers,
  Wrench,
  Plus,
  Pencil,
  Trash2,
  Info,
  Loader2,
  Brain,
  Calculator,
  Play,
  Save,
  Download,
} from 'lucide-react';

// Reusable UI components (defined at module scope to avoid remounting on each render)
const Card: React.FC<{ title?: string; children: React.ReactNode; right?: React.ReactNode }> = ({ title, children, right }) => (
  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 md:p-5">
    <div className="flex items-center justify-between mb-4">
      {title ? <h3 className="font-semibold text-gray-800">{title}</h3> : <div />}
      {right}
    </div>
    {children}
  </div>
);

const Modal: React.FC<{ open: boolean; onClose: () => void; title: string; subtitle?: string; footer?: React.ReactNode; children: React.ReactNode }>
  = ({ open, onClose, title, subtitle, footer, children }) => {
    if (!open) return null;
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        <div className="absolute inset-0 bg-black/40" onClick={onClose} />
        <div className="relative bg-white w-full max-w-lg rounded-xl shadow-lg border border-gray-200 p-5">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
            {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
          </div>
          <div className="space-y-3">{children}</div>
          <div className="mt-6 flex items-center justify-end gap-3">{footer}</div>
        </div>
      </div>
    );
  };


// Reusable drag-and-drop file input
const FileDropzone: React.FC<{ value: File | null; onChange: (f: File | null) => void; accept?: string }>
  = ({ value, onChange, accept = 'image/*' }) => {
    const inputRef = useRef<HTMLInputElement | null>(null);
    const [dragOver, setDragOver] = useState(false);
    return (
      <div
        onDragOver={(e) => { e.preventDefault(); setDragOver(true); }}
        onDragLeave={() => setDragOver(false)}
        onDrop={(e) => { e.preventDefault(); setDragOver(false); const f = e.dataTransfer.files?.[0]; if (f) onChange(f); }}
        onClick={() => inputRef.current?.click()}
        className={`w-full rounded-lg border-2 border-dashed ${dragOver ? 'border-green-400 bg-green-50/40' : 'border-gray-300 bg-white'} px-4 py-6 text-center cursor-pointer`}
      >
        <div className="text-sm text-gray-600">
          {value ? (<span className="font-medium text-gray-800">{value.name}</span>) : 'Drag & drop gambar ke sini, atau klik untuk memilih'}
        </div>
        <input ref={inputRef} type="file" accept={accept} className="hidden" onChange={(e) => onChange(e.target.files?.[0] || null)} />
      </div>
    );
  };


// Helper formatters
const formatRp = (n: number | string) => {
  const num = typeof n === 'string' ? Number(n || 0) : n;
  return new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', maximumFractionDigits: 0 }).format(num || 0);
};

const parseNumber = (v: string) => {
  const n = Number((v || '').toString().replace(/[^0-9.\-]/g, ''));
  return isNaN(n) ? 0 : n;
};

// Types
type ModelBisnis =
  | 'iklan-cod'
  | 'marketplace'
  | 'analisis-cepat'
  | 'ritel-fnb'
  | 'manufaktur'
  | 'produksi-turunan'
  | 'produk-jasa';

const modelItems: { id: ModelBisnis; label: string; icon: React.ElementType }[] = [
  { id: 'iklan-cod', label: 'Iklan & COD', icon: Megaphone },
  { id: 'marketplace', label: 'Marketplace', icon: ShoppingCart },
  { id: 'analisis-cepat', label: 'Analisis Cepat', icon: Wand2 },
  { id: 'ritel-fnb', label: 'Bisnis Ritel/F&B', icon: Utensils },
  { id: 'manufaktur', label: 'Manufaktur/Pabrik', icon: Factory },
  { id: 'produksi-turunan', label: 'Produksi Turunan', icon: Layers },
  { id: 'produk-jasa', label: 'Produk Jasa', icon: Wrench },
];

// Backend API base URL
const API_BASE = (import.meta as any).env?.VITE_BACKEND_URL || 'http://localhost:8000';

type Worksheet = { id: number; name: string };


export default function HppCalculatorPage() {
  // Worksheets via API
  const [worksheets, setWorksheets] = useState<Worksheet[]>([]);
  const [selectedWorksheetId, setSelectedWorksheetId] = useState<number | null>(null);

  useEffect(() => {
    (async () => {
      try {
        const res = await fetch(`${API_BASE}/api/worksheets`);
        const data = await res.json();
        if (Array.isArray(data)) {
          setWorksheets(data);
          if (!selectedWorksheetId && data.length) setSelectedWorksheetId(data[0].id);
        }
      } catch (e) {
        console.error('Gagal memuat worksheets', e);
      }
    })();
  }, []);

  const currentWorksheet = useMemo(() => {
    return worksheets.find((w) => w.id === selectedWorksheetId) || null;
  }, [worksheets, selectedWorksheetId]);

  // Modal states
  const [showAddEdit, setShowAddEdit] = useState<null | { mode: 'add' | 'edit'; value: string }>(
    null
  );
  const [showDelete, setShowDelete] = useState<null | number>(null);

  // Model & results
  const [model, setModel] = useState<ModelBisnis>('iklan-cod');
  const [result, setResult] = useState<any>(null);

  // ----- IKLAN & COD -----
  type ProductRow = { id: string; nama: string; hpp: string; harga: string; qty: string };
  const [adProducts, setAdProducts] = useState<ProductRow[]>([
    { id: crypto.randomUUID(), nama: '', hpp: '', harga: '', qty: '' },
  ]);
  const [adTotalIklan, setAdTotalIklan] = useState('');
  const [adAdminPct, setAdAdminPct] = useState('');

  // AI suggestions state for Iklan & COD
  const [adAiLoading, setAdAiLoading] = useState(false);
  const [adAiError, setAdAiError] = useState<string | null>(null);
  const [adAiSaran, setAdAiSaran] = useState<string[]>([]);

  const [adReturnPct, setAdReturnPct] = useState('');

  const adSummary = useMemo(() => {
    const items = adProducts.map((p) => ({
      nama: p.nama || 'Produk',
      hpp: parseNumber(p.hpp),
      harga: parseNumber(p.harga),
      qty: parseNumber(p.qty),
    }));
    const totalQty = items.reduce((s, i) => s + i.qty, 0);
    const totalRevenue = items.reduce((s, i) => s + i.harga * i.qty, 0);
    const totalHpp = items.reduce((s, i) => s + i.hpp * i.qty, 0);
    const biayaIklan = parseNumber(adTotalIklan);
    const admin = (parseNumber(adAdminPct) / 100) * totalRevenue;
    const retur = (parseNumber(adReturnPct) / 100) * totalRevenue;
    const profit = totalRevenue - totalHpp - biayaIklan - admin - retur;



    const margin = totalRevenue > 0 ? (profit / totalRevenue) * 100 : 0;
    const labaPerUnit = totalQty > 0 ? profit / totalQty : 0;
    const roas = biayaIklan > 0 ? totalRevenue / biayaIklan : null;
    const cpr = totalQty > 0 ? (biayaIklan / totalQty) : null;
    return { totalQty, totalRevenue, totalHpp, biayaIklan, admin, retur, profit, margin, labaPerUnit, roas, cpr };
  }, [adProducts, adTotalIklan, adAdminPct, adReturnPct]);

  // ----- MARKETPLACE -----
  const [mpHppPerProduk, setMpHppPerProduk] = useState('');
  const [mpHargaJual, setMpHargaJual] = useState('');
  const [mpPlatform, setMpPlatform] = useState<'lainnya' | 'shopee' | 'tiktok'>('lainnya');
  const [mpKomisiPct, setMpKomisiPct] = useState('');
  const [mpLayananPct, setMpLayananPct] = useState('');
  const [mpProgramRp, setMpProgramRp] = useState('');
  const [mpAmsPct, setMpAmsPct] = useState('');

  // HPP Calculator modal
  const [showHppCalc, setShowHppCalc] = useState(false);
  const [calc, setCalc] = useState({
    bahan: '', tenaga: '', overhead: '', kemasan: '', logistik: '', tambahan: '', jumlah: '1',
  });
  const [helpOpen, setHelpOpen] = useState<Record<string, boolean>>({});
  const [calcResult, setCalcResult] = useState<{ total: number; perProduk: number } | null>(null);

  const applyHppCalc = () => {
    const total =
      parseNumber(calc.bahan) +
      parseNumber(calc.tenaga) +
      parseNumber(calc.overhead) +
      parseNumber(calc.kemasan) +
      parseNumber(calc.logistik) +
      parseNumber(calc.tambahan);
    const qty = Math.max(1, parseNumber(calc.jumlah));
    const perProduk = Math.floor(total / qty);
    setCalcResult({ total, perProduk });
  };


  const requestAdAi = async () => {
    try {
      setAdAiError(null);
      setAdAiLoading(true);
      setAdAiSaran([]);
      const items = adProducts.map(p => ({
        nama: p.nama || 'Produk',
        hpp: parseNumber(p.hpp),
        harga: parseNumber(p.harga),
        qty: parseNumber(p.qty),
      }));
      const payload = {
        items,
        biayaIklan: parseNumber(adTotalIklan),
        adminPct: parseNumber(adAdminPct),
        returnPct: parseNumber(adReturnPct),
      };
      const res = await fetch(`${API_BASE}/api/ads/suggestions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal mengambil saran AI');
      const saran = Array.isArray(data?.saran) ? data.saran : [];
      setAdAiSaran(saran);
    } catch (e: any) {
      setAdAiError(e?.message || 'Terjadi kesalahan');
    } finally {
      setAdAiLoading(false);
    }
  };

  const mpSummary = useMemo(() => {
    const hpp = parseNumber(mpHppPerProduk);



    const harga = parseNumber(mpHargaJual);
    const komisi = (parseNumber(mpKomisiPct) / 100) * harga;
    const layanan = (parseNumber(mpLayananPct) / 100) * harga;
    const program = parseNumber(mpProgramRp);
    const ams = (parseNumber(mpAmsPct) / 100) * harga;
    const prosesPesanan = mpPlatform === 'shopee' ? 1250 : 0;
    const potongan = komisi + layanan + program + ams + prosesPesanan;
    const biaya = hpp + potongan;
    const profit = harga - biaya;
    const margin = harga > 0 ? (profit / harga) * 100 : 0;
    return { harga, hpp, komisi, layanan, program, ams, prosesPesanan, potongan, biaya, profit, margin };
  }, [mpHppPerProduk, mpHargaJual, mpKomisiPct, mpLayananPct, mpProgramRp, mpAmsPct, mpPlatform]);

  // ----- ANALISIS CEPAT -----
  const [qcNama, setQcNama] = useState('');
  const [qcHarga, setQcHarga] = useState('');
  const [qcImg, setQcImg] = useState<File | null>(null);
  const [qcLoading, setQcLoading] = useState(false);
  const [qcError, setQcError] = useState<string | null>(null);

  const runQuickAnalysis = async () => {
    try {
      setQcError(null);
      setQcLoading(true);
      const harga = parseNumber(qcHarga);
      const res = await fetch(`${API_BASE}/api/quick/estimate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ nama: qcNama || 'Produk', harga }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal mengambil estimasi AI');
      setResult({
        type: 'analisis-cepat',
        estimasi_hpp: data.estimasi_hpp,
        potensi_laba: data.potensi_laba,
        margin: typeof data.margin_pct === 'number' ? data.margin_pct : 0,
        detail: {
          komponen_biaya: Array.isArray(data.komponen_biaya) ? data.komponen_biaya : [],
          saran_ai: Array.isArray(data.saran_ai) ? data.saran_ai : [],
        },
      });
    } catch (e: any) {
      setQcError(e?.message || 'Terjadi kesalahan');
    } finally {
      setQcLoading(false);
    }
  };


  // ----- RITEL / F&B -----
  type Unit = 'g' | 'kg' | 'ml' | 'l' | 'pcs' | 'buah' | 'lembar';
  type VarCost = {
    id: string;
    bahan: string;
    pakaiQty: string;
    pakaiUnit: Unit;
    totalHarga: string;
    beliQty: string;
    beliUnit: Unit;
  };
  const [vfNama, setVfNama] = useState('');
  const [vfKategori, setVfKategori] = useState('fashion');
  const [vfImg, setVfImg] = useState<File | null>(null);
  const [vfVarCosts, setVfVarCosts] = useState<VarCost[]>([
    { id: crypto.randomUUID(), bahan: '', pakaiQty: '', pakaiUnit: 'g', totalHarga: '', beliQty: '', beliUnit: 'kg' }
  ]);
  const [vfTarget, setVfTarget] = useState('100');
  type FixCost = { id: string; nama: string; totalBulan: string; alokasi?: string };
  const [vfFixCosts, setVfFixCosts] = useState<FixCost[]>([{ id: crypto.randomUUID(), nama: '', totalBulan: '', alokasi: '' }]);
  const [vfAiLoading, setVfAiLoading] = useState(false);
  const [vfAiError, setVfAiError] = useState<string | null>(null);

  const [showPresetModal, setShowPresetModal] = useState(false);
  const [presetName, setPresetName] = useState('');
  type FixPreset = { id: number; name: string; fix: FixCost[] };
  const [presets, setPresets] = useState<FixPreset[]>([]);
  const [vfTargetProfit, setVfTargetProfit] = useState('');
  const [vfChosenPrice, setVfChosenPrice] = useState('');
  const [vfPriceSuggestLoading, setVfPriceSuggestLoading] = useState(false);
  const [vfPriceSuggests, setVfPriceSuggests] = useState<Array<{ level: string; harga: number; profit_per_unit: number; margin_pct: number; catatan: string }>>([]);
  // Reset semua input & hasil ketika pindah/menambah Worksheet baru
  const resetWorksheetData = () => {
    setResult(null);
    // Ritel/F&B
    setVfNama('');
    setVfKategori('fashion');
    setVfImg(null);
    setVfVarCosts([{ id: crypto.randomUUID(), bahan: '', pakaiQty: '', pakaiUnit: 'g', totalHarga: '', beliQty: '', beliUnit: 'kg' }]);
    setVfFixCosts([{ id: crypto.randomUUID(), nama: '', totalBulan: '', alokasi: '' }]);
    setVfTarget('100');
    setVfTargetProfit('');
    setVfChosenPrice('');
    setVfPriceSuggests([]);
    setVfAiError(null);
    setVfAiLoading(false);
  };


  // Riwayat simpan perhitungan (ritel/fnb)
  const [showHistory, setShowHistory] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);
  type HppRecord = { id: number; worksheet_id: number; model: string; title: string; data: any; created_at: string };
  const [historyItems, setHistoryItems] = useState<HppRecord[]>([]);

  const loadHistory = async (m?: string) => {
    try {
      setHistoryLoading(true);
      if (!selectedWorksheetId) { setHistoryItems([]); return; }
      const which = m ?? (model === 'manufaktur' ? 'manufaktur' : 'ritel-fnb');
      const res = await fetch(`${API_BASE}/api/hpp-records?model=${which}&worksheet_id=${selectedWorksheetId}`);
      const data = await res.json();
      if (Array.isArray(data)) setHistoryItems(data);
    } catch (e) {
      console.error(e);
    } finally {
      setHistoryLoading(false);
    }
  };

  const applyHistory = (rec: HppRecord) => {
    try {
      const d = rec.data || {};
      if (rec.model === 'manufaktur') {
        const i = d.inputs || {};
        setMfNama(i.mfNama || '');
        setMfKategori(i.mfKategori || 'fashion');
        setMfOutput(i.mfOutput || '');
        setMfChosenPrice(i.mfChosenPrice || '');
        setMfTargetProfit(i.mfTargetProfit || '');
        if (Array.isArray(d.mfBahan)) setMfBahan(d.mfBahan.map((x: any) => ({ ...x, id: crypto.randomUUID() })));
        if (Array.isArray(d.mfTkl)) setMfTkl(d.mfTkl.map((x: any) => ({ ...x, id: crypto.randomUUID() })));
        if (Array.isArray(d.mfOhp)) setMfOhp(d.mfOhp.map((x: any) => ({ ...x, id: crypto.randomUUID() })));
        setMfPriceSuggests(Array.isArray(d.mfPriceSuggests) ? d.mfPriceSuggests : []);
        setResult({ type: 'manufaktur', hpp: (d.summary?.hpp ?? mfSummary.hpp) });
      } else if (rec.model === 'produk-jasa') {
        const i = d.inputs || {};
        setPjNama(i.pjNama || '');
        setPjKategori(i.pjKategori || 'jasa-profesional');
        setPjTarget(i.pjTarget || '');
        setPjChosenPrice(i.pjChosenPrice || '');
        setPjTargetProfit(i.pjTargetProfit || '');
        if (Array.isArray(d.pjDirect)) setPjDirect(d.pjDirect.map((x: any) => ({ ...x, id: crypto.randomUUID() })));
        if (Array.isArray(d.pjOpex)) setPjOpex(d.pjOpex.map((x: any) => ({ ...x, id: crypto.randomUUID() })));
        setPjPriceSuggests(Array.isArray(d.pjPriceSuggests) ? d.pjPriceSuggests : []);
        setResult({ type: 'produk-jasa', hpp: (d.summary?.hpp ?? pjSummary.hpp) });
      } else if (rec.model === 'produksi-turunan') {
        const i = d.inputs || {};
        setPtNama(i.ptNama || '');
        setPtBatch(i.ptBatch || '1');
        if (Array.isArray(d.ptBahan)) setPtBahan(d.ptBahan.map((x: any) => ({ ...x, id: crypto.randomUUID() })));
        if (Array.isArray(d.ptOlah)) setPtOlah(d.ptOlah.map((x: any) => ({ ...x, id: crypto.randomUUID() })));
        if (Array.isArray(d.ptProduk)) setPtProduk(d.ptProduk.map((x: any) => ({ ...x, id: crypto.randomUUID() })));
        setResult({ type: 'produksi-turunan' } as any);
      } else {
        if (d.inputs) {
          const i = d.inputs;
          setVfNama(i.vfNama || '');
          setVfKategori(i.vfKategori || 'fnb');
          setVfTarget(i.vfTarget || '');
          setVfChosenPrice(i.vfChosenPrice || '');
          setVfTargetProfit(i.vfTargetProfit || '');
        }
        if (Array.isArray(d.vfVarCosts)) setVfVarCosts(d.vfVarCosts.map((x: any) => ({ ...x, id: crypto.randomUUID() })));
        if (Array.isArray(d.vfFixCosts)) setVfFixCosts(d.vfFixCosts.map((x: any) => ({ ...x, id: crypto.randomUUID() })));
        setVfPriceSuggests(Array.isArray(d.vfPriceSuggests) ? d.vfPriceSuggests : []);
        setResult({ type: 'ritel-fnb', hpp: (d.summary?.hpp ?? vfSummary.hpp) });
      }
      setShowHistory(false);
    } catch (e) {
      console.error(e);
      alert('Gagal menerapkan riwayat');
    }
  };

  const deleteHistory = async (id: number) => {
    try {
      await fetch(`${API_BASE}/api/hpp-records/${id}`, { method: 'DELETE' });
      setHistoryItems((items) => items.filter((x) => x.id !== id));
    } catch (e) {
      console.error(e);
      alert('Gagal menghapus riwayat');
    }
  };

  // Simpan Perhitungan
  const [showSaveCalc, setShowSaveCalc] = useState(false);
  const [saveCalcName, setSaveCalcName] = useState('');
  const saveCalculation = async () => {
    try {
      if (!selectedWorksheetId) { alert('Pilih worksheet dahulu sebelum menyimpan.'); return; }
      const isMf = result?.type === 'manufaktur' || model === 'manufaktur';
      const isPj = result?.type === 'produk-jasa' || model === 'produk-jasa';
      const isPt = result?.type === 'produksi-turunan' || model === 'produksi-turunan';

      let payload: any;
      if (isPj) {
        // Produk Jasa
        const varUnit = pjSummary.directPerLayanan;
        const harga = parseNumber(pjChosenPrice);
        const unitProfit = Math.max(0, harga - varUnit);
        const margin_pct = harga > 0 ? (unitProfit / harga) * 100 : 0;
        const price = { harga_pilihan: harga, profit_per_unit: unitProfit, margin_pct: Math.round(margin_pct * 10) / 10 };
        payload = {
          worksheet_id: selectedWorksheetId,
          model: 'produk-jasa',
          title: saveCalcName || `Perhitungan ${new Date().toLocaleString()}`,
          data: {
            inputs: { pjNama, pjKategori, pjTarget, pjChosenPrice, pjTargetProfit },
            pjDirect,
            pjOpex,
            summary: { directPerLayanan: pjSummary.directPerLayanan, monthlyOpex: pjSummary.monthlyOpex, total: pjSummary.total, target: pjSummary.target, hpp: pjSummary.hpp },
            price,
            pjPriceSuggests,
            projection: pjProjection ? {
              target_laba_bulan: parseNumber(pjTargetProfit),
              qty_day: pjProjection.qtyDayBase,
              qty_month: pjProjection.qtyMonth,
              omset: pjProjection.omset,
              biaya_produksi: pjProjection.biayaProduksi,
              fix_month: pjProjection.fixMonth,
              laba_bersih: pjProjection.laba,
            } : undefined,
          },
        };
      } else if (isMf) {
        // Manufaktur/Pabrik
        const varUnit = Math.round((mfSummary.totalBahan + mfSummary.totalTkl) / Math.max(1, mfSummary.target));
        const harga = parseNumber(mfChosenPrice);
        const unitProfit = Math.max(0, harga - varUnit);
        const margin_pct = harga > 0 ? (unitProfit / harga) * 100 : 0;
        const price = { harga_pilihan: harga, profit_per_unit: unitProfit, margin_pct: Math.round(margin_pct * 10) / 10 };
        payload = {
          worksheet_id: selectedWorksheetId,
          model: 'manufaktur',
          title: saveCalcName || `Perhitungan ${new Date().toLocaleString()}`,
          data: {
            inputs: { mfNama, mfKategori, mfOutput, mfChosenPrice, mfTargetProfit },
            mfBahan,
            mfTkl,
            mfOhp,
            summary: { totalBahan: mfSummary.totalBahan, totalTkl: mfSummary.totalTkl, totalOhp: mfSummary.totalOhp, total: mfSummary.total, target: mfSummary.target, hpp: mfSummary.hpp },
            price,
            mfPriceSuggests,
            projection: mfProjection ? {
              target_laba_bulan: parseNumber(mfTargetProfit),
              qty_day: mfProjection.qtyDayBase,
              qty_month: mfProjection.qtyMonth,
              omset: mfProjection.omset,
              biaya_produksi: mfProjection.biayaProduksi,
              fix_month: mfProjection.fixMonth,
              laba_bersih: mfProjection.laba,
            } : undefined,
          },
        };
      } else if (isPt) {
        // Produksi Turunan
        const batch = Math.max(1, parseNumber(ptBatch));
        const bahanPerBatch = ptBahan.reduce((s, b) => s + (parseNumber(b.biaya) / batch), 0);
        const olahPerBatch = ptOlah.reduce((s, o) => {
          const h = parseNumber(o.harga);
          if (o.periode === 'per-batch') return s + h;
          if (o.periode === 'per-bulan') return s + h / batch;
          if (o.periode === 'per-minggu') return s + (h * 4) / batch;
          if (o.periode === 'per-hari') return s + (h * 30) / batch;
          return s;
        }, 0);
        const biayaPerBatch = Math.round(bahanPerBatch + olahPerBatch);
        const qtyPerBatchTotal = ptProduk.reduce((s, p) => s + (parseNumber(p.qty) / batch), 0) || 1;
        const hppUnitBatch = Math.round(biayaPerBatch / qtyPerBatchTotal);
        const revenuePerBatch = ptProduk.reduce((s, p) => s + (parseNumber(p.hargaJual) * (parseNumber(p.qty) / batch)), 0);
        const profitPerBatch = Math.round(revenuePerBatch - biayaPerBatch);
        const marginPct = revenuePerBatch > 0 ? (profitPerBatch / revenuePerBatch) * 100 : 0;

        payload = {
          worksheet_id: selectedWorksheetId,
          model: 'produksi-turunan',
          title: saveCalcName || `Perhitungan ${new Date().toLocaleString()}`,
          data: {
            inputs: { ptNama, ptBatch },
            ptBahan,
            ptOlah,
            ptProduk,
            summary: {
              batch,
              biayaPerBatch,
              qtyPerBatchTotal,
              hppUnitBatch,
              revenuePerBatch: Math.round(revenuePerBatch),
              profitPerBatch,
              marginPct: Math.round(marginPct * 10) / 10,
            },
          },
        };
      } else {
        // Ritel/F&B
        const price = (() => {
          const harga = parseNumber(vfChosenPrice);
          const unitProfit = Math.max(0, harga - vfSummary.totalVar);
          const margin_pct = harga > 0 ? (unitProfit / harga) * 100 : 0;
          return { harga_pilihan: harga, profit_per_unit: unitProfit, margin_pct: Math.round(margin_pct * 10) / 10 };
        })();
        payload = {
          worksheet_id: selectedWorksheetId,
          model: 'ritel-fnb',
          title: saveCalcName || `Perhitungan ${new Date().toLocaleString()}`,
          data: {
            inputs: { vfNama, vfKategori, vfTarget, vfChosenPrice, vfTargetProfit },
            vfVarCosts,
            vfFixCosts,
            summary: { totalVar: vfSummary.totalVar, allocFix: vfSummary.allocFix, totalFix: vfSummary.totalFix, hpp: vfSummary.hpp },
            price,
            vfPriceSuggests,
            projection: vfProjection ? {
              target_laba_bulan: parseNumber(vfTargetProfit),
              qty_day: vfProjection.qtyDayBase,
              qty_month: vfProjection.qtyMonth,
              omset: vfProjection.omset,
              biaya_produksi: vfProjection.biayaProduksi,
              fix_month: vfProjection.fixMonth,
              laba_bersih: vfProjection.laba,
            } : undefined,
          },
        };
      }

      const res = await fetch(`${API_BASE}/api/hpp-records`, {
        method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal menyimpan perhitungan');
      setShowSaveCalc(false);
      setSaveCalcName('');
    } catch (e) {
      console.error(e);
      alert('Gagal menyimpan perhitungan');
    }
  };

  useEffect(() => {
    (async () => {
      try {
        const res = await fetch(`${API_BASE}/api/fix-presets`);
        const data = await res.json();
        if (Array.isArray(data)) setPresets(data);
      } catch (e) {
        console.error('Gagal memuat fix-presets', e);
      }
    })();
  }, []);

  // Helpers untuk konversi satuan dan hitung biaya per produk
  const unitGroup = (u: Unit) => (u === 'g' || u === 'kg') ? 'weight' : (u === 'ml' || u === 'l') ? 'volume' : 'count';
  const toBase = (qty: number, u: Unit) => {
    switch (u) {
      case 'kg': return qty * 1000; // ke gram
      case 'g': return qty;
      case 'l': return qty * 1000; // ke ml
      case 'ml': return qty;
      case 'pcs':
      case 'buah':
      case 'lembar':
      default: return qty; // satuan hitung
    }
  };
  const varCostPerProduk = (v: VarCost): number => {
    const totalHarga = parseNumber(v.totalHarga);
    const beliQty = parseNumber(v.beliQty);
    const pakaiQty = parseNumber(v.pakaiQty);
    if (totalHarga <= 0 || beliQty <= 0 || pakaiQty <= 0) return 0;
    const gBuy = unitGroup(v.beliUnit);
    const gUse = unitGroup(v.pakaiUnit);
    if (gBuy !== gUse) return 0; // tidak sebanding
    const baseBeli = toBase(beliQty, v.beliUnit);
    const basePakai = toBase(pakaiQty, v.pakaiUnit);
    if (baseBeli <= 0) return 0;

    const hargaPerBase = totalHarga / baseBeli;
    return Math.round(hargaPerBase * basePakai);
  };

  const savePreset = async () => {
    try {
      const name = presetName || `Preset ${new Date().toLocaleString()}`;
      const res = await fetch(`${API_BASE}/api/fix-presets`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, fix: vfFixCosts }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal menyimpan preset');
      setPresets((p) => [data, ...p]);
      setShowPresetModal(false);
      setPresetName('');
    } catch (e) {
      console.error(e);
    }
  };

  const applyPreset = (p: { id: number; name: string; fix: FixCost[] }) => {
    setVfFixCosts(p.fix.map((x) => ({ ...x, id: crypto.randomUUID() })));
  };

  const vfSummary = useMemo(() => {
    const totalVar = vfVarCosts.reduce((s, v) => s + varCostPerProduk(v), 0);
    const totalFix = vfFixCosts.reduce((s, f) => s + parseNumber(f.totalBulan), 0);
    // HPP per produk menggunakan TOTAL alokasi manual per produk (tanpa fallback)
    const allocFix = vfFixCosts.reduce((s, f) => s + parseNumber(f.alokasi || ''), 0);
    const hpp = totalVar + allocFix;
    return { totalVar, totalFix, allocFix, hpp };
  }, [vfVarCosts, vfFixCosts]);


  const runVfAiAnalysis = async () => {
    try {
      setVfAiError(null);
      setVfAiLoading(true);
      const res = await fetch(`${API_BASE}/api/ritel/estimate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ nama: vfNama || 'Produk', kategori: vfKategori || 'fnb' }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal memproses analisis AI');

      const units: Unit[] = ['g', 'kg', 'ml', 'l', 'pcs', 'buah', 'lembar'];
      const toUnit = (u: any): Unit => (units.includes(u as Unit) ? (u as Unit) : 'g');

      const vc = Array.isArray(data?.var_costs) ? data.var_costs.map((v: any) => ({
        id: crypto.randomUUID(),
        bahan: String(v?.bahan ?? ''),
        pakaiQty: String(v?.pakai_qty ?? ''),
        pakaiUnit: toUnit(v?.pakai_unit),
        totalHarga: String(v?.total_harga ?? ''),
        beliQty: String(v?.beli_qty ?? ''),
        beliUnit: toUnit(v?.beli_unit),
      })) : [];

      const fc = Array.isArray(data?.fix_costs) ? data.fix_costs.map((f: any) => ({
        id: crypto.randomUUID(),
        nama: String(f?.nama ?? ''),
        totalBulan: String(f?.total_bulan ?? ''),
        // Sesuai permintaan: jangan auto-isi alokasi per produk dari AI
        alokasi: '',
      })) : [];

      if (vc.length === 0 && fc.length === 0) throw new Error('AI tidak mengembalikan data biaya');

      setVfVarCosts((prev) => [...prev, ...vc]);
      setVfFixCosts((prev) => [...prev, ...fc]);
    } catch (e: any) {
      setVfAiError(e?.message || 'Terjadi kesalahan tak terduga');
    } finally {
      setVfAiLoading(false);
    }
  };


  const requestVfPriceSuggest = async () => {
    try {
      setVfPriceSuggestLoading(true);
      setVfPriceSuggests([]);
      const res = await fetch(`${API_BASE}/api/price/suggest`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ nama: vfNama || 'Produk', kategori: vfKategori || 'umum', hpp: vfSummary.hpp }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal meminta saran harga');
      const items = Array.isArray(data?.saran) ? data.saran : [];
      setVfPriceSuggests(items);
    } catch (e) {
      console.error(e);
    } finally {
      setVfPriceSuggestLoading(false);
    }
  };

  const requestMfPriceSuggest = async () => {
    try {
      setMfPriceSuggestLoading(true);
      setMfPriceSuggests([]);
      const res = await fetch(`${API_BASE}/api/price/suggest`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ nama: mfNama || 'Produk', kategori: mfKategori || 'umum', hpp: mfSummary.hpp }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal meminta saran harga');
      const items = Array.isArray(data?.saran) ? data.saran : [];
      setMfPriceSuggests(items);
    } catch (e) {
      console.error(e);
    } finally {
      setMfPriceSuggestLoading(false);
    }
  };

  const exportVfXlsx = async () => {
    try {
      // Siapkan payload export berdasarkan state saat ini
      const var_costs = vfVarCosts.map((v) => ({
        bahan: v.bahan,
        pakai_qty: parseNumber(v.pakaiQty),
        pakai_unit: v.pakaiUnit,
        biaya_per_produk: varCostPerProduk(v),
        total_harga: parseNumber(v.totalHarga),
        beli_qty: parseNumber(v.beliQty),
        beli_unit: v.beliUnit,
      }));
      const fix_allocs = vfFixCosts.map((f) => ({
        nama: f.nama,
        alokasi: parseNumber(f.alokasi || ''),
      }));
      const ringkasan = {
        total_var: vfSummary.totalVar,
        total_alloc: vfSummary.allocFix,
        hpp: vfSummary.hpp,
      };


      const price = (() => {
        const harga = parseNumber(vfChosenPrice);
        const unitProfit = Math.max(0, harga - vfSummary.totalVar);
        const margin_pct = harga > 0 ? (unitProfit / harga) * 100 : 0;
        return { harga_pilihan: harga, profit_per_unit: unitProfit, margin_pct: Math.round(margin_pct * 10) / 10 };
      })();
      const price_suggestions = vfPriceSuggests;
      const projection = vfProjection ? {
        target_laba_bulan: parseNumber(vfTargetProfit),
        qty_day: vfProjection.qtyDayBase,
        qty_month: vfProjection.qtyMonth,
        omset: vfProjection.omset,
        biaya_produksi: vfProjection.biayaProduksi,
        fix_month: vfProjection.fixMonth,
        laba_bersih: vfProjection.laba,
      } : undefined;

      const payload: any = {
        produk: { nama: vfNama || 'Produk' },
        var_costs,
        fix_allocs,
        ringkasan,
        price,
        price_suggestions,
      };
      if (projection) payload.projection = projection;

      const res = await fetch(`${API_BASE}/api/ritel/export-xlsx`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const text = await res.text();
        throw new Error(text || 'Gagal mengekspor XLSX');
      }
      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      const safeName = (vfNama || 'Produk').replace(/[^A-Za-z0-9_\-]+/g, '_');
      a.href = url;
      a.download = `HPP_${safeName}_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (e) {
      console.error(e);
      alert('Gagal mengekspor file .xlsx');
    }
  };


  const exportPjXlsx = async () => {
    try {
      const target = Math.max(1, pjSummary.target);
      const direct = pjDirect.map((d) => ({ nama: d.nama, harga: parseNumber(d.harga) }));
      const opex = pjOpex.map((o) => {
        const b = parseNumber(o.biaya);
        const monthly = o.periode === 'per-bulan' ? b : (o.periode === 'per-minggu' ? b * 4 : b * 30);
        return { nama: o.nama, total_bulan: monthly, alokasi_per_layanan: Math.round(monthly / target) };
      });
      const varUnit = pjSummary.directPerLayanan;
      const harga = parseNumber(pjChosenPrice);
      const unitProfit = Math.max(0, harga - varUnit);
      const margin_pct = harga > 0 ? (unitProfit / harga) * 100 : 0;
      const price = { harga_pilihan: harga, profit_per_unit: unitProfit, margin_pct: Math.round(margin_pct * 10) / 10 };

      const payload = {
        layanan: { nama: pjNama || 'Layanan' },
        direct,
        opex,
        ringkasan: {
          direct_per_layanan: pjSummary.directPerLayanan,
          opex_bulan: pjSummary.monthlyOpex,
          target_layanan: pjSummary.target,
          hpp_per_layanan: pjSummary.hpp,
        },
        price,
        price_suggestions: pjPriceSuggests,
        projection: pjProjection ? {
          target_laba_bulan: parseNumber(pjTargetProfit),
          qty_day: pjProjection.qtyDayBase,
          qty_month: pjProjection.qtyMonth,
          omset: pjProjection.omset,
          biaya_produksi: pjProjection.biayaProduksi,
          fix_month: pjProjection.fixMonth,
          laba_bersih: pjProjection.laba,
        } : undefined,
      };

      const res = await fetch(`${API_BASE}/api/jasa/export-xlsx`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        throw new Error(data?.error || 'Gagal mengekspor file .xlsx');
      }
      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `HPP_${(pjNama || 'Layanan').replace(/[^A-Za-z0-9_\-]+/g, '_')}_${new Date().toISOString().slice(0, 10)}.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (e) {
      console.error(e);
      alert('Gagal mengekspor file .xlsx');
    }
  };


  const exportPtXlsx = async () => {
    try {
      const batch = Math.max(1, parseNumber(ptBatch));
      const bahanPerBatch = ptBahan.reduce((s, b) => s + (parseNumber(b.biaya) / batch), 0);
      const olahPerBatch = ptOlah.reduce((s, o) => {
        const h = parseNumber(o.harga);
        if (o.periode === 'per-batch') return s + h;
        if (o.periode === 'per-bulan') return s + h / batch;
        if (o.periode === 'per-minggu') return s + (h * 4) / batch;
        if (o.periode === 'per-hari') return s + (h * 30) / batch;
        return s;
      }, 0);
      const biayaPerBatch = Math.round(bahanPerBatch + olahPerBatch);
      const qtyPerBatchTotal = ptProduk.reduce((s, p) => s + (parseNumber(p.qty) / batch), 0) || 1;
      const hppUnitBatch = Math.round(biayaPerBatch / qtyPerBatchTotal);
      const revenuePerBatch = ptProduk.reduce((s, p) => s + (parseNumber(p.hargaJual) * (parseNumber(p.qty) / batch)), 0);
      const profitPerBatch = Math.round(revenuePerBatch - biayaPerBatch);
      const marginPct = revenuePerBatch > 0 ? (profitPerBatch / revenuePerBatch) * 100 : 0;

      const rincianBahan = ptBahan.map((b) => ({
        nama: b.nama,
        biaya_per_batch: Math.round(parseNumber(b.biaya) / batch),
        jumlah: parseNumber(b.jumlah),
        satuan: b.satuan,
      }));
      const rincianOlah = ptOlah.map((o) => {
        const h = parseNumber(o.harga);
        const perBatch = o.periode === 'per-batch' ? h : o.periode === 'per-bulan' ? h / batch : o.periode === 'per-minggu' ? (h * 4) / batch : (h * 30) / batch;
        return { nama: o.nama, biaya_per_batch: Math.round(perBatch), periode: o.periode };
      });

      const produkTurunan = ptProduk.map((p) => {
        const qtyBatch = parseNumber(p.qty) / batch;
        const hppTotal = Math.round(hppUnitBatch * qtyBatch);
        const revenue = Math.round(parseNumber(p.hargaJual) * qtyBatch);
        const profit = revenue - hppTotal;
        const margin = revenue > 0 ? (profit / revenue) * 100 : 0;
        return {
          nama: p.nama,
          qty_batch: qtyBatch,
          satuan: p.satuan,
          harga_jual: parseNumber(p.hargaJual),
          hpp_unit_batch: hppUnitBatch,
          hpp_total_batch: hppTotal,
          revenue_batch: revenue,
          profit_batch: profit,
          margin_pct: Math.round(margin * 10) / 10,
        };
      });

      const payload = {
        produk_utama: { nama: ptNama || 'Produk turunan' },
        ringkasan: {
          batch_per_bulan: batch,
          biaya_bahan_bulan: ptBahan.reduce((s, b) => s + parseNumber(b.biaya), 0),
          biaya_olah_bulan: ptOlah.reduce((s, o) => s + (o.periode === 'per-batch' ? parseNumber(o.harga) * batch : o.periode === 'per-bulan' ? parseNumber(o.harga) : o.periode === 'per-minggu' ? parseNumber(o.harga) * 4 : parseNumber(o.harga) * 30), 0),
          total_biaya_bulan: ptSummary.total,
          biaya_per_batch: biayaPerBatch,
          revenue_per_batch: Math.round(revenuePerBatch),
          profit_per_batch: profitPerBatch,
          margin_pct: Math.round(marginPct * 10) / 10,
        },
        rincian_per_batch: { bahan: rincianBahan, olah: rincianOlah },
        produk_turunan: produkTurunan,
      };

      const res = await fetch(`${API_BASE}/api/turunan/export-xlsx`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        throw new Error(data?.error || 'Gagal mengekspor file .xlsx');
      }
      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `HPP_${(ptNama || 'Produk_turunan').replace(/[^A-Za-z0-9_\-]+/g, '_')}_${new Date().toISOString().slice(0, 10)}.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (e) {
      console.error(e);
      alert('Gagal mengekspor file .xlsx');
    }
  };




  const exportMfXlsx = async () => {
    try {
      const target = Math.max(1, mfSummary.target);
      const bahan = mfBahan.map((b) => {
        const total = parseNumber(b.totalBulan);
        return { nama: b.nama, total_bulan: total, alokasi_per_produk: Math.round(total / target) };
      });
      const tkl = mfTkl.map((t) => {
        const total = parseNumber(t.totalUpah);
        return { jenis: t.jenis, total_upah: total, alokasi_per_produk: Math.round(total / target) };
      });
      const ohp = mfOhp.map((o) => {
        const total = parseNumber(o.totalBulan);
        return { nama: o.nama, total_bulan: total, alokasi_per_produk: Math.round(total / target) };
      });

      const varUnit = Math.round((mfSummary.totalBahan + mfSummary.totalTkl) / target);
      const harga = parseNumber(mfChosenPrice);
      const unitProfit = Math.max(0, harga - varUnit);
      const margin_pct = harga > 0 ? (unitProfit / harga) * 100 : 0;

      const projection = mfProjection ? {
        target_laba_bulan: parseNumber(mfTargetProfit),
        qty_day: mfProjection.qtyDayBase,
        qty_month: mfProjection.qtyMonth,
        omset: mfProjection.omset,
        biaya_produksi: mfProjection.biayaProduksi,
        fix_month: mfProjection.fixMonth,
        laba_bersih: mfProjection.laba,
      } : undefined;

      const payload: any = {
        produk: { nama: mfNama || 'Produk' },
        bahan,
        tkl,
        ohp,
        ringkasan: {
          total_bahan_bln: mfSummary.totalBahan,
          total_tkl_bln: mfSummary.totalTkl,
          total_ohp_bln: mfSummary.totalOhp,
          total_biaya_bln: mfSummary.total,
          target_output: target,
          hpp_per_pcs: mfSummary.hpp,
        },
        price: { harga_pilihan: harga, profit_per_unit: unitProfit, margin_pct: Math.round(margin_pct * 10) / 10 },
        price_suggestions: mfPriceSuggests,
      };
      if (projection) payload.projection = projection;

      const res = await fetch(`${API_BASE}/api/manufaktur/export-xlsx`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const text = await res.text();
        throw new Error(text || 'Gagal mengekspor XLSX');
      }
      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      const safeName = (mfNama || 'Produk').replace(/[^A-Za-z0-9_\-]+/g, '_');
      a.href = url;
      a.download = `HPP_${safeName}_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (e) {
      console.error(e);
      alert('Gagal mengekspor file .xlsx');
    }
  };


  const vfProjection = useMemo(() => {
    const harga = parseNumber(vfChosenPrice);
    const targetProfit = parseNumber(vfTargetProfit);
    const varUnit = vfSummary.totalVar;
    const fixMonth = vfSummary.totalFix;
    const unitProfit = harga - varUnit;
    if (harga <= 0 || targetProfit <= 0 || unitProfit <= 0) return null;
    const qtyMonth = Math.ceil((targetProfit + fixMonth) / unitProfit);
    const qtyDayBase = Math.ceil(qtyMonth / 30);
    const omset = harga * qtyMonth;
    const biayaProduksi = varUnit * qtyMonth;
    const laba = omset - biayaProduksi - fixMonth;
    const scenario = [0.7, 1.0, 1.3].map((m) => {
      const dailyQty = Math.max(0, Math.round(qtyDayBase * m));
      const dailyProfit = unitProfit * dailyQty - fixMonth / 30;
      return Array.from({ length: 30 }, () => Math.round(dailyProfit));
    });
    return { qtyDayBase, qtyMonth, omset, biayaProduksi, fixMonth, laba, unitProfit, scenario };
  }, [vfChosenPrice, vfTargetProfit, vfSummary]);


  // ----- MANUFAKTUR / PABRIK -----
  type MfBahan = { id: string; nama: string; totalBulan: string };
  type MfTkl = { id: string; jenis: string; totalUpah: string };
  type MfOhp = { id: string; nama: string; totalBulan: string };
  const [mfNama, setMfNama] = useState('');
  const [mfKategori, setMfKategori] = useState<'fashion' | 'elektronik' | 'perabotan' | 'lainnya'>('fashion');
  const [mfBahan, setMfBahan] = useState<MfBahan[]>([{ id: crypto.randomUUID(), nama: '', totalBulan: '' }]);
  const [mfTkl, setMfTkl] = useState<MfTkl[]>([{ id: crypto.randomUUID(), jenis: '', totalUpah: '' }]);
  const [mfOhp, setMfOhp] = useState<MfOhp[]>([{ id: crypto.randomUUID(), nama: '', totalBulan: '' }]);
  const [mfOutput, setMfOutput] = useState('100');

  const [mfAiLoading, setMfAiLoading] = useState(false);
  const [mfAiError, setMfAiError] = useState<string | null>(null);

  const [mfTargetProfit, setMfTargetProfit] = useState('');
  const [mfChosenPrice, setMfChosenPrice] = useState('');
  const [mfPriceSuggestLoading, setMfPriceSuggestLoading] = useState(false);
  const [mfPriceSuggests, setMfPriceSuggests] = useState<Array<{ level: string; harga: number; profit_per_unit: number; margin_pct: number; catatan: string }>>([]);

  const mfSummary = useMemo(() => {
    const totalBahan = mfBahan.reduce((s, b) => s + parseNumber(b.totalBulan), 0);
    const totalTkl = mfTkl.reduce((s, t) => s + parseNumber(t.totalUpah), 0);
    const totalOhp = mfOhp.reduce((s, o) => s + parseNumber(o.totalBulan), 0);
    const total = totalBahan + totalTkl + totalOhp;
    const target = Math.max(1, parseNumber(mfOutput));
    const hpp = Math.round(total / target);
    return { totalBahan, totalTkl, totalOhp, total, target, hpp };
  }, [mfBahan, mfTkl, mfOhp, mfOutput]);

  const mfProjection = useMemo(() => {
    const harga = parseNumber(mfChosenPrice);
    const targetProfit = parseNumber(mfTargetProfit);
    const varUnit = Math.round((mfSummary.totalBahan + mfSummary.totalTkl) / Math.max(1, mfSummary.target));
    const fixMonth = mfSummary.totalOhp;
    const unitProfit = harga - varUnit;
    if (harga <= 0 || targetProfit <= 0 || unitProfit <= 0) return null;
    const qtyMonth = Math.ceil((targetProfit + fixMonth) / unitProfit);
    const qtyDayBase = Math.ceil(qtyMonth / 30);
    const omset = harga * qtyMonth;
    const biayaProduksi = varUnit * qtyMonth;
    const laba = omset - biayaProduksi - fixMonth;
    const scenario = [0.7, 1.0, 1.3].map((m) => {
      const dailyQty = Math.max(0, Math.round(qtyDayBase * m));
      const dailyProfit = unitProfit * dailyQty - fixMonth / 30;
      return Array.from({ length: 30 }, () => Math.round(dailyProfit));
    });
    return { qtyDayBase, qtyMonth, omset, biayaProduksi, fixMonth, laba, unitProfit, scenario };
  }, [mfChosenPrice, mfTargetProfit, mfSummary]);


  const runMfAiAnalysis = async () => {
    try {
      setMfAiError(null);
      setMfAiLoading(true);
      const res = await fetch(`${API_BASE}/api/manufaktur/estimate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ nama: mfNama || 'Produk', kategori: mfKategori || 'umum' }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal memproses analisis AI');

      const bahan = Array.isArray(data?.bahan) ? data.bahan.map((b: any) => ({
        id: crypto.randomUUID(),
        nama: String(b?.nama ?? ''),
        totalBulan: String(b?.total_bulan ?? ''),
      })) : [];
      const tkl = Array.isArray(data?.tkl) ? data.tkl.map((t: any) => ({
        id: crypto.randomUUID(),
        jenis: String(t?.jenis ?? ''),
        totalUpah: String(t?.total_upah ?? ''),
      })) : [];
      const ohp = Array.isArray(data?.ohp) ? data.ohp.map((o: any) => ({
        id: crypto.randomUUID(),
        nama: String(o?.nama ?? ''),
        totalBulan: String(o?.total_bulan ?? ''),
      })) : [];

      if (bahan.length === 0 && tkl.length === 0 && ohp.length === 0) {
        throw new Error('AI tidak mengembalikan data biaya');
      }

      setMfBahan((prev) => [...prev, ...bahan]);
      setMfTkl((prev) => [...prev, ...tkl]);
      setMfOhp((prev) => [...prev, ...ohp]);
    } catch (e: any) {
      setMfAiError(e?.message || 'Terjadi kesalahan tak terduga');
    } finally {
      setMfAiLoading(false);
    }
  };

  // ----- PRODUKSI TURUNAN -----
  type PtBahan = { id: string; nama: string; biaya: string; jumlah: string; satuan: string };
  type PtOlah = { id: string; nama: string; harga: string; periode: 'per-batch' | 'per-bulan' | 'per-minggu' | 'per-hari' };
  type PtProduk = { id: string; nama: string; qty: string; satuan: 'kg' | 'pcs'; hargaJual: string };
  const [ptNama, setPtNama] = useState('');
  const [ptBatch, setPtBatch] = useState('1');
  const [ptBahan, setPtBahan] = useState<PtBahan[]>([{ id: crypto.randomUUID(), nama: '', biaya: '', jumlah: '', satuan: 'kg' }]);
  const [ptOlah, setPtOlah] = useState<PtOlah[]>([{ id: crypto.randomUUID(), nama: '', harga: '', periode: 'per-bulan' }]);
  const [ptProduk, setPtProduk] = useState<PtProduk[]>([{ id: crypto.randomUUID(), nama: '', qty: '', satuan: 'pcs', hargaJual: '' }]);
  const [ptShowBatchInfo, setPtShowBatchInfo] = useState(false);


  const ptSummary = useMemo(() => {
    const batch = Math.max(1, parseNumber(ptBatch));
    const totalBahan = ptBahan.reduce((s, b) => s + parseNumber(b.biaya), 0);
    const totalOlah = ptOlah.reduce((s, o) => {
      const h = parseNumber(o.harga);
      switch (o.periode) {
        case 'per-batch': return s + h * batch;
        case 'per-bulan': return s + h;
        case 'per-minggu': return s + h * 4;
        case 'per-hari': return s + h * 30;
        default: return s;
      }
    }, 0);
    const total = totalBahan + totalOlah;
    const totalQty = ptProduk.reduce((s, p) => s + parseNumber(p.qty), 0) || 1;
    const hppUnit = Math.round(total / totalQty);
    // Average price across all products (weighted by qty)
    const totalRevenue = ptProduk.reduce((s, p) => s + parseNumber(p.hargaJual) * parseNumber(p.qty), 0);
    const avgPrice = totalQty > 0 ? totalRevenue / totalQty : 0;
    const potensiLabaPerUnit = Math.max(0, Math.round(avgPrice - hppUnit));
    const marginPct = avgPrice > 0 ? Math.max(0, ((avgPrice - hppUnit) / avgPrice) * 100) : 0;
    return { batch, totalBahan, totalOlah, total, totalQty, hppUnit, avgPrice, potensiLabaPerUnit, marginPct };
  }, [ptBatch, ptBahan, ptOlah, ptProduk]);

  const calcPt = () => {
    // Saat hitung, tampilkan hasil khusus Produksi Turunan
    setResult({ type: 'produksi-turunan' } as any);
  };


  const [ptAiLoading, setPtAiLoading] = useState(false);
  const runPtAiAnalysis = async () => {
    try {
      setPtAiLoading(true);
      const res = await fetch(`${API_BASE}/api/turunan/estimate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ nama: ptNama || 'Produk', batch: parseNumber(ptBatch) }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal mengambil estimasi AI');
      if (Array.isArray(data?.bahan) && data.bahan.length > 0) {
        const b = data.bahan[0];
        setPtBahan([{ id: crypto.randomUUID(), nama: String(b.nama || ''), biaya: String(b.biaya || ''), jumlah: String(b.jumlah || ''), satuan: String(b.satuan || '') }]);
      }
      if (Array.isArray(data?.olah)) {
        setPtOlah(
          data.olah.map((o: any) => ({
            id: crypto.randomUUID(),
            nama: String(o.nama || ''),
            harga: String(o.harga || ''),
            periode: (['per-batch', 'per-bulan', 'per-minggu', 'per-hari'].includes(o.periode) ? o.periode : 'per-bulan')
          }))
        );
      }
      if (Array.isArray(data?.produk)) {
        setPtProduk(
          data.produk.map((p: any) => ({
            id: crypto.randomUUID(),
            nama: String(p.nama || ''),
            qty: String(p.qty ?? ''),
            satuan: (['pcs', 'kg'].includes(p.satuan) ? p.satuan : 'pcs'),
            hargaJual: String(p.hargaJual ?? ''),
          }))
        );
      }
    } catch (e) {
      console.error(e);
      alert('Gagal mengambil estimasi AI');
    } finally {
      setPtAiLoading(false);
    }
  };
  // ----- PRODUK JASA -----
  type PjDirect = { id: string; nama: string; harga: string };
  type PjOpex = { id: string; nama: string; biaya: string; periode: 'per-bulan' | 'per-minggu' | 'per-hari' };
  const [pjNama, setPjNama] = useState('');
  const [pjKategori, setPjKategori] = useState<'jasa-profesional' | 'jasa-kreatif' | 'jasa-perawatan' | 'lainnya'>('jasa-profesional');
  const [pjDirect, setPjDirect] = useState<PjDirect[]>([{ id: crypto.randomUUID(), nama: '', harga: '' }]);
  const [pjOpex, setPjOpex] = useState<PjOpex[]>([{ id: crypto.randomUUID(), nama: '', biaya: '', periode: 'per-bulan' }]);
  const [pjTarget, setPjTarget] = useState('100');

  const pjSummary = useMemo(() => {
    const target = Math.max(1, parseNumber(pjTarget));
    const directPerLayanan = pjDirect.reduce((s, d) => s + parseNumber(d.harga), 0);
    const monthlyDirect = directPerLayanan * target;
    const monthlyOpex = pjOpex.reduce((s, o) => {
      const b = parseNumber(o.biaya);
      switch (o.periode) {
        case 'per-bulan': return s + b;
        case 'per-minggu': return s + (b * 4);
        case 'per-hari': return s + (b * 30);
        default: return s;
      }
    }, 0);
    const total = monthlyDirect + monthlyOpex;
    const hpp = Math.round(total / target);
    return { target, directPerLayanan, monthlyDirect, monthlyOpex, total, hpp };
  }, [pjDirect, pjOpex, pjTarget]);

  const [pjAiLoading, setPjAiLoading] = useState(false);
  const [pjAiError, setPjAiError] = useState<string | null>(null);
  const runPjAiAnalysis = async () => {
    try {
      setPjAiError(null);
      setPjAiLoading(true);
      const res = await fetch(`${API_BASE}/api/jasa/estimate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ nama: pjNama, kategori: pjKategori }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error((data && data.error) || 'Gagal memuat estimasi AI');
      const direct = Array.isArray(data.direct) ? data.direct : [];
      const opex = Array.isArray(data.opex) ? data.opex : [];
      if (direct.length === 0 && opex.length === 0) throw new Error('AI tidak mengembalikan data');
      setPjDirect((r) => [
        ...r,
        ...direct.map((d: any) => ({ id: crypto.randomUUID(), nama: String(d.nama || ''), harga: String(Math.round(Number(d.harga || 0))) })),
      ]);
      setPjOpex((r) => [
        ...r,
        ...opex.map((o: any) => ({ id: crypto.randomUUID(), nama: String(o.nama || ''), biaya: String(Math.round(Number(o.biaya || 0))), periode: (o.periode === 'per-minggu' || o.periode === 'per-hari') ? o.periode : 'per-bulan' })),
      ]);
    } catch (e: any) {
      console.error(e);
      setPjAiError(e.message || 'Terjadi kesalahan');
    } finally {
      setPjAiLoading(false);
    }
  };

  const [pjTargetProfit, setPjTargetProfit] = useState('');
  const [pjChosenPrice, setPjChosenPrice] = useState('');
  const [pjPriceSuggestLoading, setPjPriceSuggestLoading] = useState(false);
  const [pjPriceSuggests, setPjPriceSuggests] = useState<Array<{ level: string; harga: number; profit_per_unit: number; margin_pct: number; catatan: string }>>([]);

  const pjProjection = useMemo(() => {
    const harga = parseNumber(pjChosenPrice);
    const targetProfit = parseNumber(pjTargetProfit);
    const varUnit = pjSummary.directPerLayanan;
    const fixMonth = pjSummary.monthlyOpex;
    const unitProfit = harga - varUnit;
    if (harga <= 0 || targetProfit <= 0 || unitProfit <= 0) return null;
    const qtyMonth = Math.ceil((targetProfit + fixMonth) / unitProfit);
    const qtyDayBase = Math.ceil(qtyMonth / 30);
    const omset = harga * qtyMonth;
    const biayaProduksi = varUnit * qtyMonth;
    const laba = omset - biayaProduksi - fixMonth;
    const scenario = [0.7, 1.0, 1.3].map((m) => {
      const dailyQty = Math.max(0, Math.round(qtyDayBase * m));
      const dailyProfit = unitProfit * dailyQty - fixMonth / 30;
      return Array.from({ length: 30 }, () => Math.round(dailyProfit));
    });
    return { qtyDayBase, qtyMonth, omset, biayaProduksi, fixMonth, laba, unitProfit, scenario };
  }, [pjChosenPrice, pjTargetProfit, pjSummary]);

  const requestPjPriceSuggest = async () => {
    try {
      setPjPriceSuggestLoading(true);
      setPjPriceSuggests([]);
      const res = await fetch(`${API_BASE}/api/price/suggest`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ nama: pjNama || 'Layanan', kategori: pjKategori || 'umum', hpp: pjSummary.hpp }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal meminta saran harga');
      const items = Array.isArray(data?.saran) ? data.saran : [];
      setPjPriceSuggests(items);
    } catch (e) {
      console.error(e);
    } finally {
      setPjPriceSuggestLoading(false);
    }
  };




  // Render helpers removed: using module-scoped Card and Modal to keep focus stable

  return (
    <div className="space-y-6">
      {/* Title & Subtitle */}
      <div>
        <h1 className="text-2xl font-bold text-gray-800">Kalkulator HPP Otomatis</h1>
        <p className="text-gray-600">Analisis profitabilitas produk dan kampanye iklan anda</p>
      </div>

      {/* Worksheet selector */}
      <Card title="Worksheet (Brand/Toko)" right={
        <div className="flex gap-2">
          <button onClick={() => setShowAddEdit({ mode: 'add', value: '' })} className="inline-flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
            <Plus size={16} /> Tambah
          </button>
          <button disabled={!selectedWorksheetId} onClick={() => setShowAddEdit({ mode: 'edit', value: currentWorksheet?.name || '' })} className="inline-flex items-center gap-2 px-3 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 disabled:opacity-50">
            <Pencil size={16} /> Edit
          </button>
          <button disabled={!selectedWorksheetId} onClick={() => { if (selectedWorksheetId) setShowDelete(selectedWorksheetId); }} className="inline-flex items-center gap-2 px-3 py-2 bg-rose-600 text-white rounded-lg hover:bg-rose-700 disabled:opacity-50">
            <Trash2 size={16} /> Hapus
          </button>
        </div>
      }>
        <div className="flex flex-col md:flex-row gap-3 items-start md:items-center">
          <select value={selectedWorksheetId ?? ''} onChange={(e) => {
            const v = e.target.value;
            setSelectedWorksheetId(v ? Number(v) : null);
            // Bersihkan data saat berpindah worksheet
            resetWorksheetData();
          }} className="w-full md:w-80 border border-gray-200 rounded-lg px-3 py-2">
            <option value="">- Pilih Worksheet -</option>
            {worksheets.map((w) => (
              <option key={w.id} value={w.id}>{w.name}</option>
            ))}
          </select>
          {currentWorksheet && <span className="text-sm text-gray-500">Aktif: {currentWorksheet.name}</span>}
        </div>
      </Card>

      {/* Model + Hasil */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card title="Pilih Model Bisnis">
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
            {modelItems.map((m) => {
              const Icon = m.icon as any;
              const active = model === m.id;
              return (
                <button key={m.id} onClick={() => setModel(m.id)}
                  className={`flex items-center gap-3 border rounded-lg p-3 text-left hover:border-green-500 ${active ? 'border-green-600 bg-green-50' : 'border-gray-200'}`}>
                  <Icon className={`${active ? 'text-green-600' : 'text-gray-500'}`} size={20} />
                  <span className="text-sm font-medium text-gray-800">{m.label}</span>
                </button>
              );
            })}
          </div>
        </Card>

        <Card title="Hasil Perhitungan" right={(model === 'ritel-fnb' || model === 'manufaktur' || model === 'produk-jasa') ? (
          <button onClick={() => { setShowHistory(true); loadHistory(model); }} className="inline-flex items-center gap-2 px-3 py-1.5 border border-gray-200 rounded-lg hover:bg-gray-50">Riwayat</button>
        ) : undefined}>
          {!result && model === 'iklan-cod' && (
            <div className="space-y-4">
              {/* Atas: 2 kartu utama */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="rounded-xl border border-green-200 bg-green-50 p-4">
                  <div className="text-sm text-green-700">Laba Bersih / Penjualan</div>
                  <div className="mt-1 text-2xl font-semibold text-green-800">{formatRp(adSummary.labaPerUnit)}</div>
                  <div className="text-xs text-green-700/80">Per unit (total qty: {adSummary.totalQty})</div>
                </div>
                <div className="rounded-xl border border-cyan-200 bg-cyan-50 p-4">
                  <div className="text-sm text-cyan-700">Potensi Laba Total</div>
                  <div className="mt-1 text-2xl font-semibold text-cyan-800">{formatRp(adSummary.profit)}</div>
                  <div className="text-xs text-cyan-700/80">Revenue: {formatRp(adSummary.totalRevenue)} • Biaya: {formatRp(adSummary.totalHpp + adSummary.biayaIklan + adSummary.admin + adSummary.retur)}</div>
                </div>
              </div>

              {/* Tengah: 3 kartu kecil metric */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="rounded-xl bg-slate-100 p-4">
                  <div className="text-xs text-slate-600">Margin</div>
                  <div className="text-xl font-semibold text-slate-800">{adSummary.margin.toFixed(1)}%</div>
                </div>
                <div className="rounded-xl bg-slate-100 p-4">
                  <div className="text-xs text-slate-600">ROAS</div>
                  <div className="text-xl font-semibold text-slate-800">{adSummary.roas ? `${adSummary.roas.toFixed(2)}x` : '-'}</div>
                </div>
                <div className="rounded-xl bg-slate-100 p-4">
                  <div className="text-xs text-slate-600">CPR</div>
                  <div className="text-xl font-semibold text-slate-800">{adSummary.cpr ? formatRp(adSummary.cpr) : '-'}</div>
                </div>
              </div>

              {/* Bawah: Saran Evaluasi Iklan (AI) */}
              <div className="rounded-xl border border-gray-200 p-4">
                <div className="text-sm font-semibold text-gray-800 mb-2">Saran Evaluasi Iklan (AI)</div>
                {adAiError ? (
                  <div className="text-xs text-red-700 bg-red-50 border border-red-200 rounded p-2 mb-2">{adAiError}</div>
                ) : null}
                {adAiLoading ? (
                  <div className="flex items-center gap-2 text-sm text-gray-600"><Loader2 className="w-4 h-4 animate-spin" /> Meminta saran AI...</div>
                ) : adAiSaran.length ? (
                  <ul className="list-disc ml-5 text-sm text-gray-700 space-y-1">
                    {adAiSaran.map((s, i) => <li key={i}>{s}</li>)}
                  </ul>
                ) : (
                  <button onClick={requestAdAi} className="inline-flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"><Brain className="w-4 h-4" /> Minta Saran AI</button>
                )}
              </div>
            </div>
          )}

          {!result && model === 'marketplace' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="rounded-xl bg-slate-50 p-4">
                  <div className="text-sm font-semibold text-gray-800 mb-2">Rincian biaya & potongan per pcs</div>
                  <ul className="text-sm text-gray-700 space-y-1">
                    <li>HPP produk: <span className="font-semibold text-gray-900">{formatRp(mpSummary.hpp)}</span></li>
                    <li>Komisi marketplace: <span className="font-semibold text-gray-900">{formatRp(mpSummary.komisi)}</span></li>
                    <li>Biaya layanan: <span className="font-semibold text-gray-900">{formatRp(mpSummary.layanan)}</span></li>
                    <li>Biaya program: <span className="font-semibold text-gray-900">{formatRp(mpSummary.program)}</span></li>
                    {mpSummary.prosesPesanan > 0 && (
                      <li>Biaya proses pesanan: <span className="font-semibold text-gray-900">{formatRp(mpSummary.prosesPesanan)}</span></li>
                    )}
                    <li>Komisi AMS: <span className="font-semibold text-gray-900">{formatRp(mpSummary.ams)}</span></li>
                  </ul>
                </div>
                <div className="rounded-xl bg-slate-50 p-4">
                  <div className="text-sm font-semibold text-gray-800 mb-2">Total biaya & potongan</div>
                  <div className="text-2xl font-semibold text-gray-900">{formatRp(mpSummary.biaya)}</div>
                  <div className="text-xs text-gray-500">Harga jual: {formatRp(mpSummary.harga)} • Potongan (non-HPP): {formatRp(mpSummary.potongan)}</div>
                </div>
              </div>

              <div className="rounded-xl border border-green-200 bg-green-50 p-4">
                <div className="text-sm text-green-700">Estimasi Penghasilan Bersih Anda</div>
                <div className="mt-1 text-2xl font-semibold text-green-800">{formatRp(mpSummary.profit)}</div>
                <div className="text-xs text-green-700/80">Margin: {mpSummary.margin.toFixed(1)}%</div>
              </div>
            </div>
          )}

          {result && result.type === 'analisis-cepat' && (
            <div className="space-y-4">
              {/* Tiga kartu ringkasan */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="rounded-xl border border-orange-200 bg-orange-50 p-4">
                  <div className="text-sm text-orange-700">Estimasi HPP</div>
                  <div className="mt-1 text-2xl font-semibold text-orange-800">{formatRp(result.estimasi_hpp)}</div>
                </div>
                <div className="rounded-xl border border-green-200 bg-green-50 p-4">
                  <div className="text-sm text-green-700">Potensi Laba</div>
                  <div className="mt-1 text-2xl font-semibold text-green-800">{formatRp(result.potensi_laba)}</div>
                </div>
                <div className="rounded-xl border border-cyan-200 bg-cyan-50 p-4">
                  <div className="text-sm text-cyan-700">Margin</div>
                  <div className="mt-1 text-2xl font-semibold text-cyan-800">{result.margin}%</div>
                </div>
              </div>

              {/* Estimasi komponen biaya */}
              <div className="rounded-xl bg-slate-50 p-4">
                <div className="text-sm font-semibold text-gray-800 mb-2">Estimasi Komponen Biaya</div>
                <ul className="text-sm text-gray-700 space-y-1 list-disc ml-5">
                  {result.detail.komponen_biaya.map((k: any, idx: number) => (
                    <li key={idx} className="flex items-center justify-between gap-3">
                      <span>{k.label}</span>
                      <span className="font-semibold text-gray-900">{formatRp(k.nilai)}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Analisis & Saran AI */}
              <div className="rounded-xl border border-amber-200 bg-amber-50 p-4">
                <div className="text-sm font-semibold text-amber-800 mb-2">Analisis & Saran AI</div>
                <ul className="text-sm text-amber-900 list-disc ml-5 space-y-1">
                  {result.detail.saran_ai.map((s: any, idx: number) => <li key={idx}>{s}</li>)}
                </ul>
              </div>
            </div>
          )}


          {result && result.type === 'manufaktur' && (
            <div className="space-y-4">
              <div className="rounded-xl border border-gray-200 bg-white p-4">
                <div className="text-sm font-semibold text-gray-800 mb-2">Rincian HPP per Produk</div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs font-medium text-gray-600 mb-1">Biaya variabel per produk</div>
                    <ul className="text-sm text-gray-700 divide-y divide-gray-100">
                      {mfBahan.map((b) => (
                        <li key={`b-${b.id ?? b.nama}`} className="flex items-center justify-between py-1.5">
                          <span className="truncate pr-3">{b.nama || 'Bahan baku'}</span>
                          <span className="font-semibold text-gray-900">{formatRp(Math.round(parseNumber(b.totalBulan) / Math.max(1, mfSummary.target)))}</span>
                        </li>
                      ))}
                      {mfTkl.map((t) => (
                        <li key={`t-${t.id ?? t.jenis}`} className="flex items-center justify-between py-1.5">
                          <span className="truncate pr-3">{t.jenis ? `TKL: ${t.jenis}` : 'Tenaga kerja langsung'}</span>
                          <span className="font-semibold text-gray-900">{formatRp(Math.round(parseNumber(t.totalUpah) / Math.max(1, mfSummary.target)))}</span>
                        </li>
                      ))}
                    </ul>
                    <div className="flex items-center justify-between text-sm text-gray-700 mt-2 pt-2 border-t">
                      <span>Total biaya variabel per produk</span>
                      <span className="font-semibold text-gray-900">{formatRp(Math.round((mfSummary.totalBahan + mfSummary.totalTkl) / Math.max(1, mfSummary.target)))}</span>
                    </div>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-gray-600 mb-1">Alokasi biaya tetap per produk</div>
                    <ul className="text-sm text-gray-700 divide-y divide-gray-100">
                      {mfOhp.map((o) => (
                        <li key={`o-${o.id ?? o.nama}`} className="flex items-center justify-between py-1.5">
                          <span className="truncate pr-3">{o.nama || 'Overhead pabrik'}</span>
                          <span className="font-semibold text-gray-900">{formatRp(Math.round(parseNumber(o.totalBulan) / Math.max(1, mfSummary.target)))}</span>
                        </li>
                      ))}
                    </ul>
                    <div className="flex items-center justify-between text-sm text-gray-700 mt-2 pt-2 border-t">
                      <span>Total alokasi biaya tetap</span>
                      <span className="font-semibold text-gray-900">{formatRp(Math.round(mfSummary.totalOhp / Math.max(1, mfSummary.target)))}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="rounded-xl border border-amber-200 bg-amber-50 p-4">
                <div className="text-sm font-semibold text-amber-800 mb-2">Saran harga jual</div>
                {mfPriceSuggests.length === 0 ? (
                  <button onClick={requestMfPriceSuggest} disabled={mfPriceSuggestLoading} className="inline-flex items-center gap-2 px-3 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 disabled:opacity-50">
                    {mfPriceSuggestLoading ? (<><Loader2 className="w-4 h-4 animate-spin" /><span>Memproses...</span></>) : (<><Wand2 className="w-4 h-4" /><span>Minta Saran Harga AI</span></>)}
                  </button>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {mfPriceSuggests.map((s, idx) => (
                      <button key={idx} type="button" onClick={() => setMfChosenPrice(String(s.harga))} className={`text-left rounded-lg border p-3 hover:bg-white ${parseNumber(mfChosenPrice) === s.harga ? 'border-green-400 ring-2 ring-green-200' : 'border-gray-200 bg-white'}`}>
                        <div className="text-xs uppercase tracking-wide text-gray-500">{s.level}</div>
                        <div className="text-xl font-semibold text-gray-900 mt-0.5">{formatRp(s.harga)}</div>
                        <div className="text-xs text-gray-600 mt-1">Profit/unit: <span className="font-medium text-gray-800">{formatRp(s.profit_per_unit)}</span> · Margin: <span className="font-medium text-gray-800">{s.margin_pct}%</span></div>
                        {s.catatan && <div className="text-xs text-gray-600 mt-1">{s.catatan}</div>}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              <div className="rounded-xl border border-gray-200 bg-white p-4">
                <div className="text-sm font-semibold text-gray-800 mb-2">Target & Proyeksi Penjualan</div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Target laba bersih / bulan (Rp)</label>
                    <input value={mfTargetProfit} onChange={(e) => setMfTargetProfit(e.target.value)} placeholder="cth: 5.000.000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Harga jual pilihan (Rp)</label>
                    <input value={mfChosenPrice} onChange={(e) => setMfChosenPrice(e.target.value)} placeholder="cth: 25.000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>
                </div>

                {mfProjection && (
                  <div className="mt-4 space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Target jual / hari</div>
                        <div className="text-xl font-semibold text-gray-900">{mfProjection.qtyDayBase} pcs</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Total jual / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{mfProjection.qtyMonth} pcs</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Potensi omset / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{formatRp(mfProjection.omset)}</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Biaya produksi</div>
                        <div className="text-xl font-semibold text-gray-900">{formatRp(mfProjection.biayaProduksi)}</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Biaya tetap / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{formatRp(mfProjection.fixMonth)}</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Laba bersih / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{formatRp(mfProjection.laba)}</div>
                      </div>
                    </div>

                    <div className="mt-2">
                      {(() => {
                        // Build cumulative 30-day profit for three scenarios
                        const raw = mfProjection.scenario; // [sepi, target, rame] as daily profit
                        const cumulative = (arr: number[]) => {
                          const out: number[] = [];
                          arr.forEach((v, i) => out.push((out[i - 1] || 0) + v));
                          return out;
                        };
                        const sepi = cumulative(raw[0]);
                        const target = cumulative(raw[1]);
                        const rame = cumulative(raw[2]);

                        // SVG canvas
                        const w = 560, h = 260;
                        const padL = 64, padR = 14, padT = 28, padB = 40;
                        const innerW = w - padL - padR;
                        const innerH = h - padT - padB;

                        const maxY = Math.max(
                          ...sepi, ...target, ...rame,
                          1
                        );
                        const tickCount = 5;
                        const ticks = Array.from({ length: tickCount }, (_, i) => Math.round((maxY * i) / (tickCount - 1)));

                        const x = (i: number) => padL + (innerW * i) / 29;
                        const y = (val: number) => padT + innerH - (val / maxY) * innerH;
                        const toPoints = (arr: number[]) => arr.map((v, i) => `${x(i)},${y(v)}`).join(' ');

                        const pRame = toPoints(rame);
                        const pTarget = toPoints(target);
                        const pSepi = toPoints(sepi);

                        return (
                          <svg width="100%" height={h} viewBox={`0 0 ${w} ${h}`} className="w-full">
                            {/* Title */}
                            <text x={w / 2} y={18} textAnchor="middle" className="fill-gray-700" style={{ fontSize: 12, fontWeight: 600 }}>Grafik Proyeksi Laba (30 Hari)</text>

                            {/* Y grid & labels */}
                            {ticks.map((t, idx) => {
                              const yy = y(t);
                              return (
                                <g key={idx}>
                                  <line x1={padL} y1={yy} x2={w - padR} y2={yy} stroke="#e5e7eb" strokeWidth={1} />
                                  <text x={padL - 8} y={yy + 4} textAnchor="end" className="fill-gray-500" style={{ fontSize: 10 }}>{formatRp(t)}</text>
                                </g>
                              );
                            })}

                            {/* X axis ticks & labels */}
                            {([0, 14, 29] as const).map((d, i) => (
                              <g key={i}>
                                <line x1={x(d)} y1={padT + innerH} x2={x(d)} y2={padT + innerH + 4} stroke="#9ca3af" />
                                <text x={x(d)} y={padT + innerH + 16} textAnchor="middle" className="fill-gray-500" style={{ fontSize: 10 }}>
                                  {d === 0 ? 'Hari 1' : d === 14 ? 'Hari 15' : 'Hari 30'}
                                </text>
                              </g>
                            ))}

                            {/* Lines */}
                            <polyline points={pRame} fill="none" stroke="#3b82f6" strokeWidth="2.5" />
                            <polyline points={pTarget} fill="none" stroke="#22c55e" strokeWidth="2.5" />
                            <polyline points={pSepi} fill="none" stroke="#f97316" strokeWidth="2.5" strokeDasharray="4,4" />
                          </svg>
                        );
                      })()}

                      {/* Legend */}
                      <div className="flex items-center gap-6 text-xs text-gray-600 mt-2">
                        <span className="inline-flex items-center gap-2"><span className="w-3 h-3 rounded-sm bg-blue-500 inline-block"></span> Kondisi Rame</span>
                        <span className="inline-flex items-center gap-2"><span className="w-3 h-3 rounded-sm bg-green-500 inline-block"></span> Target</span>
                        <span className="inline-flex items-center gap-2"><span className="w-3 h-0.5 border-t-2 border-dashed border-orange-500 inline-block"></span> Kondisi Sepi</span>
                      </div>
                    </div>

                  </div>
                )}
              </div>

              <div className="flex flex-wrap items-center gap-3">
                <button onClick={() => setShowSaveCalc(true)} className="inline-flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50"><Save className="w-4 h-4" /> Simpan Perhitungan</button>
                <button onClick={exportMfXlsx} className="inline-flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50"><Download className="w-4 h-4" /> Export .xlsx</button>
              </div>

            </div>
          )}


          {result && result.type === 'ritel-fnb' && (
            <div className="space-y-4">
              <div className="rounded-xl border border-gray-200 bg-white p-4">
                <div className="text-sm font-semibold text-gray-800 mb-2">Rincian HPP per Produk</div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs font-medium text-gray-600 mb-1">Biaya variabel per produk</div>
                    <ul className="text-sm text-gray-700 divide-y divide-gray-100">
                      {vfVarCosts.map((v) => (
                        <li key={v.id} className="flex items-center justify-between py-1.5">
                          <span className="truncate pr-3">{v.bahan || 'Bahan'}</span>
                          <span className="font-semibold text-gray-900">{formatRp(varCostPerProduk(v))}</span>
                        </li>
                      ))}
                      <li className="flex items-center justify-between pt-2 mt-1 border-t text-gray-700">
                        <span className="font-medium">Total biaya variabel</span>
                        <span className="font-semibold text-gray-900">{formatRp(vfSummary.totalVar)}</span>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-gray-600 mb-1">Alokasi biaya tetap per produk</div>
                    <ul className="text-sm text-gray-700 divide-y divide-gray-100">
                      {vfFixCosts.map((f) => {
                        const alloc = parseNumber(f.alokasi || '');
                        return (
                          <li key={f.id} className="flex items-center justify-between py-1.5">
                            <span className="truncate pr-3">{f.nama || 'Biaya tetap'}</span>
                            <span className="font-semibold text-gray-900">{formatRp(alloc)}</span>
                          </li>
                        );
                      })}
                      <li className="flex items-center justify-between pt-2 mt-1 border-t text-gray-700">
                        <span className="font-medium">Total alokasi tetap</span>
                        <span className="font-semibold text-gray-900">{formatRp(vfFixCosts.reduce((s, f) => s + parseNumber(f.alokasi || ''), 0))}</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="rounded-xl border border-amber-200 bg-amber-50 p-4">
                <div className="text-sm font-semibold text-amber-800 mb-2">Saran harga jual</div>
                {vfPriceSuggests.length === 0 ? (
                  <button onClick={requestVfPriceSuggest} disabled={vfPriceSuggestLoading} className="inline-flex items-center gap-2 px-3 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 disabled:opacity-50">
                    {vfPriceSuggestLoading ? (<><Loader2 className="w-4 h-4 animate-spin" /><span>Memproses...</span></>) : (<><Wand2 className="w-4 h-4" /><span>Minta Saran Harga AI</span></>)}
                  </button>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {vfPriceSuggests.map((s, idx) => (
                      <button key={idx} type="button" onClick={() => setVfChosenPrice(String(s.harga))} className={`text-left rounded-lg border p-3 hover:bg-white ${parseNumber(vfChosenPrice) === s.harga ? 'border-green-400 ring-2 ring-green-200' : 'border-gray-200 bg-white'}`}>
                        <div className="text-xs uppercase tracking-wide text-gray-500">{s.level}</div>
                        <div className="text-xl font-semibold text-gray-900 mt-0.5">{formatRp(s.harga)}</div>
                        <div className="text-xs text-gray-600 mt-1">Profit/unit: <span className="font-medium text-gray-800">{formatRp(s.profit_per_unit)}</span> · Margin: <span className="font-medium text-gray-800">{s.margin_pct}%</span></div>
                        {s.catatan && <div className="text-xs text-gray-600 mt-1">{s.catatan}</div>}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              <div className="rounded-xl border border-gray-200 bg-white p-4">
                <div className="text-sm font-semibold text-gray-800 mb-2">Target & Proyeksi Penjualan</div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Target laba bersih / bulan (Rp)</label>
                    <input value={vfTargetProfit} onChange={(e) => setVfTargetProfit(e.target.value)} placeholder="cth: 5000000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Harga jual pilihan (Rp)</label>
                    <input value={vfChosenPrice} onChange={(e) => setVfChosenPrice(e.target.value)} placeholder="cth: 25000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>
                </div>

                {vfProjection && (
                  <div className="mt-4 space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Target jual / hari</div>
                        <div className="text-xl font-semibold text-gray-900">{vfProjection.qtyDayBase} pcs</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Total jual / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{vfProjection.qtyMonth} pcs</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Potensi omset / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{formatRp(vfProjection.omset)}</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Total biaya produksi / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{formatRp(vfProjection.biayaProduksi)}</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Total biaya tetap / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{formatRp(vfProjection.fixMonth)}</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Proyeksi laba bersih / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{formatRp(vfProjection.laba)}</div>
                      </div>
                    </div>

                    <div className="mt-2">
                      {(() => {
                        // Build cumulative 30-day profit for three scenarios
                        const raw = vfProjection.scenario; // [sepi, target, rame] as daily profit
                        const cumulative = (arr: number[]) => {
                          const out: number[] = [];
                          arr.forEach((v, i) => out.push((out[i - 1] || 0) + v));
                          return out;
                        };
                        const sepi = cumulative(raw[0]);
                        const target = cumulative(raw[1]);
                        const rame = cumulative(raw[2]);

                        // SVG canvas
                        const w = 560, h = 260;
                        const padL = 64, padR = 14, padT = 28, padB = 40;
                        const innerW = w - padL - padR;
                        const innerH = h - padT - padB;

                        const maxY = Math.max(
                          ...sepi, ...target, ...rame,
                          1
                        );
                        const tickCount = 5;
                        const ticks = Array.from({ length: tickCount }, (_, i) => Math.round((maxY * i) / (tickCount - 1)));

                        const x = (i: number) => padL + (innerW * i) / 29;
                        const y = (val: number) => padT + innerH - (val / maxY) * innerH;
                        const toPoints = (arr: number[]) => arr.map((v, i) => `${x(i)},${y(v)}`).join(' ');

                        const pRame = toPoints(rame);
                        const pTarget = toPoints(target);
                        const pSepi = toPoints(sepi);

                        return (
                          <svg width="100%" height={h} viewBox={`0 0 ${w} ${h}`} className="w-full">
                            {/* Title */}
                            <text x={w / 2} y={18} textAnchor="middle" className="fill-gray-700" style={{ fontSize: 12, fontWeight: 600 }}>Grafik Proyeksi Laba (30 Hari)</text>

                            {/* Y grid & labels */}
                            {ticks.map((t, idx) => {
                              const yy = y(t);
                              return (
                                <g key={idx}>
                                  <line x1={padL} y1={yy} x2={w - padR} y2={yy} stroke="#e5e7eb" strokeWidth={1} />
                                  <text x={padL - 8} y={yy + 4} textAnchor="end" className="fill-gray-500" style={{ fontSize: 10 }}>{formatRp(t)}</text>
                                </g>
                              );
                            })}

                            {/* X axis ticks & labels */}
                            {([0, 14, 29] as const).map((d, i) => (
                              <g key={i}>
                                <line x1={x(d)} y1={padT + innerH} x2={x(d)} y2={padT + innerH + 4} stroke="#9ca3af" />
                                <text x={x(d)} y={padT + innerH + 16} textAnchor="middle" className="fill-gray-500" style={{ fontSize: 10 }}>
                                  {d === 0 ? 'Hari 1' : d === 14 ? 'Hari 15' : 'Hari 30'}
                                </text>
                              </g>
                            ))}

                            {/* Lines */}
                            <polyline points={pRame} fill="none" stroke="#3b82f6" strokeWidth="2.5" />
                            <polyline points={pTarget} fill="none" stroke="#22c55e" strokeWidth="2.5" />
                            <polyline points={pSepi} fill="none" stroke="#f97316" strokeWidth="2.5" strokeDasharray="4,4" />
                          </svg>
                        );
                      })()}

                      {/* Legend */}
                      <div className="flex items-center gap-6 text-xs text-gray-600 mt-2">
                        <span className="inline-flex items-center gap-2"><span className="w-3 h-3 rounded-sm bg-blue-500 inline-block"></span> Kondisi Rame</span>
                        <span className="inline-flex items-center gap-2"><span className="w-3 h-3 rounded-sm bg-green-500 inline-block"></span> Target</span>
                        <span className="inline-flex items-center gap-2"><span className="w-3 h-0.5 border-t-2 border-dashed border-orange-500 inline-block"></span> Kondisi Sepi</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex flex-wrap items-center gap-3">
                <button onClick={() => setShowSaveCalc(true)} className="inline-flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50"><Save className="w-4 h-4" /> Simpan Perhitungan</button>
                <button onClick={exportVfXlsx} className="inline-flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50"><Download className="w-4 h-4" /> Export .xlsx</button>
              </div>
            </div>
          )}


          {result && result.type === 'produk-jasa' && (
            <div className="space-y-4">
              <div className="rounded-xl border border-gray-200 bg-white p-4">
                <div className="text-sm font-semibold text-gray-800 mb-2">Rincian HPP per Produk</div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs font-medium text-gray-600 mb-1">Biaya langsung per layanan</div>
                    <ul className="text-sm text-gray-700 divide-y divide-gray-100">
                      {pjDirect.map((d) => (
                        <li key={`d-${d.id}`} className="flex items-center justify-between py-1.5">
                          <span className="truncate pr-3">{d.nama || 'Biaya langsung'}</span>
                          <span className="font-semibold text-gray-900">{formatRp(parseNumber(d.harga))}</span>
                        </li>
                      ))}
                      <li className="flex items-center justify-between pt-2 mt-1 border-t text-gray-700">
                        <span className="font-medium">Total biaya langsung</span>
                        <span className="font-semibold text-gray-900">{formatRp(pjSummary.directPerLayanan)}</span>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-gray-600 mb-1">Alokasi biaya operasional per layanan</div>
                    <ul className="text-sm text-gray-700 divide-y divide-gray-100">
                      {pjOpex.map((o) => {
                        const b = parseNumber(o.biaya);
                        const monthly = o.periode === 'per-bulan' ? b : (o.periode === 'per-minggu' ? b * 4 : b * 30);
                        const alloc = Math.round(monthly / Math.max(1, pjSummary.target));
                        return (
                          <li key={`o-${o.id}`} className="flex items-center justify-between py-1.5">
                            <span className="truncate pr-3">{o.nama || 'Biaya operasional'}</span>
                            <span className="font-semibold text-gray-900">{formatRp(alloc)}</span>
                          </li>
                        );
                      })}
                      <li className="flex items-center justify-between pt-2 mt-1 border-t text-gray-700">
                        <span className="font-medium">Total alokasi operasional</span>
                        <span className="font-semibold text-gray-900">{formatRp(Math.round(pjSummary.monthlyOpex / Math.max(1, pjSummary.target)))}</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="rounded-xl border border-amber-200 bg-amber-50 p-4">
                <div className="text-sm font-semibold text-amber-800 mb-2">Saran harga jual</div>
                {pjPriceSuggests.length === 0 ? (
                  <button onClick={requestPjPriceSuggest} disabled={pjPriceSuggestLoading} className="inline-flex items-center gap-2 px-3 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 disabled:opacity-50">
                    {pjPriceSuggestLoading ? (<><Loader2 className="w-4 h-4 animate-spin" /><span>Memproses...</span></>) : (<><Wand2 className="w-4 h-4" /><span>Minta Saran Harga AI</span></>)}
                  </button>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {pjPriceSuggests.map((s, idx) => (
                      <button key={idx} type="button" onClick={() => setPjChosenPrice(String(s.harga))} className={`text-left rounded-lg border p-3 hover:bg-white ${parseNumber(pjChosenPrice) === s.harga ? 'border-green-400 ring-2 ring-green-200' : 'border-gray-200 bg-white'}`}>
                        <div className="text-xs uppercase tracking-wide text-gray-500">{s.level}</div>
                        <div className="text-xl font-semibold text-gray-900 mt-0.5">{formatRp(s.harga)}</div>
                        <div className="text-xs text-gray-600 mt-1">Profit/unit: <span className="font-medium text-gray-800">{formatRp(s.profit_per_unit)}</span> · Margin: <span className="font-medium text-gray-800">{s.margin_pct}%</span></div>
                        {s.catatan && <div className="text-xs text-gray-600 mt-1">{s.catatan}</div>}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              <div className="rounded-xl border border-gray-200 bg-white p-4">
                <div className="text-sm font-semibold text-gray-800 mb-2">Target & Proyeksi Penjualan</div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Target laba bersih / bulan (Rp)</label>
                    <input value={pjTargetProfit} onChange={(e) => setPjTargetProfit(e.target.value)} placeholder="cth: 5000000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Harga jual pilihan (Rp)</label>
                    <input value={pjChosenPrice} onChange={(e) => setPjChosenPrice(e.target.value)} placeholder="cth: 250000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>
                </div>

                {pjProjection && (
                  <div className="mt-4 space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Target jual / hari</div>
                        <div className="text-xl font-semibold text-gray-900">{pjProjection.qtyDayBase} pcs</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Total jual / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{pjProjection.qtyMonth} pcs</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Potensi omset / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{formatRp(pjProjection.omset)}</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Biaya produksi</div>
                        <div className="text-xl font-semibold text-gray-900">{formatRp(pjProjection.biayaProduksi)}</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Biaya tetap / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{formatRp(pjProjection.fixMonth)}</div>
                      </div>
                      <div className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                        <div className="text-xs text-gray-600">Laba bersih / bulan</div>
                        <div className="text-xl font-semibold text-gray-900">{formatRp(pjProjection.laba)}</div>
                      </div>
                    </div>

                    <div className="mt-2">
                      {(() => {
                        const raw = pjProjection.scenario;
                        const cumulative = (arr: number[]) => {



                          const out: number[] = [];
                          arr.forEach((v, i) => out.push((out[i - 1] || 0) + v));
                          return out;


                        };
                        const sepi = cumulative(raw[0]);
                        const target = cumulative(raw[1]);
                        const rame = cumulative(raw[2]);

                        const w = 560, h = 260;
                        const padL = 64, padR = 14, padT = 28, padB = 40;
                        const innerW = w - padL - padR;
                        const innerH = h - padT - padB;

                        const maxY = Math.max(
                          ...sepi, ...target, ...rame,
                          1
                        );
                        const tickCount = 5;
                        const ticks = Array.from({ length: tickCount }, (_, i) => Math.round((maxY * i) / (tickCount - 1)));

                        const x = (i: number) => padL + (innerW * i) / 29;
                        const y = (val: number) => padT + innerH - (val / maxY) * innerH;
                        const toPoints = (arr: number[]) => arr.map((v, i) => `${x(i)},${y(v)}`).join(' ');

                        const pRame = toPoints(rame);
                        const pTarget = toPoints(target);
                        const pSepi = toPoints(sepi);

                        return (
                          <svg width="100%" height={h} viewBox={`0 0 ${w} ${h}`} className="w-full">
                            <text x={w / 2} y={18} textAnchor="middle" className="fill-gray-700" style={{ fontSize: 12, fontWeight: 600 }}>Grafik Proyeksi Laba (30 Hari)</text>

                            {ticks.map((t, idx) => {
                              const yy = y(t);
                              return (
                                <g key={idx}>
                                  <line x1={padL} y1={yy} x2={w - padR} y2={yy} stroke="#e5e7eb" strokeWidth={1} />
                                  <text x={padL - 8} y={yy + 4} textAnchor="end" className="fill-gray-500" style={{ fontSize: 10 }}>{formatRp(t)}</text>
                                </g>
                              );
                            })}

                            {([0, 14, 29] as const).map((d, i) => (
                              <g key={i}>
                                <line x1={x(d)} y1={padT + innerH} x2={x(d)} y2={padT + innerH + 4} stroke="#9ca3af" />
                                <text x={x(d)} y={padT + innerH + 16} textAnchor="middle" className="fill-gray-500" style={{ fontSize: 10 }}>
                                  {d === 0 ? 'Hari 1' : d === 14 ? 'Hari 15' : 'Hari 30'}
                                </text>
                              </g>
                            ))}

                            <polyline points={pRame} fill="none" stroke="#3b82f6" strokeWidth="2.5" />
                            <polyline points={pTarget} fill="none" stroke="#22c55e" strokeWidth="2.5" />
                            <polyline points={pSepi} fill="none" stroke="#f97316" strokeWidth="2.5" strokeDasharray="4,4" />
                          </svg>
                        );
                      })()}

                      <div className="flex items-center gap-6 text-xs text-gray-600 mt-2">
                        <span className="inline-flex items-center gap-2"><span className="w-3 h-3 rounded-sm bg-blue-500 inline-block"></span> Kondisi Rame</span>
                        <span className="inline-flex items-center gap-2"><span className="w-3 h-3 rounded-sm bg-green-500 inline-block"></span> Target</span>
                        <span className="inline-flex items-center gap-2"><span className="w-3 h-0.5 border-t-2 border-dashed border-orange-500 inline-block"></span> Kondisi Sepi</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}



          {result && result.type === 'produk-jasa' && (
            <div className="flex flex-wrap items-center gap-3 mt-6">
              <button onClick={() => setShowSaveCalc(true)} className="inline-flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50"><Save className="w-4 h-4" /> Simpan Perhitungan</button>
              <button onClick={exportPjXlsx} className="inline-flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50"><Download className="w-4 h-4" /> Export .xlsx</button>
            </div>
          )}



          {result && result.type === 'produksi-turunan' && (
            <div className="space-y-4">
              {(() => {
                const batch = Math.max(1, parseNumber(ptBatch));
                const bahanPerBatch = ptBahan.reduce((s, b) => s + (parseNumber(b.biaya) / batch), 0);
                const olahPerBatch = ptOlah.reduce((s, o) => {
                  const h = parseNumber(o.harga);
                  if (o.periode === 'per-batch') return s + h;
                  if (o.periode === 'per-bulan') return s + h / batch;
                  if (o.periode === 'per-minggu') return s + (h * 4) / batch;
                  if (o.periode === 'per-hari') return s + (h * 30) / batch;
                  return s;
                }, 0);
                const biayaPerBatch = Math.round(bahanPerBatch + olahPerBatch);
                const qtyPerBatchTotal = ptProduk.reduce((s, p) => s + (parseNumber(p.qty) / batch), 0) || 1;
                const hppUnitBatch = Math.round(biayaPerBatch / qtyPerBatchTotal);
                const revenuePerBatch = ptProduk.reduce((s, p) => s + (parseNumber(p.hargaJual) * (parseNumber(p.qty) / batch)), 0);
                const profitPerBatch = Math.round(revenuePerBatch - biayaPerBatch);
                const marginPct = revenuePerBatch > 0 ? (profitPerBatch / revenuePerBatch) * 100 : 0;

                return (
                  <>
                    <div className="rounded-xl border border-gray-200 bg-white p-4">
                      <div className="text-sm font-semibold text-gray-800 mb-2">Ringkasan biaya & proyeksi laba per batch</div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                        <div className="rounded-lg bg-slate-50 p-3">
                          <div className="text-gray-600">Biaya bahan/batch</div>
                          <div className="text-lg font-semibold text-gray-900">{formatRp(Math.round(bahanPerBatch))}</div>
                        </div>
                        <div className="rounded-lg bg-slate-50 p-3">
                          <div className="text-gray-600">Biaya olah/batch</div>
                          <div className="text-lg font-semibold text-gray-900">{formatRp(Math.round(olahPerBatch))}</div>
                        </div>
                        <div className="rounded-lg bg-slate-50 p-3">
                          <div className="text-gray-600">Total biaya/batch</div>
                          <div className="text-lg font-semibold text-gray-900">{formatRp(biayaPerBatch)}</div>
                        </div>
                        <div className="rounded-lg bg-slate-50 p-3">
                          <div className="text-gray-600">Qty total/batch</div>
                          <div className="text-lg font-semibold text-gray-900">{Math.round(qtyPerBatchTotal)}</div>
                        </div>
                        <div className="rounded-lg bg-slate-50 p-3">
                          <div className="text-gray-600">HPP rata-rata/unit</div>
                          <div className="text-lg font-semibold text-gray-900">{formatRp(hppUnitBatch)}</div>
                        </div>
                        <div className="rounded-lg bg-slate-50 p-3">
                          <div className="text-gray-600">Revenue/batch</div>
                          <div className="text-lg font-semibold text-gray-900">{formatRp(Math.round(revenuePerBatch))}</div>
                        </div>
                        <div className="rounded-lg bg-slate-50 p-3">
                          <div className="text-gray-600">Laba/batch</div>
                          <div className="text-lg font-semibold text-gray-900">{formatRp(profitPerBatch)}</div>
                        </div>
                        <div className="rounded-lg bg-slate-50 p-3">
                          <div className="text-gray-600">Margin</div>
                          <div className="text-lg font-semibold text-gray-900">{(Math.round(marginPct * 10) / 10).toFixed(1)}%</div>
                        </div>
                      </div>
                    </div>

                    <div className="rounded-xl border border-amber-200 bg-amber-50 p-4">
                      <div className="text-sm font-semibold text-amber-800 mb-1">Analisis cepat</div>
                      <ul className="text-sm text-amber-900 list-disc ml-5 space-y-1">
                        <li>Total biaya per batch {formatRp(biayaPerBatch)} untuk {Math.round(qtyPerBatchTotal)} unit. HPP rata-rata {formatRp(hppUnitBatch)} per unit.</li>
                        <li>Dengan harga jual saat ini, revenue per batch {formatRp(Math.round(revenuePerBatch))} dan laba {formatRp(profitPerBatch)} ({(Math.round(marginPct * 10) / 10).toFixed(1)}%).</li>
                        <li>Pertimbangkan optimasi batch (x{batch}) dan paket produk untuk meningkatkan margin.</li>
                      </ul>
                    </div>

                    <div className="rounded-xl bg-slate-50 p-4">
                      <div className="text-sm font-semibold text-gray-800 mb-2">Detail rincian biaya per batch</div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="font-medium text-gray-700 mb-1">Bahan</div>
                          <ul className="space-y-1">
                            {ptBahan.map((b) => (
                              <li key={b.id} className="flex items-center justify-between">
                                <span>{b.nama || 'Bahan'}</span>
                                <span className="font-semibold text-gray-900">{formatRp(Math.round(parseNumber(b.biaya) / batch))}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <div className="font-medium text-gray-700 mb-1">Pengolahan</div>
                          <ul className="space-y-1">
                            {ptOlah.map((o) => {
                              const h = parseNumber(o.harga);
                              const perBatch = o.periode === 'per-batch' ? h : o.periode === 'per-bulan' ? h / batch : o.periode === 'per-minggu' ? (h * 4) / batch : (h * 30) / batch;
                              return (
                                <li key={o.id} className="flex items-center justify-between">
                                  <span>{o.nama || 'Biaya olah'}</span>
                                  <span className="font-semibold text-gray-900">{formatRp(Math.round(perBatch))}</span>
                                </li>
                              );
                            })}
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="rounded-xl border border-gray-200 bg-white p-4">
                      <div className="text-sm font-semibold text-gray-800 mb-2">Detail HPP per produk turunan</div>
                      <div className="space-y-2 text-sm">
                        {ptProduk.map((p) => {
                          const qtyB = parseNumber(p.qty) / batch;
                          const hppTotal = Math.round(hppUnitBatch * qtyB);
                          const revenue = Math.round(parseNumber(p.hargaJual) * qtyB);
                          const profit = revenue - hppTotal;
                          const margin = revenue > 0 ? (profit / revenue) * 100 : 0;
                          return (
                            <div key={p.id} className="rounded-lg border border-gray-100 bg-gray-50 p-3">
                              <div className="font-medium text-gray-800">{p.nama || 'Produk turunan'}</div>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-1">
                                <div>Qty/Batch: <span className="font-semibold text-gray-900">{Math.round(qtyB)}</span></div>
                                <div>HPP/Unit: <span className="font-semibold text-gray-900">{formatRp(hppUnitBatch)}</span></div>
                                <div>HPP Total/Batch: <span className="font-semibold text-gray-900">{formatRp(hppTotal)}</span></div>
                                <div>Harga/Unit: <span className="font-semibold text-gray-900">{formatRp(parseNumber(p.hargaJual))}</span></div>
                                <div>Revenue/Batch: <span className="font-semibold text-gray-900">{formatRp(revenue)}</span></div>
                                <div>Laba/Batch: <span className="font-semibold text-gray-900">{formatRp(profit)}</span></div>
                                <div>Margin: <span className="font-semibold text-gray-900">{(Math.round(margin * 10) / 10).toFixed(1)}%</span></div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    <div className="flex flex-wrap items-center gap-3">
                      <button onClick={() => setShowSaveCalc(true)} className="inline-flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50"><Save className="w-4 h-4" /> Simpan Perhitungan</button>
                      <button onClick={exportPtXlsx} className="inline-flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50"><Download className="w-4 h-4" /> Export .xlsx</button>
                    </div>
                  </>
                );
              })()}
            </div>
          )}

          {!result && (model === 'ritel-fnb' || model === 'manufaktur' || model === 'produksi-turunan' || model === 'produk-jasa') && (
            <div className="text-sm text-gray-600">Isi data di bawah, lalu klik "Hitung HPP & Saran Harga" untuk melihat hasil.</div>
          )}
        </Card>
      </div>

      {/* Input Data 100% width */}
      <Card title="Input Data">
        <p className="text-sm text-gray-500 mb-4">Lengkapi data produk dan biaya iklan di bawah untuk menghitung HPP, margin, dan saran harga.</p>

        {/* IKLAN & COD */}
        {model === 'iklan-cod' && (
          <div className="space-y-4">
            <div className="space-y-3">
              {adProducts.map((row, idx) => (
                <div key={row.id} className="grid grid-cols-1 md:grid-cols-5 gap-3 items-center">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Nama produk</label>
                    <input value={row.nama} onChange={(e) => setAdProducts((r) => r.map((x) => x.id === row.id ? { ...x, nama: e.target.value } : x))} placeholder="Contoh: Serum A" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">HPP /pcs (Rp)</label>
                    <input value={row.hpp} onChange={(e) => setAdProducts((r) => r.map((x) => x.id === row.id ? { ...x, hpp: e.target.value } : x))} placeholder="cth: 12000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Harga jual (Rp)</label>
                    <input value={row.harga} onChange={(e) => setAdProducts((r) => r.map((x) => x.id === row.id ? { ...x, harga: e.target.value } : x))} placeholder="cth: 25000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Qty terjual</label>
                    <input value={row.qty} onChange={(e) => setAdProducts((r) => r.map((x) => x.id === row.id ? { ...x, qty: e.target.value } : x))} placeholder="cth: 10" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>
                  <div className="flex gap-2 self-end">
                    {adProducts.length > 1 && (
                      <button onClick={() => setAdProducts((r) => r.filter((x) => x.id !== row.id))} className="px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50">Hapus</button>
                    )}
                    {idx === adProducts.length - 1 && (
                      <button onClick={() => setAdProducts((r) => [...r, { id: crypto.randomUUID(), nama: '', hpp: '', harga: '', qty: '' }])} className="inline-flex items-center gap-2 px-3 py-2 bg-gray-900 text-white rounded-lg"><Plus className="w-4 h-4" /> Tambah</button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="pt-4 mt-2 border-t border-gray-200 grid grid-cols-1 md:grid-cols-3 gap-3">
              <div>
                <label className="text-sm font-medium text-gray-700">Total biaya iklan (Rp)</label>
                <input value={adTotalIklan} onChange={(e) => setAdTotalIklan(e.target.value)} placeholder="cth: 2000000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Biaya admin iklan (%)</label>
                <input value={adAdminPct} onChange={(e) => setAdAdminPct(e.target.value)} placeholder="cth: 5" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Estimasi return (%)</label>
                <input value={adReturnPct} onChange={(e) => setAdReturnPct(e.target.value)} placeholder="cth: 3" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
              </div>
            </div>
          </div>
        )}

        {/* MARKETPLACE */}
        {model === 'marketplace' && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">HPP per produk (Rp)</label>
                  <div className="flex gap-2 mt-1">
                    <input value={mpHppPerProduk} onChange={(e) => setMpHppPerProduk(e.target.value)} placeholder="cth: 12000" className="flex-1 border border-gray-200 rounded-lg px-3 py-2" />
                    <button onClick={() => { setCalcResult(null); setShowHppCalc(true); }} className="inline-flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50"><Calculator className="w-4 h-4" /> Kalkulator</button>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Harga jual (Rp)</label>
                  <input value={mpHargaJual} onChange={(e) => setMpHargaJual(e.target.value)} placeholder="cth: 25000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 flex items-center gap-1">Komisi marketplace (%) <Info size={14} className="text-gray-400" /></label>
                  <input value={mpKomisiPct} onChange={(e) => setMpKomisiPct(e.target.value)} placeholder="cth: 5" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Biaya layanan (%)</label>
                  <input value={mpLayananPct} onChange={(e) => setMpLayananPct(e.target.value)} placeholder="cth: 2" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">Platform</label>
                  <select value={mpPlatform} onChange={(e) => setMpPlatform(e.target.value as any)} className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2">
                    <option value="lainnya">Lainnya</option>
                    <option value="shopee">Shopee</option>
                    <option value="tiktok">TikTok Shop</option>
                  </select>
                  {mpPlatform === 'shopee' && (
                    <div className="mt-1 text-xs text-gray-600">Biaya Proses Pesanan — Rp 1.250</div>
                  )}

                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Biaya program (Rp)</label>
                  <input value={mpProgramRp} onChange={(e) => setMpProgramRp(e.target.value)} placeholder="cth: 1000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 flex items-center gap-1">Komisi AMS (%) <Info size={14} className="text-gray-400" /></label>
                  <input value={mpAmsPct} onChange={(e) => setMpAmsPct(e.target.value)} placeholder="cth: 3" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                </div>
              </div>
            </div>
          </div>
        )}



        {/* ANALISIS CEPAT */}
        {model === 'analisis-cepat' && (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700">Nama produk</label>
              <input value={qcNama} onChange={(e) => setQcNama(e.target.value)} placeholder="Nama produk" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Harga jual (Rp)</label>
              <input value={qcHarga} onChange={(e) => setQcHarga(e.target.value)} placeholder="Harga jual (Rp)" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Gambar produk (opsional)</label>
              <div className="mt-1"><FileDropzone value={qcImg} onChange={setQcImg} accept="image/*" /></div>
            </div>
            {qcError ? (
              <div className="text-xs text-red-700 bg-red-50 border border-red-200 rounded p-2">{qcError}</div>
            ) : null}
            <button onClick={runQuickAnalysis} disabled={qcLoading} className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50">
              {qcLoading ? (<span className="inline-flex items-center gap-2"><Loader2 className="w-4 h-4 animate-spin" /> Memproses...</span>) : (<span className="inline-flex items-center gap-2"><Play className="w-4 h-4" /> Jalankan Analisis Cepat</span>)}
            </button>
          </div>
        )}

        {/* RITEL / F&B */}
        {model === 'ritel-fnb' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <label className="text-sm font-medium text-gray-700">Nama produk</label>
                <input value={vfNama} onChange={(e) => setVfNama(e.target.value)} placeholder="Nama produk" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Kategori</label>
                <select value={vfKategori} onChange={(e) => setVfKategori(e.target.value)} className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2">
                  <option value="fashion">Fashion</option>
                  <option value="makanan">Makanan & Minuman</option>
                  <option value="kerajinan">Kerajinan</option>
                  <option value="lainnya">Lainnya</option>
                </select>
              </div>
              <div className="md:col-span-2">
                <label className="text-sm font-medium text-gray-700">Gambar produk (opsional)</label>
                <div className="mt-1"><FileDropzone value={vfImg} onChange={setVfImg} accept="image/*" /></div>
              </div>
            </div>

            {/* Tombol AI di antara gambar produk dan biaya variabel */}
            <div className="flex md:justify-end">
              <div className="w-full md:w-auto">
                {vfAiError ? (
                  <div className="mb-2 text-xs text-red-700 bg-red-50 border border-red-200 rounded p-2">{vfAiError}</div>
                ) : null}
                <button onClick={runVfAiAnalysis} disabled={vfAiLoading} className="w-full md:w-auto inline-flex items-center gap-2 px-3 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 disabled:opacity-50">
                  {vfAiLoading ? (<><Loader2 className="w-4 h-4 animate-spin" /><span>Memproses...</span></>) : (<><Wand2 className="w-4 h-4" /><span>Bantu Analisis HPP dengan AI</span></>)}
                </button>
              </div>
            </div>


            <div>
              <div className="font-semibold text-gray-800 mb-2">Biaya Variabel</div>
              <div className="space-y-3">


                {vfVarCosts.map((v, idx) => (
                  <div key={v.id} className="grid grid-cols-1 md:grid-cols-12 gap-3 items-center">
                    <div className="md:col-span-3">
                      <label className="text-sm font-medium text-gray-700">Nama Bahan</label>
                      <input value={v.bahan} onChange={(e) => setVfVarCosts((r) => r.map((x) => x.id === v.id ? { ...x, bahan: e.target.value } : x))} placeholder="Nama Bahan" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />


                    </div>
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-gray-700">Jml Pakai</label>
                      <input value={v.pakaiQty} onChange={(e) => setVfVarCosts((r) => r.map((x) => x.id === v.id ? { ...x, pakaiQty: e.target.value } : x))} placeholder="Jml Pakai" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                    </div>
                    <div className="md:col-span-1">
                      <label className="text-sm font-medium text-gray-700">Satuan</label>
                      <select value={v.pakaiUnit} onChange={(e) => setVfVarCosts((r) => r.map((x) => x.id === v.id ? { ...x, pakaiUnit: e.target.value as Unit } : x))} className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2">
                        {['g', 'kg', 'ml', 'l', 'pcs', 'buah', 'lembar'].map(u => <option key={u} value={u}>{u}</option>)}
                      </select>
                    </div>
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-gray-700">Total Harga</label>
                      <input value={v.totalHarga} onChange={(e) => setVfVarCosts((r) => r.map((x) => x.id === v.id ? { ...x, totalHarga: e.target.value } : x))} placeholder="Total Harga" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                    </div>
                    <div className="md:col-span-1">
                      <label className="text-sm font-medium text-gray-700">Jml Beli</label>
                      <input value={v.beliQty} onChange={(e) => setVfVarCosts((r) => r.map((x) => x.id === v.id ? { ...x, beliQty: e.target.value } : x))} placeholder="Jml Beli" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                    </div>
                    <div className="md:col-span-1">
                      <label className="text-sm font-medium text-gray-700">Satuan</label>
                      <select value={v.beliUnit} onChange={(e) => setVfVarCosts((r) => r.map((x) => x.id === v.id ? { ...x, beliUnit: e.target.value as Unit } : x))} className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2">
                        {['kg', 'g', 'l', 'ml', 'pcs', 'buah', 'lembar'].map(u => <option key={u} value={u}>{u}</option>)}
                      </select>
                    </div>
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-gray-700">Biaya per Produk (Rp)</label>
                      <div className="mt-1 flex items-center gap-2">
                        <input value={String(varCostPerProduk(v))} readOnly className="w-full border border-gray-200 rounded-lg px-3 py-2 bg-gray-50 flex-1" />
                        {vfVarCosts.length > 1 && (
                          <button onClick={() => setVfVarCosts((r) => r.filter((x) => x.id !== v.id))} className="px-2 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 text-xs">Hapus</button>
                        )}
                        {idx === vfVarCosts.length - 1 && (
                          <button onClick={() => setVfVarCosts((r) => [...r, { id: crypto.randomUUID(), bahan: '', pakaiQty: '', pakaiUnit: 'g', totalHarga: '', beliQty: '', beliUnit: 'kg' }])} className="inline-flex items-center gap-2 px-2 py-2 bg-gray-900 text-white rounded-lg text-xs"><Plus className="w-3 h-3" /> Tambah</button>
                        )}
                      </div>
                    </div>




















                  </div>
                ))}


              </div>
            </div>

            <div className="space-y-3">
              <div className="font-semibold text-gray-800">Alokasi Biaya Tetap per Produk</div>
              <div className="p-3 md:p-4 border border-teal-200 rounded-lg bg-teal-50/50">
                <label className="text-sm font-medium text-gray-700">Jumlah Target Penjualan per Bulan</label>
                <input value={vfTarget} onChange={(e) => setVfTarget(e.target.value)} placeholder="1000" className="mt-1 w-full border border-teal-200 rounded-lg px-3 py-2 bg-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500" />
                <div className="text-[12px] text-gray-500 mt-1">Digunakan untuk memberi saran alokasi biaya yang ideal.</div>
              </div>


              {vfFixCosts.map((f, idx) => (
                <div key={f.id} className="grid grid-cols-1 md:grid-cols-4 gap-3 items-center">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Nama biaya</label>
                    <input value={f.nama} onChange={(e) => setVfFixCosts((r) => r.map((x) => x.id === f.id ? { ...x, nama: e.target.value } : x))} placeholder="Nama biaya" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Total biaya (per bulan)</label>
                    <input value={f.totalBulan} onChange={(e) => setVfFixCosts((r) => r.map((x) => x.id === f.id ? { ...x, totalBulan: e.target.value } : x))} placeholder="Total biaya (per bulan)" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Alokasi per produk (Rp)</label>
                    <input value={f.alokasi || ''} onChange={(e) => setVfFixCosts((r) => r.map((x) => x.id === f.id ? { ...x, alokasi: e.target.value } : x))} placeholder={`Saran: ${formatRp(Math.round(parseNumber(f.totalBulan) / Math.max(1, parseNumber(vfTarget))))}`} className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                  </div>

                  <div className="flex gap-2 self-end">
                    {vfFixCosts.length > 1 && (
                      <button onClick={() => setVfFixCosts((r) => r.filter((x) => x.id !== f.id))} className="px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50">Hapus</button>
                    )}
                    {idx === vfFixCosts.length - 1 && (
                      <button onClick={() => setVfFixCosts((r) => [...r, { id: crypto.randomUUID(), nama: '', totalBulan: '', alokasi: '' }])} className="inline-flex items-center gap-2 px-3 py-2 bg-gray-900 text-white rounded-lg"><Plus className="w-4 h-4" /> Tambah</button>
                    )}
                  </div>
                </div>
              ))}
              <div className="flex flex-wrap items-center gap-3">
                <button onClick={() => setShowPresetModal(true)} className="inline-flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50"><Save className="w-4 h-4" /> Simpan & Gunakan Preset</button>
                {presets.length > 0 && (
                  <select onChange={(e) => {
                    const id = Number(e.target.value);
                    const p = presets.find((x) => x.id === id);
                    if (p) applyPreset(p);
                  }} defaultValue="" className="border border-gray-200 rounded-lg px-3 py-2">
                    <option value="">Gunakan preset tersimpan...</option>
                    {presets.map((p) => <option key={p.id} value={p.id}>{p.name}</option>)}
                  </select>
                )}
                <button onClick={() => { setVfPriceSuggests([]); setVfPriceSuggestLoading(false); setVfChosenPrice(''); setVfTargetProfit(''); setResult({ type: 'ritel-fnb', hpp: vfSummary.hpp }); }} className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"><Calculator className="w-4 h-4" /> Hitung HPP & Saran Harga</button>
              </div>
            </div>
          </div>
        )
        }

        {/* MANUFAKTUR / PABRIK */}
        {
          model === 'manufaktur' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 items-end">
                <div>
                  <label className="text-sm font-medium text-gray-700">Nama produk</label>
                  <input value={mfNama} onChange={(e) => setMfNama(e.target.value)} placeholder="Nama produk" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Kategori produk</label>
                  <select value={mfKategori} onChange={(e) => setMfKategori(e.target.value as any)} className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2">
                    <option value="fashion">Fashion</option>
                    <option value="elektronik">Elektronik</option>
                    <option value="perabotan">Perabotan</option>
                    <option value="lainnya">Lainnya</option>
                  </select>
                </div>
              </div>
              <div className="flex md:justify-end">
                <button onClick={runMfAiAnalysis} disabled={mfAiLoading} className="w-full md:w-auto inline-flex items-center gap-2 px-3 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 disabled:opacity-50">
                  {mfAiLoading ? (<><Loader2 className="w-4 h-4 animate-spin" /><span>Memproses...</span></>) : (<><Wand2 className="w-4 h-4" /><span>Bantu Analisis HPP dengan AI</span></>)}
                </button>
              </div>
              {mfAiError ? (<div className="mt-2 text-xs text-red-700 bg-red-50 border border-red-200 rounded p-2">{mfAiError}</div>) : null}

              <div>
                <div className="font-semibold text-gray-800 mb-2">Biaya Bahan Baku Langsung (per bulan)</div>
                <div className="space-y-3">
                  {mfBahan.map((b, idx) => (
                    <div key={b.id} className="grid grid-cols-1 md:grid-cols-3 gap-3 items-center">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Nama bahan</label>
                        <input value={b.nama} onChange={(e) => setMfBahan((r) => r.map((x) => x.id === b.id ? { ...x, nama: e.target.value } : x))} placeholder="Nama bahan" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Total biaya (per bulan)</label>
                        <input value={b.totalBulan} onChange={(e) => setMfBahan((r) => r.map((x) => x.id === b.id ? { ...x, totalBulan: e.target.value } : x))} placeholder="cth: 2.000.000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div className="flex gap-2 self-end">
                        {mfBahan.length > 1 && (
                          <button onClick={() => setMfBahan((r) => r.filter((x) => x.id !== b.id))} className="px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50">Hapus</button>
                        )}
                        {idx === mfBahan.length - 1 && (
                          <button onClick={() => setMfBahan((r) => [...r, { id: crypto.randomUUID(), nama: '', totalBulan: '' }])} className="inline-flex items-center gap-2 px-3 py-2 bg-gray-900 text-white rounded-lg"><Plus className="w-4 h-4" /> Tambah</button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <div className="font-semibold text-gray-800 mb-2">Biaya Tenaga Kerja Langsung (per bulan)</div>
                <div className="space-y-3">
                  {mfTkl.map((t, idx) => (
                    <div key={t.id} className="grid grid-cols-1 md:grid-cols-3 gap-3 items-center">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Jenis pekerjaan</label>
                        <input value={t.jenis} onChange={(e) => setMfTkl((r) => r.map((x) => x.id === t.id ? { ...x, jenis: e.target.value } : x))} placeholder="Contoh: Operator mesin" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Total upah (per bulan)</label>
                        <input value={t.totalUpah} onChange={(e) => setMfTkl((r) => r.map((x) => x.id === t.id ? { ...x, totalUpah: e.target.value } : x))} placeholder="cth: 5.000.000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div className="flex gap-2 self-end">
                        {mfTkl.length > 1 && (
                          <button onClick={() => setMfTkl((r) => r.filter((x) => x.id !== t.id))} className="px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50">Hapus</button>
                        )}
                        {idx === mfTkl.length - 1 && (
                          <button onClick={() => setMfTkl((r) => [...r, { id: crypto.randomUUID(), jenis: '', totalUpah: '' }])} className="inline-flex items-center gap-2 px-3 py-2 bg-gray-900 text-white rounded-lg"><Plus className="w-4 h-4" /> Tambah</button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <div className="font-semibold text-gray-800 mb-2">Biaya Overhead Pabrik (per bulan)</div>
                <div className="space-y-3">
                  {mfOhp.map((o, idx) => (
                    <div key={o.id} className="grid grid-cols-1 md:grid-cols-3 gap-3 items-center">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Nama biaya overhead</label>
                        <input value={o.nama} onChange={(e) => setMfOhp((r) => r.map((x) => x.id === o.id ? { ...x, nama: e.target.value } : x))} placeholder="Contoh: Listrik pabrik" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Total biaya (per bulan)</label>
                        <input value={o.totalBulan} onChange={(e) => setMfOhp((r) => r.map((x) => x.id === o.id ? { ...x, totalBulan: e.target.value } : x))} placeholder="cth: 3.000.000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div className="flex gap-2 self-end">
                        {mfOhp.length > 1 && (
                          <button onClick={() => setMfOhp((r) => r.filter((x) => x.id !== o.id))} className="px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50">Hapus</button>
                        )}
                        {idx === mfOhp.length - 1 && (
                          <button onClick={() => setMfOhp((r) => [...r, { id: crypto.randomUUID(), nama: '', totalBulan: '' }])} className="inline-flex items-center gap-2 px-3 py-2 bg-gray-900 text-white rounded-lg"><Plus className="w-4 h-4" /> Tambah</button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Jumlah produk dihasilkan per bulan (pcs)</label>
                <input value={mfOutput} onChange={(e) => setMfOutput(e.target.value)} placeholder="cth: 1.000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
              </div>



              <div>
                <button onClick={() => { setMfPriceSuggests([]); setMfPriceSuggestLoading(false); setMfChosenPrice(''); setMfTargetProfit(''); setResult({ type: 'manufaktur', hpp: mfSummary.hpp }); }} className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"><Calculator className="w-4 h-4" /> Hitung HPP & Saran Harga</button>
              </div>
            </div>
          )
        }

        {/* PRODUKSI TURUNAN */}
        {
          model === 'produksi-turunan' && (
            <div className="space-y-6">
              <div className="space-y-2">
                <div>
                  <label className="text-sm font-medium text-gray-700">Nama bisnis / produk utama</label>
                  <input value={ptNama} onChange={(e) => setPtNama(e.target.value)} placeholder="Contoh: Minyak kelapa murni" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                </div>
                <div className="flex md:justify-end">
                  <button onClick={runPtAiAnalysis} disabled={ptAiLoading} className="inline-flex items-center gap-2 w-full md:w-auto px-3 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 disabled:opacity-60">
                    {ptAiLoading ? (<><Loader2 className="w-4 h-4 animate-spin" /><span>Memproses...</span></>) : (<><Wand2 className="w-4 h-4" /><span>Bantu Analisis HPP dengan AI</span></>)}
                  </button>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">
                  Jumlah batch produksi per bulan
                  <button type="button" onClick={() => setPtShowBatchInfo(true)} className="ml-2 inline-flex items-center justify-center w-4 h-4 text-[10px] font-semibold rounded-full bg-gray-200 text-gray-700 align-middle hover:bg-gray-300">?</button>
                </label>
                <input value={ptBatch} onChange={(e) => setPtBatch(e.target.value)} placeholder="cth: 4" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
              </div>

              <div>
                <div className="font-semibold text-gray-800 mb-2">Bahan baku utama</div>
                <div className="space-y-3">
                  {ptBahan.map((b) => (
                    <div key={b.id} className="grid grid-cols-1 md:grid-cols-5 gap-3 items-center">
                      <div className="md:col-span-2">
                        <label className="text-sm font-medium text-gray-700">Nama bahan</label>
                        <input value={b.nama} onChange={(e) => setPtBahan((r) => r.map((x) => x.id === b.id ? { ...x, nama: e.target.value } : x))} placeholder="Contoh: Kelapa" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Total biaya (Rp)</label>
                        <input value={b.biaya} onChange={(e) => setPtBahan((r) => r.map((x) => x.id === b.id ? { ...x, biaya: e.target.value } : x))} placeholder="cth: 2.000.000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Jumlah</label>
                        <input value={b.jumlah} onChange={(e) => setPtBahan((r) => r.map((x) => x.id === b.id ? { ...x, jumlah: e.target.value } : x))} placeholder="cth: 100" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Satuan</label>
                        <input value={b.satuan} onChange={(e) => setPtBahan((r) => r.map((x) => x.id === b.id ? { ...x, satuan: e.target.value } : x))} placeholder="cth: kg, pcs, liter" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>

                    </div>
                  ))}
                </div>
              </div>

              <div>
                <div className="font-semibold text-gray-800 mb-2">Biaya pengolahan</div>
                <div className="space-y-3">
                  {ptOlah.map((o, idx) => (
                    <div key={o.id} className="grid grid-cols-1 md:grid-cols-5 gap-3 items-center">
                      <div className="md:col-span-2">
                        <label className="text-sm font-medium text-gray-700">Nama biaya</label>
                        <input value={o.nama} onChange={(e) => setPtOlah((r) => r.map((x) => x.id === o.id ? { ...x, nama: e.target.value } : x))} placeholder="Contoh: Tenaga olah" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Harga (Rp)</label>
                        <input value={o.harga} onChange={(e) => setPtOlah((r) => r.map((x) => x.id === o.id ? { ...x, harga: e.target.value } : x))} placeholder="cth: 1.000.000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Periode</label>
                        <select value={o.periode} onChange={(e) => setPtOlah((r) => r.map((x) => x.id === o.id ? { ...x, periode: e.target.value as any } : x))} className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2">
                          <option value="per-batch">per batch</option>
                          <option value="per-bulan">per bulan</option>
                          <option value="per-minggu">per minggu</option>
                          <option value="per-hari">per hari</option>
                        </select>
                      </div>
                      <div className="flex gap-2 self-end">
                        {ptOlah.length > 1 && (
                          <button onClick={() => setPtOlah((r) => r.filter((x) => x.id !== o.id))} className="px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50">Hapus</button>
                        )}
                        {idx === ptOlah.length - 1 && (
                          <button onClick={() => setPtOlah((r) => [...r, { id: crypto.randomUUID(), nama: '', harga: '', periode: 'per-bulan' }])} className="inline-flex items-center gap-2 px-3 py-2 bg-gray-900 text-white rounded-lg self-end"><Plus className="w-4 h-4" /> Tambah</button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <div className="font-semibold text-gray-800 mb-2">Produk turunan</div>
                <div className="space-y-3">
                  {ptProduk.map((p, idx) => (
                    <div key={p.id} className="grid grid-cols-1 md:grid-cols-5 gap-3 items-center">
                      <div className="md:col-span-2">
                        <label className="text-sm font-medium text-gray-700">Nama produk jadi</label>
                        <input value={p.nama} onChange={(e) => setPtProduk((r) => r.map((x) => x.id === p.id ? { ...x, nama: e.target.value } : x))} placeholder="Contoh: Minyak kemasan 250ml" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Qty</label>
                        <input value={p.qty} onChange={(e) => setPtProduk((r) => r.map((x) => x.id === p.id ? { ...x, qty: e.target.value } : x))} placeholder="cth: 100" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Satuan</label>
                        <select value={p.satuan} onChange={(e) => setPtProduk((r) => r.map((x) => x.id === p.id ? { ...x, satuan: e.target.value as any } : x))} className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2">
                          <option value="pcs">pcs</option>
                          <option value="kg">kg</option>
                        </select>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Harga jual / satuan (Rp)</label>
                        <input value={p.hargaJual} onChange={(e) => setPtProduk((r) => r.map((x) => x.id === p.id ? { ...x, hargaJual: e.target.value } : x))} placeholder="cth: 25.000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div className="flex gap-2">
                        {ptProduk.length > 1 && (
                          <button onClick={() => setPtProduk((r) => r.filter((x) => x.id !== p.id))} className="px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50">Hapus</button>
                        )}
                        {idx === ptProduk.length - 1 && (
                          <button onClick={() => setPtProduk((r) => [...r, { id: crypto.randomUUID(), nama: '', qty: '', satuan: 'pcs', hargaJual: '' }])} className="inline-flex items-center gap-2 px-3 py-2 bg-gray-900 text-white rounded-lg"><Plus className="w-4 h-4" /> Tambah</button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>



              <div>
                <button onClick={calcPt} className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"><Calculator className="w-4 h-4" /> Hitung HPP</button>
              </div>
            </div>
          )
        }



        {
          model === 'produk-jasa' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 items-end">
                <div>
                  <label className="text-sm font-medium text-gray-700">Nama jasa</label>
                  <input value={pjNama} onChange={(e) => setPjNama(e.target.value)} placeholder="Contoh: Jasa desain logo" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Kategori jasa</label>
                  <select value={pjKategori} onChange={(e) => setPjKategori(e.target.value as any)} className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2">
                    <option value="jasa-profesional">Jasa profesional</option>
                    <option value="jasa-kreatif">Jasa kreatif</option>
                    <option value="jasa-perawatan">Jasa perawatan</option>
                    <option value="lainnya">Lainnya</option>
                  </select>
                </div>
              </div>
              <div className="flex md:justify-end">
                <button onClick={runPjAiAnalysis} disabled={pjAiLoading} className="w-full md:w-auto inline-flex items-center gap-2 px-3 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 disabled:opacity-50">
                  {pjAiLoading ? (<><Loader2 className="w-4 h-4 animate-spin" /><span>Memproses...</span></>) : (<><Wand2 className="w-4 h-4" /><span>Bantu Analisis HPP dengan AI</span></>)}
                </button>
              </div>
              {pjAiError && <div className="text-sm text-red-600">{pjAiError}</div>}

              <div>
                <div className="font-semibold text-gray-800 mb-2">Biaya langsung (Direct cost)</div>
                <div className="space-y-3">
                  {pjDirect.map((d, idx) => (
                    <div key={d.id} className="grid grid-cols-1 md:grid-cols-3 gap-3 items-center">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Nama biaya</label>
                        <input value={d.nama} onChange={(e) => setPjDirect((r) => r.map((x) => x.id === d.id ? { ...x, nama: e.target.value } : x))} placeholder="Contoh: Bahan pendukung" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Harga (Rp)</label>
                        <input value={d.harga} onChange={(e) => setPjDirect((r) => r.map((x) => x.id === d.id ? { ...x, harga: e.target.value } : x))} placeholder="cth: 50.000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div className="flex gap-2 self-end">
                        {pjDirect.length > 1 && (
                          <button onClick={() => setPjDirect((r) => r.filter((x) => x.id !== d.id))} className="px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50">Hapus</button>
                        )}
                        {idx === pjDirect.length - 1 && (
                          <button onClick={() => setPjDirect((r) => [...r, { id: crypto.randomUUID(), nama: '', harga: '' }])} className="inline-flex items-center gap-2 px-3 py-2 bg-gray-900 text-white rounded-lg self-end"><Plus className="w-4 h-4" /> Tambah</button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <div className="font-semibold text-gray-800 mb-2">Biaya operasional (Operational cost)</div>
                <div className="space-y-3">
                  {pjOpex.map((o, idx) => (
                    <div key={o.id} className="grid grid-cols-1 md:grid-cols-4 gap-3 items-center">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Nama biaya</label>
                        <input value={o.nama} onChange={(e) => setPjOpex((r) => r.map((x) => x.id === o.id ? { ...x, nama: e.target.value } : x))} placeholder="Contoh: Sewa kantor" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Total biaya</label>
                        <input value={o.biaya} onChange={(e) => setPjOpex((r) => r.map((x) => x.id === o.id ? { ...x, biaya: e.target.value } : x))} placeholder="cth: 1.000.000" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Periode</label>
                        <select value={o.periode} onChange={(e) => setPjOpex((r) => r.map((x) => x.id === o.id ? { ...x, periode: e.target.value as any } : x))} className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2">
                          <option value="per-bulan">per bulan</option>
                          <option value="per-minggu">per minggu</option>
                          <option value="per-hari">per hari</option>
                        </select>
                      </div>
                      <div className="flex gap-2 self-end">
                        {pjOpex.length > 1 && (
                          <button onClick={() => setPjOpex((r) => r.filter((x) => x.id !== o.id))} className="px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50">Hapus</button>
                        )}
                        {idx === pjOpex.length - 1 && (
                          <button onClick={() => setPjOpex((r) => [...r, { id: crypto.randomUUID(), nama: '', biaya: '', periode: 'per-bulan' }])} className="inline-flex items-center gap-2 px-3 py-2 bg-gray-900 text-white rounded-lg self-end"><Plus className="w-4 h-4" /> Tambah</button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Jumlah target layanan per bulan</label>
                <input value={pjTarget} onChange={(e) => setPjTarget(e.target.value)} placeholder="cth: 100" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
              </div>



              <div>
                <button onClick={() => { setPjPriceSuggests([]); setPjPriceSuggestLoading(false); setPjChosenPrice(''); setPjTargetProfit(''); setResult({ type: 'produk-jasa', hpp: pjSummary.hpp }); }} className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"><Calculator className="w-4 h-4" /> Hitung HPP & Saran Harga</button>
              </div>
            </div>
          )
        }
      </Card >

      {/* Modals */}
      <Modal
        open={!!showAddEdit}
        onClose={() => setShowAddEdit(null)}
        title={showAddEdit?.mode === 'add' ? 'Buat Worksheet' : 'Edit Worksheet'}
        subtitle={'Masukkan nama untuk worksheet'}
        footer={
          <>
            <button onClick={() => setShowAddEdit(null)} className="px-3 py-2 rounded-lg border border-gray-200">Cancel</button>
            <button onClick={async () => {
              if (!showAddEdit) return;
              const val = showAddEdit.value.trim();
              if (!val) { setShowAddEdit(null); return; }
              try {
                if (showAddEdit.mode === 'add') {
                  const res = await fetch(`${API_BASE}/api/worksheets`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ name: val })
                  });
                  const data = await res.json();
                  if (!res.ok) throw new Error(data?.message || 'Gagal membuat worksheet');
                  setWorksheets((w) => [data, ...w]);
                  setSelectedWorksheetId(data.id);
                  // Bersihkan data saat membuat worksheet baru
                  resetWorksheetData();
                } else {
                  if (!selectedWorksheetId) return;
                  const res = await fetch(`${API_BASE}/api/worksheets/${selectedWorksheetId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ name: val })
                  });
                  const data = await res.json();
                  if (!res.ok) throw new Error(data?.message || 'Gagal memperbarui worksheet');
                  setWorksheets((w) => w.map((x) => (x.id === selectedWorksheetId ? data : x)));
                }
              } catch (e) {
                console.error(e);
              } finally {
                setShowAddEdit(null);
              }
            }} className="px-3 py-2 rounded-lg bg-green-600 text-white">Simpan</button>
          </>
        }
      >
        <input
          autoFocus
          value={showAddEdit?.value || ''}
          onChange={(e) => setShowAddEdit((s) => (s ? { ...s, value: e.target.value } : s))}
          placeholder="Contoh: Brand Skincare-ku"
          className="w-full border border-gray-200 rounded-lg px-3 py-2"
        />
      </Modal>

      <Modal
        open={!!showDelete}
        onClose={() => setShowDelete(null)}
        title="Hapus Worksheet?"
        subtitle={showDelete ? (worksheets.find((w) => w.id === showDelete)?.name || '') : ''}
        footer={
          <>
            <button onClick={() => setShowDelete(null)} className="px-3 py-2 rounded-lg border border-gray-200">Batal</button>
            <button onClick={async () => {
              const id = showDelete!;
              try {
                await fetch(`${API_BASE}/api/worksheets/${id}`, { method: 'DELETE' });
                setWorksheets((w) => w.filter((x) => x.id !== id));
                if (selectedWorksheetId === id) setSelectedWorksheetId(null);
              } catch (e) {
                console.error(e);
              } finally {
                setShowDelete(null);
              }
            }} className="px-3 py-2 rounded-lg bg-rose-600 text-white">Hapus</button>
          </>
        }
      >
        <div className="text-sm text-gray-600">Tindakan ini tidak dapat dibatalkan.</div>
      </Modal>

      <Modal
        open={showHppCalc}
        onClose={() => setShowHppCalc(false)}
        title="Hitung HPP"
        subtitle="Isi komponen biaya dan jumlah produksi"
        footer={
          <>
            <button onClick={() => setShowHppCalc(false)} className="px-3 py-2 rounded-lg border border-gray-200">Batal</button>
            <button onClick={applyHppCalc} className="px-3 py-2 rounded-lg bg-green-600 text-white">Hitung HPP</button>
          </>
        }
      >
        {[
          { key: 'bahan', label: 'Bahan baku' },
          { key: 'tenaga', label: 'Tenaga kerja' },
          { key: 'overhead', label: 'Overhead' },
          { key: 'kemasan', label: 'Kemasan' },
          { key: 'logistik', label: 'Logistik' },
          { key: 'tambahan', label: 'Tambahan' },
        ].map((f) => (
          <div key={f.key} className="relative">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
              {f.label}
              <button type="button" onClick={() => setHelpOpen((h) => ({ ...h, [f.key]: !h[f.key] }))}>
                <Info size={14} className="text-gray-400" />
              </button>
            </label>
            <input value={(calc as any)[f.key]} onChange={(e) => setCalc((c) => ({ ...c, [f.key]: e.target.value }))} placeholder="Rp" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
            {helpOpen[f.key] && (
              <div className="mt-1 inline-block text-xs bg-amber-50 border border-amber-200 text-amber-800 rounded px-2 py-1">
                Masukkan estimasi {f.label.toLowerCase()} total untuk batch produksi ini.
              </div>
            )}
          </div>
        ))}
        {calcResult && (
          <div className="mt-3 rounded-xl border border-green-200 bg-green-50 p-4">
            <div className="text-sm text-green-700">Hasil Perhitungan</div>
            <div className="text-sm text-gray-800">Total biaya: <span className="font-semibold">{formatRp(calcResult.total)}</span></div>
            <div className="mt-1 text-2xl font-semibold text-green-800">HPP per produk: {formatRp(calcResult.perProduk)}</div>
            <div className="mt-3">
              <button onClick={() => { setMpHppPerProduk(String(calcResult.perProduk)); setShowHppCalc(false); setCalcResult(null); }} className="px-3 py-2 rounded-lg bg-green-600 text-white hover:bg-green-700">Gunakan HPP Ini</button>
            </div>
          </div>
        )}

        <div>
          <label className="text-sm font-medium text-gray-700">Jumlah produksi (pcs)</label>
          <input value={calc.jumlah} onChange={(e) => setCalc((c) => ({ ...c, jumlah: e.target.value }))} placeholder="cth: 100" className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
        </div>
      </Modal>

      <Modal
        open={showPresetModal}
        onClose={() => setShowPresetModal(false)}
        title="Gunakan Preset"
        subtitle="Nama preset untuk disimpan"
        footer={
          <>
            <button onClick={() => setShowPresetModal(false)} className="px-3 py-2 rounded-lg border border-gray-200">Batal</button>
            <button onClick={savePreset} className="px-3 py-2 rounded-lg bg-green-600 text-white">Simpan</button>
          </>
        }
      >
        <div className="flex items-center gap-2">
          <input value={presetName} onChange={(e) => setPresetName(e.target.value)} placeholder="Nama preset" className="flex-1 border border-gray-200 rounded-lg px-3 py-2" />
        </div>
      </Modal>
      <Modal
        open={showHistory}
        onClose={() => setShowHistory(false)}
        title={`Riwayat Perhitungan (${model === 'manufaktur' ? 'Manufaktur/Pabrik' : 'Ritel/F&B'})`}
        subtitle="Terapkan kembali atau hapus riwayat yang tersimpan"
      >
        <div className="space-y-2">
          <button onClick={() => loadHistory(model)} className="px-3 py-1.5 border border-gray-200 rounded-lg hover:bg-gray-50">Reload</button>
          <div className="max-h-80 overflow-auto divide-y divide-gray-100">
            {historyItems.length === 0 ? (
              <div className="text-sm text-gray-600 py-4">Belum ada riwayat tersimpan.</div>
            ) : historyItems.map((it) => (
              <div key={it.id} className="py-2 flex items-center justify-between gap-3">
                <div className="min-w-0">
                  <div className="text-sm font-medium text-gray-800 truncate">{it.title}</div>
                  <div className="text-xs text-gray-500">{new Date(it.created_at).toLocaleString()}</div>
                </div>
                <div className="flex items-center gap-2">
                  <button onClick={() => applyHistory(it)} className="px-3 py-1.5 rounded-lg bg-green-600 text-white hover:bg-green-700">Terapkan</button>
                  <button onClick={() => deleteHistory(it.id)} className="px-3 py-1.5 rounded-lg border border-gray-200 hover:bg-gray-50">Hapus</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Modal>

      <Modal
        open={showSaveCalc}
        onClose={() => setShowSaveCalc(false)}
        title="Simpan Perhitungan"
        subtitle="Berikan nama agar mudah ditemukan di Riwayat"
        footer={
          <>
            <button onClick={() => setShowSaveCalc(false)} className="px-3 py-2 rounded-lg border border-gray-200">Batal</button>
            <button onClick={saveCalculation} className="px-3 py-2 rounded-lg bg-green-600 text-white">Simpan</button>
          </>
        }
      >
        <input value={saveCalcName} onChange={(e) => setSaveCalcName(e.target.value)} placeholder="Nama perhitungan..." className="mt-1 w-full border border-gray-200 rounded-lg px-3 py-2" />
      </Modal>

      <Modal
        open={ptShowBatchInfo}
        onClose={() => setPtShowBatchInfo(false)}
        title={'Apa itu "Jumlah Batch Produksi"?'}
        footer={
          <>
            <button onClick={() => setPtShowBatchInfo(false)} className="px-3 py-2 rounded-lg bg-gray-900 text-white">Mengerti</button>
          </>
        }
      >
        <div className="text-sm text-gray-700 space-y-3">
          <p>Field ini sangat penting untuk menghitung HPP secara akurat, karena fungsinya adalah untuk membagi rata biaya bulanan Anda (seperti sewa, listrik, gaji admin) ke setiap siklus produksi.</p>
          <div>
            <div className="font-semibold text-gray-800">Contoh Kasus: Usaha Ayam Potong</div>
            <p>Anggap satu "batch" adalah kegiatan Anda memproses satu truk ayam hidup (misal: 4.000 kg).</p>
            <p>Jika biaya sewa ruko Anda adalah Rp 3.000.000 / bulan dan dalam sebulan Anda melakukan 30 batch produksi (satu batch setiap hari), maka:</p>
            <p className="mt-1">Rp 3.000.000 / 30 batch = <span className="font-semibold">Rp 100.000 per batch</span></p>
            <p>Artinya, setiap satu batch produksi akan dibebani biaya sewa sebesar Rp 100.000. Metode ini memastikan semua biaya operasional bulanan Anda ikut terhitung dalam HPP produk, sehingga harga jual yang Anda tentukan lebih akurat dan tidak merugi.</p>
          </div>
        </div>
      </Modal>


    </div >
  );
}



