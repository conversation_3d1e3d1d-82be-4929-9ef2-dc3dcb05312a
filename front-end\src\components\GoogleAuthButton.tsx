import React, { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

const API_BASE = import.meta.env.VITE_API_BASE || 'http://127.0.0.1:8000';

interface GoogleAuthButtonProps {
  mode: 'login' | 'register';
  onSuccess?: () => void;
}

const GoogleAuthButton: React.FC<GoogleAuthButtonProps> = ({ mode, onSuccess }) => {
  const { setUser } = useAuth();

  const handleGoogleAuth = async () => {
    try {
      // Get redirect URL from backend
      const response = await fetch(`${API_BASE}/api/auth/google`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.redirect_url) {
        // Open Google OAuth in a popup window
        const popup = window.open(
          data.redirect_url,
          'google-auth',
          'width=500,height=600,scrollbars=yes,resizable=yes'
        );

        // Listen for popup to close or receive message
        const checkClosed = setInterval(() => {
          if (popup?.closed) {
            clearInterval(checkClosed);
            // Check if authentication was successful by checking URL params
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            const error = urlParams.get('error');

            if (error) {
              toast.error('Google Authentication Failed\n' + error);
            } else if (token) {
              // Handle successful authentication
              handleAuthSuccess(token);
            }
          }
        }, 1000);

        // Listen for messages from popup (alternative method)
        const messageListener = (event: MessageEvent) => {
          if (event.origin !== window.location.origin) return;

          if (event.data.type === 'GOOGLE_AUTH_SUCCESS') {
            clearInterval(checkClosed);
            popup?.close();
            handleAuthSuccess(event.data.token, event.data.user);
            window.removeEventListener('message', messageListener);
          } else if (event.data.type === 'GOOGLE_AUTH_ERROR') {
            clearInterval(checkClosed);
            popup?.close();
            toast.error('Google Authentication Failed\n' + event.data.error);
            window.removeEventListener('message', messageListener);
          }
        };

        window.addEventListener('message', messageListener);

      } else {
        console.error('Google auth failed:', data);
        toast.error('Failed to initialize Google authentication: ' + (data.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('Google auth error:', error);
      toast.error('Failed to connect to Google authentication service: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  const handleAuthSuccess = async (token: string, userData?: any) => {
    try {
      // Store token
      localStorage.setItem('auth_token', token);

      if (userData) {
        // Use provided user data
        setUser(userData);
      } else {
        // Fetch user data from backend
        const response = await fetch(`${API_BASE}/api/auth/me`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
        });

        const data = await response.json();
        if (data.success && data.user) {
          setUser(data.user);
        }
      }

      toast.success(
        mode === 'login'
          ? 'Login with Google successful!'
          : 'Registration with Google successful!'
      );

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Auth success handling error:', error);
      toast.error('Failed to complete authentication');
    }
  };

  // Handle URL params on component mount (for direct callback handling)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const error = urlParams.get('error');
    const userParam = urlParams.get('user');

    if (token) {
      try {
        const userData = userParam ? JSON.parse(decodeURIComponent(userParam)) : null;
        handleAuthSuccess(token, userData);

        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname);
      } catch (error) {
        console.error('Error parsing user data:', error);
        handleAuthSuccess(token);
      }
    } else if (error) {
      toast.error('Google Authentication Failed\n' + decodeURIComponent(error));
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  return (
    <button
      onClick={handleGoogleAuth}
      className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors"
    >
      <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
        <path
          fill="#4285F4"
          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
        />
        <path
          fill="#34A853"
          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
        />
        <path
          fill="#FBBC05"
          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
        />
        <path
          fill="#EA4335"
          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
        />
      </svg>
      {mode === 'login' ? 'Sign in with Google' : 'Sign up with Google'}
    </button>
  );
};

export default GoogleAuthButton;
