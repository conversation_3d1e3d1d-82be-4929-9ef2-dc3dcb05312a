import React, { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

const GoogleAuthCallback: React.FC = () => {
  const { setUser } = useAuth();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        const error = urlParams.get('error');
        const userParam = urlParams.get('user');

        if (error) {
          toast.error('Google Authentication Failed\n' + decodeURIComponent(error));
          // Redirect to login page
          window.location.href = '/';
          return;
        }

        if (token) {
          // Store token
          localStorage.setItem('auth_token', token);

          // Parse user data if provided
          let userData = null;
          if (userParam) {
            try {
              userData = JSON.parse(decodeURIComponent(userParam));
            } catch (e) {
              console.error('Error parsing user data:', e);
            }
          }

          if (userData) {
            // Use provided user data
            setUser(userData);
            toast.success('Google authentication successful!');
          } else {
            // Fetch user data from backend
            const API_BASE = import.meta.env.VITE_API_BASE || 'http://127.0.0.1:8000';
            const response = await fetch(`${API_BASE}/api/auth/me`, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
              },
            });

            const data = await response.json();
            if (data.success && data.user) {
              setUser(data.user);
              toast.success('Google authentication successful!');
            } else {
              throw new Error('Failed to fetch user data');
            }
          }

          // Send success message to parent window if in popup
          if (window.opener) {
            window.opener.postMessage({
              type: 'GOOGLE_AUTH_SUCCESS',
              token: token,
              user: userData
            }, window.location.origin);
            window.close();
            return;
          }

          // Redirect to dashboard
          window.location.href = '/dashboard';
        } else {
          throw new Error('No token received from Google authentication');
        }
      } catch (error) {
        console.error('Google auth callback error:', error);
        toast.error('Failed to complete Google authentication');
        
        // Send error message to parent window if in popup
        if (window.opener) {
          window.opener.postMessage({
            type: 'GOOGLE_AUTH_ERROR',
            error: error instanceof Error ? error.message : 'Unknown error'
          }, window.location.origin);
          window.close();
          return;
        }

        // Redirect to login page
        window.location.href = '/';
      }
    };

    handleCallback();
  }, [setUser]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Completing Authentication
          </h2>
          <p className="text-gray-600">
            Please wait while we complete your Google authentication...
          </p>
        </div>
      </div>
    </div>
  );
};

export default GoogleAuthCallback;
