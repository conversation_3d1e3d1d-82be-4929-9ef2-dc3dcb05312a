<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MarketingContentController extends Controller
{
    /**
     * Generate text content using Vertex AI
     */
    public function generateText(Request $request)
    {
        try {
            $validated = $request->validate([
                'contentType' => 'required|string|in:caption,email,ads-copy,blog,product-description',
                'product' => 'required|string|max:1000',
                'tone' => 'nullable|string',
                'platform' => 'nullable|string',
                'target' => 'nullable|string',
                'purpose' => 'nullable|string',
                'additionalContext' => 'nullable|string',
                'url' => 'nullable|url|max:500',
            ]);

            $contentType = $validated['contentType'];
            $product = $validated['product'];
            $tone = $validated['tone'] ?? 'professional';
            $platform = $validated['platform'] ?? '';
            $target = $validated['target'] ?? '';
            $purpose = $validated['purpose'] ?? '';
            $additionalContext = $validated['additionalContext'] ?? '';
            $url = $validated['url'] ?? null;

            // Get Vertex AI credentials
            $projectId = env('VERTEX_AI_PROJECT_ID');
            $location = env('VERTEX_AI_LOCATION', 'us-central1');
            $model = env('VERTEX_AI_MODEL', 'gemini-1.5-flash');

            if (!$projectId) {
                return response()->json(['error' => 'Vertex AI project ID not configured'], 500);
            }

            // Build prompt based on content type
            $prompt = $this->buildPrompt($contentType, $product, $tone, $platform, $target, $purpose, $additionalContext, $url);

            // Call Vertex AI
            $result = $this->callVertexAI($projectId, $location, $model, $prompt);

            // Parse and format response based on content type
            $formattedResult = $this->formatResponse($contentType, $result);

            return response()->json($formattedResult);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'error' => 'Validation failed',
                'details' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Marketing content generation failed: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to generate content',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Build prompt based on content type
     */
    private function buildPrompt($contentType, $product, $tone, $platform, $target, $purpose, $additionalContext, $url = null)
    {
        // Add URL instruction if provided
        $urlInstruction = '';
        if ($url) {
            $urlInstruction = "\n\nPENTING: Kunjungi dan pelajari website berikut untuk memahami brand voice, tone, dan style mereka:\nWebsite: {$url}\n\nGunakan insight dari website tersebut untuk membuat konten yang lebih relevan dan kontekstual. Pelajari:\n- Brand voice dan tone of voice\n- Messaging dan value proposition\n- Style penulisan dan format\n- Target audience mereka\n\nNamun tetap buat konten yang UNIK dan ORIGINAL, jangan copy paste.";
        }

        $prompts = [
            'caption' => "Buatkan caption media sosial yang menarik untuk platform {$platform} dengan tone {$tone}.

Produk/Layanan: {$product}
" . ($additionalContext ? "Konteks tambahan: {$additionalContext}" : "") . "
{$urlInstruction}

Buatlah caption yang:
- Menarik perhatian di 3 detik pertama
- Maksimal 3-4 kalimat (tidak lebih dari 150 kata)
- Menggunakan emoji yang relevan (2-4 emoji)
- Menyertakan call-to-action yang jelas
- Sesuai dengan karakteristik platform {$platform}
- Menggunakan tone {$tone}
- Sertakan 3-5 hashtag yang relevan di akhir

PENTING: Pastikan caption LENGKAP dan TIDAK TERPOTONG!

Format output dalam JSON (pastikan valid JSON):
{
  \"caption\": \"caption lengkap dengan emoji dan hashtag\"
}",

            'email' => "Buatkan email marketing {$purpose} dengan tone {$tone}.

Produk/Layanan: {$product}
" . ($additionalContext ? "Konteks tambahan: {$additionalContext}" : "") . "
{$urlInstruction}

Buatlah email yang:
- Subject line yang menarik dan tidak spam
- Opening yang engaging
- Body yang persuasif dan informatif
- Call-to-action yang jelas
- Closing yang profesional
- Tone {$tone}

Format output dalam JSON:
{
  \"subject\": \"subject line email\",
  \"body\": \"isi email lengkap dengan paragraf yang terstruktur\"
}",

            'ads-copy' => "Buatkan copy iklan untuk platform {$platform}.

Produk/Layanan: {$product}
Target Audiens: {$target}
" . ($additionalContext ? "Konteks tambahan: {$additionalContext}" : "") . "
{$urlInstruction}

Buatlah ads copy yang:
- Headline yang powerful dan attention-grabbing
- Description yang persuasif dan benefit-focused
- Call-to-action yang compelling
- Sesuai dengan karakteristik platform {$platform}
- Menyasar target audiens: {$target}

Format output dalam JSON:
{
  \"headline\": \"headline iklan (max 40 karakter)\",
  \"description\": \"deskripsi iklan yang persuasif\",
  \"cta\": \"call to action yang compelling\"
}",

            'blog' => "Buatkan artikel blog dengan tone {$tone}.

Topik: {$product}
" . ($additionalContext ? "Konteks tambahan: {$additionalContext}" : "") . "

Buatlah artikel yang:
- Judul yang SEO-friendly dan menarik
- Intro yang engaging
- Body dengan struktur yang jelas (gunakan subheading)
- Informasi yang valuable dan actionable
- Kesimpulan yang kuat
- Tone {$tone}

Format output dalam JSON:
{
  \"title\": \"judul artikel\",
  \"content\": \"isi artikel lengkap dengan markdown formatting\"
}",

            'product-description' => "Buatkan deskripsi produk yang menarik dengan tone {$tone}.

Produk: {$product}
" . ($additionalContext ? "Konteks tambahan: {$additionalContext}" : "") . "

Buatlah deskripsi yang:
- Highlight fitur utama
- Jelaskan benefit untuk customer
- Gunakan power words yang persuasif
- Sertakan spesifikasi jika relevan
- Call-to-action yang subtle
- Tone {$tone}

Format output dalam JSON:
{
  \"shortDescription\": \"deskripsi singkat (1-2 kalimat)\",
  \"longDescription\": \"deskripsi lengkap dengan bullet points fitur dan benefit\"
}"
        ];

        return $prompts[$contentType] ?? $prompts['caption'];
    }

    /**
     * Call Vertex AI API
     */
    private function callVertexAI($projectId, $location, $model, $prompt)
    {
        // Get access token using gcloud auth
        $accessToken = $this->getAccessToken();

        if (!$accessToken) {
            throw new \Exception('Failed to get Vertex AI access token');
        }

        $endpoint = "https://{$location}-aiplatform.googleapis.com/v1/projects/{$projectId}/locations/{$location}/publishers/google/models/{$model}:generateContent";

        $response = Http::withToken($accessToken)
            ->timeout(60)
            ->post($endpoint, [
                'contents' => [
                    [
                        'role' => 'user',
                        'parts' => [
                            ['text' => $prompt]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.7,
                    'topP' => 0.95,
                    'topK' => 40,
                    'maxOutputTokens' => 8192,
                ],
                'safetySettings' => [
                    [
                        'category' => 'HARM_CATEGORY_HATE_SPEECH',
                        'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                    ],
                    [
                        'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                        'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                    ],
                    [
                        'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                        'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                    ],
                    [
                        'category' => 'HARM_CATEGORY_HARASSMENT',
                        'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                    ]
                ]
            ]);

        if (!$response->successful()) {
            Log::error('Vertex AI API error', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            throw new \Exception('Vertex AI API request failed: ' . $response->body());
        }

        $data = $response->json();

        // Extract text from Vertex AI response
        $text = $data['candidates'][0]['content']['parts'][0]['text'] ?? '';

        if (empty($text)) {
            throw new \Exception('Empty response from Vertex AI');
        }

        return $text;
    }

    /**
     * Get access token for Vertex AI
     */
    private function getAccessToken()
    {
        // Try to get from environment first (for manual token)
        $token = env('VERTEX_AI_ACCESS_TOKEN');

        if ($token) {
            return $token;
        }

        // Try using service account key file (RECOMMENDED)
        $keyFile = env('GOOGLE_APPLICATION_CREDENTIALS');
        if ($keyFile && file_exists($keyFile)) {
            try {
                $client = new \Google\Client();
                $client->setAuthConfig($keyFile);
                $client->addScope([
                    'https://www.googleapis.com/auth/cloud-platform',
                    'https://www.googleapis.com/auth/generative-language'
                ]);

                // Use service account credentials directly
                $token = $client->fetchAccessTokenWithAssertion();

                if (isset($token['access_token'])) {
                    Log::info('Successfully obtained access token from service account');
                    return $token['access_token'];
                }

                if (isset($token['error'])) {
                    Log::error('Service account token error: ' . $token['error']);
                }
            } catch (\Exception $e) {
                Log::error('Failed to get token from service account: ' . $e->getMessage());
                Log::error('Service account file: ' . $keyFile);
            }
        } else {
            Log::error('Service account key file not found: ' . $keyFile);
        }

        // Fallback to gcloud command (for development)
        try {
            $output = shell_exec('gcloud auth print-access-token 2>&1');
            $token = trim($output);

            if (!empty($token) && !str_contains($token, 'ERROR')) {
                return $token;
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get access token via gcloud: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Format response based on content type
     */
    private function formatResponse($contentType, $rawResponse)
    {
        // Clean response - remove markdown code blocks if present
        $cleanResponse = preg_replace('/```json\s*|\s*```/', '', $rawResponse);
        $cleanResponse = trim($cleanResponse);

        // Try to extract JSON from response
        $jsonMatch = [];
        if (preg_match('/\{[\s\S]*\}/', $cleanResponse, $jsonMatch)) {
            $jsonStr = $jsonMatch[0];

            // Try to fix incomplete JSON
            if (!str_ends_with($jsonStr, '}')) {
                // Find last complete field
                $lastComma = strrpos($jsonStr, ',');
                $lastQuote = strrpos($jsonStr, '"');

                if ($lastQuote > $lastComma) {
                    // Truncate at last complete quote and close JSON
                    $jsonStr = substr($jsonStr, 0, $lastQuote + 1) . '}';
                }
            }

            $decoded = json_decode($jsonStr, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                return $decoded;
            }
        }

        // Fallback: return raw response in appropriate format
        return match ($contentType) {
            'caption' => ['caption' => $cleanResponse],
            'email' => ['subject' => 'Generated Email', 'body' => $cleanResponse],
            'ads-copy' => ['headline' => '', 'description' => $cleanResponse, 'cta' => ''],
            'blog' => ['title' => 'Generated Article', 'content' => $cleanResponse],
            'product-description' => ['shortDescription' => '', 'longDescription' => $cleanResponse],
            default => ['content' => $cleanResponse]
        };
    }
}
