<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SubscriptionPackage;

class SubscriptionPackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $packages = [
            [
                'name' => 'Paket Mulai Jualan',
                'slug' => 'mulai-jualan',
                'description' => 'Paket starter untuk memulai bisnis online Anda',
                'price' => 69000,
                'duration_months' => 1,
                'duration_label' => 'per bulan',
                'features' => [
                    'Akses semua tools generator',
                    'Template marketing content',
                    'Kalkulator HPP otomatis',
                    'Support email',
                    'Tutorial lengkap'
                ],
                'badge' => null,
                'color' => 'blue',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Paket Serius Cuan',
                'slug' => 'serius-cuan',
                'description' => 'Paket terpopuler untuk bisnis yang serius berkembang',
                'price' => 149000,
                'duration_months' => 3,
                'duration_label' => 'per 3 bulan',
                'features' => [
                    'Semua fitur Paket Mulai Jualan',
                    'Ana<PERSON>is kompetitor',
                    'Peta cuan lokasi premium',
                    'Konsultasi bisnis 1-on-1',
                    'Template ads premium',
                    'Priority support'
                ],
                'badge' => 'Popular',
                'color' => 'emerald',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Paket Full Scale',
                'slug' => 'full-scale',
                'description' => 'Paket lengkap untuk bisnis skala besar',
                'price' => 249000,
                'duration_months' => 12,
                'duration_label' => 'per tahun',
                'features' => [
                    'Semua fitur Paket Serius Cuan',
                    'White-label solution',
                    'API access',
                    'Custom integrations',
                    'Dedicated account manager',
                    'Advanced analytics',
                    'Unlimited konsultasi'
                ],
                'badge' => 'Best Value',
                'color' => 'purple',
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($packages as $package) {
            SubscriptionPackage::updateOrCreate(
                ['slug' => $package['slug']],
                $package
            );
        }
    }
}
