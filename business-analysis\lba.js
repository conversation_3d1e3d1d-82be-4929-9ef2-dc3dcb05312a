#!/usr/bin/env node
/**
 * Location-Based Business Analysis (LBA)
 * - Node.js single file
 * - Uses OpenAI Responses API with Structured Outputs (JSON Schema)
 * - Validates the model output with Ajv
 *
 * Usage:
 *   1) npm i openai ajv dotenv
 *   2) echo "OPENAI_API_KEY=sk-..." > .env   // JANGAN commit ke git
 *   3) node lba.js --lat -7.9543379 --lng 112.5779014 --name "Karangwidoro, Dau" --radius 1 --modal sedang --prefer ritel,kuliner,jasa
 */

import 'dotenv/config';
import Ajv from 'ajv';
import { OpenAI } from 'openai';

// -------- CLI ARGS (sederhana) --------
const args = Object.fromEntries(
    process.argv.slice(2).reduce((acc, cur, i, arr) => {
        if (cur.startsWith('--')) {
            const key = cur.replace(/^--/, '');
            const val = (arr[i + 1] && !arr[i + 1].startsWith('--')) ? arr[i + 1] : true;
            acc.push([key, val]);
        }
        return acc;
    }, [])
);

const lat = parseFloat(args.lat ?? '-7.9543379');
const lng = parseFloat(args.lng ?? '112.5779014');
const lokasi_nama = String(args.name ?? 'Karangwidoro, Kec. Dau, Kab. Malang');
const radius_km = parseFloat(args.radius ?? '1');
const modal = String(args.modal ?? 'sedang'); // rendah|sedang|tinggi
const preferensi = String(args.prefer ?? 'ritel,kuliner,jasa')
    .split(',')
    .map(s => s.trim())
    .filter(Boolean);

// -------- OpenAI client --------
const client = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY, // ← ambil dari .env
});

// -------- SYSTEM + USER PROMPT --------
const systemPrompt = `
Kamu adalah analis location-based business. Tugasmu: menilai kelayakan ide bisnis di suatu titik peta Indonesia.
Jawab HANYA dalam JSON sesuai skema. Semua skor 0–100 (integer). Jika data tidak tersedia, jelaskan asumsi di "ringkasan.asumsi".
Bobot penilaian:
- permintaan_lokal(30), kompetisi(20), aksesibilitas(15), daya_beli(15), kecocokan_lingkungan(10), regulasi_risiko(10).
Hitung "skor_total" sebagai penjumlahan komponen ter-bobot (0–100).
Berikan 1–5 ide di "ide_bisnis" urut skor tertinggi.
Sertakan "estimasi" biaya awal dan balik modal dalam rentang.
`;

const userPayload = {
    koordinat: { lat, lng },
    lokasi_nama,
    radius_km,
    preferensi: {
        modal,
        tipe_bisnis_diutamakan: preferensi
    },
    batasan: { maks_ide: 5, output_bahasa: "id" }
};

const schema = {
    // $schema dihapus atau ganti ke draft-07:
    // $schema: "http://json-schema.org/draft-07/schema#",
    type: "object",
    additionalProperties: false,
    required: ["lokasi", "ringkasan", "ide_bisnis", "rekomendasi_final"],
    properties: {
        lokasi: {
            type: "object",
            additionalProperties: false,
            required: ["nama", "lat", "lng", "radius_km"],
            properties: {
                nama: { type: "string" },
                lat: { type: "number" },
                lng: { type: "number" },
                radius_km: { type: "number" }
            }
        },
        ringkasan: {
            type: "object",
            additionalProperties: false,
            required: ["karakter", "temuan_kunci", "asumsi"],
            properties: {
                karakter: { type: "string" },
                temuan_kunci: { type: "array", items: { type: "string" } },
                asumsi: { type: "array", items: { type: "string" } }
            }
        },
        ide_bisnis: {
            type: "array",
            minItems: 1,
            maxItems: 5,
            items: {
                type: "object",
                additionalProperties: false,
                required: [
                    "nama",
                    "skor_total",
                    "skor_komponen",
                    "alasan",
                    "risiko",
                    "langkah_awal",
                    "estimasi",
                    "evidence"
                ],
                properties: {
                    nama: { type: "string" },
                    skor_total: { type: "integer", minimum: 0, maximum: 100 },
                    skor_komponen: {
                        type: "object",
                        additionalProperties: false,
                        required: [
                            "permintaan_lokal",
                            "kompetisi",
                            "aksesibilitas",
                            "daya_beli",
                            "kecocokan_lingkungan",
                            "regulasi_risiko"
                        ],
                        properties: {
                            permintaan_lokal: { type: "integer", minimum: 0, maximum: 100 },
                            kompetisi: { type: "integer", minimum: 0, maximum: 100 },
                            aksesibilitas: { type: "integer", minimum: 0, maximum: 100 },
                            daya_beli: { type: "integer", minimum: 0, maximum: 100 },
                            kecocokan_lingkungan: { type: "integer", minimum: 0, maximum: 100 },
                            regulasi_risiko: { type: "integer", minimum: 0, maximum: 100 }
                        }
                    },
                    alasan: { type: "array", items: { type: "string" } },
                    risiko: { type: "array", items: { type: "string" } },
                    langkah_awal: { type: "array", items: { type: "string" } },
                    estimasi: {
                        type: "object",
                        additionalProperties: false,
                        required: ["biaya_awal_idr", "balik_modal_bulan"],
                        properties: {
                            biaya_awal_idr: {
                                type: "object",
                                additionalProperties: false,
                                required: ["min", "max"],
                                properties: {
                                    min: { type: "integer", minimum: 0 },
                                    max: { type: "integer", minimum: 0 }
                                }
                            },
                            balik_modal_bulan: {
                                type: "object",
                                additionalProperties: false,
                                required: ["min", "max"],
                                properties: {
                                    min: { type: "integer", minimum: 0 },
                                    max: { type: "integer", minimum: 0 }
                                }
                            }
                        }
                    },
                    evidence: { type: "array", items: { type: "string" } }
                }
            }
        },
        rekomendasi_final: {
            type: "object",
            additionalProperties: false,
            required: ["pilihan", "alasan", "metrik_keberhasilan_90_hari"],
            properties: {
                pilihan: { type: "string" },
                alasan: { type: "array", items: { type: "string" } },
                metrik_keberhasilan_90_hari: { type: "array", items: { type: "string" } }
            }
        }
    }
};

// Helper untuk menghitung ulang skor_total (sanity check)
function recomputeScore(komp) {
    const w = {
        permintaan_lokal: 30,
        kompetisi: 20,
        aksesibilitas: 15,
        daya_beli: 15,
        kecocokan_lingkungan: 10,
        regulasi_risiko: 10
    };
    const sum = Object.entries(w).reduce((acc, [k, wt]) => acc + (komp[k] * wt / 100), 0);
    return Math.round(sum);
}

(async () => {
    try {
        // ---- Responses API with Structured Outputs ----
        const response = await client.responses.create({
            model: "gpt-4o",
            input: [
                { role: "system", content: systemPrompt },
                { role: "user", content: JSON.stringify(userPayload, null, 2) }
            ],
            text: {
                format: {
                    type: "json_schema",
                    // ⬇️ 'name' HARUS di level yang sama dengan 'type'
                    name: "lba_schema",
                    strict: true,
                    schema // <- objek JSON Schema kamu
                }
            }
        });

        // Ambil text JSON
        const text = response.output_text ?? (response.output && response.output[0]?.content[0]?.text) ?? "";
        if (!text) {
            throw new Error("Model tidak mengembalikan teks JSON.");
        }

        // Parse + validasi
        const data = JSON.parse(text);
        const ajv = new Ajv({ allErrors: true, strict: false });
        const validate = ajv.compile(schema);
        const ok = validate(data);

        if (!ok) {
            console.error("❌ JSON tidak valid menurut skema:");
            console.error(validate.errors);
            // Tetap cetak raw untuk debugging
            console.log(JSON.stringify(data, null, 2));
            process.exit(2);
        }

        // Sanity-check: pastikan skor_total konsisten
        let corrected = false;
        for (const ide of data.ide_bisnis) {
            const recomputed = recomputeScore(ide.skor_komponen);
            if (ide.skor_total !== recomputed) {
                ide.skor_total = recomputed;
                corrected = true;
            }
        }
        if (corrected) {
            // urutkan ulang jika perlu
            data.ide_bisnis.sort((a, b) => b.skor_total - a.skor_total);
        }

        // Output final JSON
        console.log(JSON.stringify(data, null, 2));
    } catch (err) {
        console.error("Error:", err?.response?.data ?? err.message ?? err);
        process.exit(1);
    }
})();
