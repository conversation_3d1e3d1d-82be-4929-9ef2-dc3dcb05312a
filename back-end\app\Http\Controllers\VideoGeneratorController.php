<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\JsonResponse;

class VideoGeneratorController extends Controller
{
    /**
     * STEP 1: Generate narasi + konsep video
     * Fokus pada tahap pertama dari algoritma baru
     */
    public function generateNarasiAndConcept(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'product' => 'required|string|max:1000',
                'purpose' => 'nullable|string|max:255',
                'tone' => 'nullable|string|max:100',
                'duration' => 'nullable|integer|min:10|max:300',
                'style' => 'nullable|string|max:100',
                'target_audience' => 'nullable|string|max:255',
                'key_message' => 'nullable|string|max:500',
            ]);

            $product = $validated['product'];
            $purpose = $validated['purpose'] ?? 'promosi produk';
            $tone = $validated['tone'] ?? 'friendly';
            $duration = $validated['duration'] ?? 30;
            $style = $validated['style'] ?? 'modern';
            $targetAudience = $validated['target_audience'] ?? 'umum';
            $keyMessage = $validated['key_message'] ?? '';

            Log::info('Starting Step 1: Generate narasi + konsep', [
                'product' => $product,
                'purpose' => $purpose,
                'duration' => $duration,
                'tone' => $tone,
            ]);

            // Generate narasi dan konsep menggunakan OpenAI
            $result = $this->generateConceptAndScript(
                $product,
                $purpose,
                $tone,
                $duration,
                $style,
                $targetAudience,
                $keyMessage
            );

            Log::info('Step 1 completed successfully', [
                'concept_length' => strlen($result['concept']),
                'narration_length' => strlen($result['narration']),
                'duration' => $result['duration'],
            ]);

            return response()->json([
                'success' => true,
                'step' => 1,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Step 1 error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Failed to generate narasi and concept',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate concept and script using OpenAI API
     */
    private function generateConceptAndScript($product, $purpose, $tone, $duration, $style, $targetAudience, $keyMessage)
    {
        try {
            // Get OpenAI API key
            $openaiApiKey = env('OPENAI_API_KEY');

            if (!$openaiApiKey) {
                throw new \Exception('OpenAI API key not configured');
            }

            // Build comprehensive prompt
            $prompt = $this->buildConceptPrompt($product, $purpose, $tone, $duration, $style, $targetAudience, $keyMessage);

            // Call OpenAI API
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $openaiApiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post('https://api.openai.com/v1/chat/completions', [
                'model' => 'gpt-4',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'Anda adalah expert video marketing yang ahli dalam membuat konsep dan script video promosi yang menarik dan efektif dalam bahasa Indonesia.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => 2000,
                'temperature' => 0.7,
            ]);

            if (!$response->successful()) {
                throw new \Exception('OpenAI API request failed: ' . $response->body());
            }

            $data = $response->json();
            $content = $data['choices'][0]['message']['content'] ?? '';

            if (empty($content)) {
                throw new \Exception('Empty response from OpenAI');
            }

            // Parse and structure the response
            $result = $this->parseAndStructureResponse($content, $duration);

            return $result;
        } catch (\Exception $e) {
            Log::error('Concept generation error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Build comprehensive prompt for concept generation
     */
    private function buildConceptPrompt($product, $purpose, $tone, $duration, $style, $targetAudience, $keyMessage)
    {
        $prompt = "Buatkan konsep video marketing yang komprehensif untuk produk/layanan berikut:

**PRODUK/LAYANAN:** {$product}
**TUJUAN:** {$purpose}
**TARGET AUDIENCE:** {$targetAudience}
**DURASI:** {$duration} detik
**TONE:** {$tone}
**STYLE:** {$style}";

        if (!empty($keyMessage)) {
            $prompt .= "\n**KEY MESSAGE:** {$keyMessage}";
        }

        $prompt .= "\n\nHasilkan output dalam format JSON dengan struktur berikut:

```json
{
  \"concept\": {
    \"title\": \"Judul konsep video\",
    \"overview\": \"Deskripsi singkat konsep video\",
    \"target_audience\": \"Target audience spesifik\",
    \"key_message\": \"Pesan utama yang ingin disampaikan\",
    \"visual_style\": \"Deskripsi gaya visual\",
    \"call_to_action\": \"Call to action yang kuat\"
  },
  \"narration\": {
    \"full_script\": \"Script narasi lengkap untuk voice over {$duration} detik\",
    \"word_count\": 0,
    \"estimated_duration\": 0
  }
}
```

PENTING:
1. Script narasi harus natural dan sesuai durasi {$duration} detik (sekitar " . round($duration * 2.5) . " kata)
2. Fokus pada konsep dan narasi yang kuat
3. Gunakan bahasa Indonesia yang menarik dan sesuai tone '{$tone}'
4. Konsep harus jelas dan mudah dipahami
5. Narasi harus mengalir natural dan menarik";

        return $prompt;
    }

    /**
     * Parse and structure AI response
     */
    private function parseAndStructureResponse($content, $duration)
    {
        try {
            // Clean response - remove markdown code blocks if present
            $cleanResponse = preg_replace('/```json\s*|\s*```/', '', $content);
            $cleanResponse = trim($cleanResponse);

            // Try to extract JSON from response
            $jsonMatch = [];
            if (preg_match('/\{[\s\S]*\}/', $cleanResponse, $jsonMatch)) {
                $jsonStr = $jsonMatch[0];
                $decoded = json_decode($jsonStr, true);

                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    return $this->structureValidResponse($decoded, $duration);
                }
            }

            // If JSON parsing fails, try to extract information manually
            return $this->extractInformationManually($content, $duration);
        } catch (\Exception $e) {
            Log::error('Response parsing error: ' . $e->getMessage());
            return $this->createFallbackResponse($duration);
        }
    }

    /**
     * Structure valid JSON response
     */
    private function structureValidResponse($decoded, $duration)
    {
        $concept = $decoded['concept'] ?? [];
        $narration = $decoded['narration'] ?? [];

        // Format concept
        $formattedConcept = $this->formatConcept($concept);

        // Format narration
        $formattedNarration = $this->formatNarration($narration);

        return [
            'concept' => $formattedConcept,
            'narration' => $formattedNarration,
            'duration' => $duration
        ];
    }

    /**
     * Format concept for display
     */
    private function formatConcept($concept)
    {
        if (is_string($concept)) {
            return $concept;
        }

        if (!is_array($concept)) {
            return 'Konsep video marketing';
        }

        $formatted = "";

        if (isset($concept['title'])) {
            $formatted .= "📹 **{$concept['title']}**\n\n";
        }

        if (isset($concept['overview'])) {
            $formatted .= "📝 **Overview:**\n{$concept['overview']}\n\n";
        }

        if (isset($concept['target_audience'])) {
            $formatted .= "🎯 **Target Audience:**\n{$concept['target_audience']}\n\n";
        }

        if (isset($concept['key_message'])) {
            $formatted .= "💡 **Key Message:**\n{$concept['key_message']}\n\n";
        }

        if (isset($concept['visual_style'])) {
            $formatted .= "🎨 **Visual Style:**\n{$concept['visual_style']}\n\n";
        }

        if (isset($concept['call_to_action'])) {
            $formatted .= "📢 **Call to Action:**\n{$concept['call_to_action']}\n\n";
        }

        return trim($formatted);
    }

    /**
     * Format narration for display
     */
    private function formatNarration($narration)
    {
        if (is_string($narration)) {
            return $narration;
        }

        if (!is_array($narration)) {
            return 'Script narasi video';
        }

        $formatted = "";

        if (isset($narration['full_script'])) {
            $formatted .= $narration['full_script'];
        }

        if (isset($narration['word_count']) && isset($narration['estimated_duration'])) {
            $formatted .= "\n\n---\n";
            $formatted .= "📊 **Statistik:**\n";
            $formatted .= "• Jumlah kata: {$narration['word_count']}\n";
            $formatted .= "• Estimasi durasi: {$narration['estimated_duration']} detik";
        }

        return trim($formatted);
    }



    /**
     * Extract information manually from unstructured response
     */
    private function extractInformationManually($content, $duration)
    {
        // Simple extraction logic for when JSON parsing fails
        $lines = explode("\n", $content);
        $concept = '';
        $script = '';

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            if (stripos($line, 'konsep') !== false || stripos($line, 'concept') !== false) {
                $concept .= $line . "\n";
            } elseif (stripos($line, 'script') !== false || stripos($line, 'narasi') !== false) {
                $script .= $line . "\n";
            }
        }

        return [
            'concept' => trim($concept) ?: $content,
            'narration' => trim($script) ?: 'Script akan dihasilkan berdasarkan konsep',
            'duration' => $duration
        ];
    }

    /**
     * Create fallback response
     */
    private function createFallbackResponse($duration)
    {
        return [
            'concept' => 'Konsep video marketing profesional untuk mempromosikan produk dengan pendekatan yang menarik dan efektif.',
            'narration' => 'Script narasi akan disesuaikan dengan durasi dan tujuan video untuk menghasilkan pesan yang kuat dan meyakinkan.',
            'duration' => $duration
        ];
    }

    /**
     * STEP 2: Generate voice over dari narasi
     */
    public function generateVoiceOver(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'narration' => 'required|string|max:5000',
                'language' => 'nullable|string|max:10',
                'gender' => 'nullable|string|max:10',
                'tone' => 'nullable|string|max:50',
            ]);

            $narration = $validated['narration'];
            $language = $validated['language'] ?? 'id-ID';
            $gender = $validated['gender'] ?? 'female';
            $tone = $validated['tone'] ?? 'friendly';

            Log::info('Starting Step 2: Generate voice over', [
                'narration_length' => strlen($narration),
                'language' => $language,
                'gender' => $gender,
                'tone' => $tone,
            ]);

            // Generate voice over using Gemini TTS
            $audioUrl = $this->textToSpeech($narration, $language, $gender);

            if (!$audioUrl) {
                throw new \Exception('Failed to generate voice over');
            }

            Log::info('Step 2 completed successfully', [
                'audio_url' => $audioUrl,
            ]);

            return response()->json([
                'success' => true,
                'step' => 2,
                'data' => [
                    'audio_url' => $audioUrl,
                    'narration' => $narration,
                    'language' => $language,
                    'gender' => $gender,
                    'tone' => $tone
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Step 2 error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Failed to generate voice over',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Convert text to speech using Gemini 2.5 Pro Preview TTS
     */
    private function textToSpeech($text, $language, $gender)
    {
        try {
            // Get API key
            $apiKey = env('GEMINI_API_KEY');
            if (!$apiKey) {
                throw new \Exception('GEMINI_API_KEY not configured');
            }

            // Determine voice name based on language and gender
            $voiceName = $this->getVoiceName($language, $gender);

            // Call Gemini 2.5 Pro Preview TTS API
            $endpoint = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro-preview-tts:generateContent?key={$apiKey}";

            $response = Http::timeout(120)->withOptions([
                'stream' => false,
                'verify' => false,
            ])->withHeaders([
                'Content-Type' => 'application/json',
            ])->post($endpoint, [
                'contents' => [
                    [
                        'role' => 'user',
                        'parts' => [
                            [
                                'text' => $text
                            ]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 1,
                    'responseModalities' => ['audio'],
                    'speechConfig' => [
                        'voiceConfig' => [
                            'prebuiltVoiceConfig' => [
                                'voiceName' => $voiceName
                            ]
                        ]
                    ]
                ]
            ]);

            if (!$response->successful()) {
                $errorBody = $response->body();
                $statusCode = $response->status();
                Log::error('Gemini TTS API request failed', [
                    'status' => $statusCode,
                    'body' => $errorBody,
                    'endpoint' => $endpoint,
                ]);
                throw new \Exception("Gemini TTS API request failed (HTTP {$statusCode}): {$errorBody}");
            }

            $data = $response->json();
            Log::info('Gemini TTS API response received', [
                'has_candidates' => isset($data['candidates']),
                'response_keys' => array_keys($data),
            ]);

            // Extract audio data from response
            $audioData = null;
            $mimeType = null;

            if (isset($data['candidates'][0]['content']['parts'])) {
                foreach ($data['candidates'][0]['content']['parts'] as $part) {
                    if (isset($part['inlineData']['data'])) {
                        $audioData = $part['inlineData']['data'];
                        $mimeType = $part['inlineData']['mimeType'] ?? 'audio/wav';
                        break;
                    }
                }
            }

            if (!$audioData) {
                throw new \Exception('No audio data in response');
            }

            // Decode base64 audio data
            $decodedAudio = base64_decode($audioData);

            // Convert to WAV if needed
            if (str_contains($mimeType, 'L16') || str_contains($mimeType, 'pcm')) {
                $decodedAudio = $this->convertToWav($decodedAudio, $mimeType);
                $extension = 'wav';
            } else {
                $extension = 'mp3';
            }

            // Save audio file
            $filename = 'video-voice-over-' . time() . '.' . $extension;
            $path = 'video-voice-overs/' . $filename;

            Storage::disk('public')->put($path, $decodedAudio);

            // Return public URL via API endpoint
            $audioUrl = url('api/storage/' . $path);

            Log::info("Video voice over generated successfully: {$audioUrl}");

            return $audioUrl;
        } catch (\Exception $e) {
            Log::error('Video voice over generation error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get voice name based on language and gender
     */
    private function getVoiceName($language, $gender)
    {
        // Gemini TTS prebuilt voices
        $voices = [
            'id-ID' => [
                'female' => 'Aoede',  // Warm, friendly female voice
                'male' => 'Fenrir',   // Strong, confident male voice
            ],
            'en-US' => [
                'female' => 'Aoede',  // Warm, friendly female voice
                'male' => 'Puck',     // Energetic male voice
            ],
        ];

        return $voices[$language][$gender] ?? 'Aoede';
    }

    /**
     * Convert raw PCM audio to WAV format
     */
    private function convertToWav($audioData, $mimeType)
    {
        // Parse audio parameters from mime type
        $params = $this->parseAudioMimeType($mimeType);
        $bitsPerSample = $params['bits_per_sample'];
        $sampleRate = $params['rate'];
        $numChannels = 1;
        $dataSize = strlen($audioData);
        $bytesPerSample = $bitsPerSample / 8;
        $blockAlign = $numChannels * $bytesPerSample;
        $byteRate = $sampleRate * $blockAlign;
        $chunkSize = 36 + $dataSize;

        // Build WAV header
        $header = pack(
            'a4Va4a4VvvVVvva4V',
            'RIFF',           // ChunkID
            $chunkSize,       // ChunkSize
            'WAVE',           // Format
            'fmt ',           // Subchunk1ID
            16,               // Subchunk1Size (16 for PCM)
            1,                // AudioFormat (1 for PCM)
            $numChannels,     // NumChannels
            $sampleRate,      // SampleRate
            $byteRate,        // ByteRate
            $blockAlign,      // BlockAlign
            $bitsPerSample,   // BitsPerSample
            'data',           // Subchunk2ID
            $dataSize         // Subchunk2Size
        );

        return $header . $audioData;
    }

    /**
     * Parse audio MIME type to extract parameters
     */
    private function parseAudioMimeType($mimeType)
    {
        $bitsPerSample = 16;
        $rate = 24000;

        // Extract rate from parameters
        $parts = explode(';', $mimeType);
        foreach ($parts as $param) {
            $param = trim($param);
            if (stripos($param, 'rate=') === 0) {
                $rateStr = substr($param, 5);
                $rate = intval($rateStr);
            } elseif (stripos($param, 'audio/L') === 0) {
                $bitsStr = substr($param, 7);
                $bitsPerSample = intval($bitsStr);
            }
        }

        return [
            'bits_per_sample' => $bitsPerSample,
            'rate' => $rate
        ];
    }

    /**
     * STEP 3: Generate silent video berdasarkan voice over
     */
    public function generateSilentVideo(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'concept' => 'required|string|max:5000',
                'audio_url' => 'required|url',
                'duration' => 'nullable|integer|min:5|max:300',
                'style' => 'nullable|string|max:100',
                'product' => 'nullable|string|max:500',
                'async' => 'nullable|boolean',
            ]);

            $concept = $validated['concept'];
            $audioUrl = $validated['audio_url'];
            $duration = $validated['duration'] ?? 30;
            $style = $validated['style'] ?? 'modern';
            $product = $validated['product'] ?? '';
            $async = $validated['async'] ?? false;

            Log::info('Starting Step 3: Generate silent video', [
                'concept_length' => strlen($concept),
                'audio_url' => $audioUrl,
                'duration' => $duration,
                'style' => $style,
                'async' => $async,
            ]);

            // Get audio duration from file
            $actualDuration = $this->getAudioDuration($audioUrl);
            if ($actualDuration) {
                $duration = $actualDuration;
                Log::info('Using actual audio duration', ['duration' => $duration]);
            }

            // Generate video prompt from concept
            $videoPrompt = $this->buildVideoPrompt($concept, $product, $style, $duration);

            if ($async) {
                // Start async video generation
                $operationId = $this->startAsyncVideoGeneration($videoPrompt, $duration);

                return response()->json([
                    'success' => true,
                    'step' => 3,
                    'status' => 'processing',
                    'operation_id' => $operationId,
                    'message' => 'Video generation started. Use operation_id to check status.',
                    'data' => [
                        'audio_url' => $audioUrl,
                        'duration' => $duration,
                        'video_prompt' => $videoPrompt,
                        'style' => $style
                    ]
                ]);
            } else {
                // Synchronous generation (may timeout)
                $videoWithAudioUrl = $this->generateVideoWithVeo($videoPrompt, $duration);

                if (!$videoWithAudioUrl) {
                    throw new \Exception('Failed to generate silent video');
                }

                // Try to remove audio to make it truly silent
                $result = $this->removeAudioFromVideo($videoWithAudioUrl);
                $silentVideoUrl = $result['video_url'];
                $audioRemoved = $result['audio_removed'];

                Log::info('Step 3 completed successfully', [
                    'video_url' => $silentVideoUrl,
                    'duration' => $duration,
                    'audio_removed' => $audioRemoved,
                ]);

                return response()->json([
                    'success' => true,
                    'step' => 3,
                    'data' => [
                        'video_url' => $silentVideoUrl,
                        'audio_url' => $audioUrl,
                        'duration' => $duration,
                        'video_prompt' => $videoPrompt,
                        'style' => $style,
                        'audio_removed' => $audioRemoved,
                        'note' => $audioRemoved ? 'Audio successfully removed' : 'Video may contain audio - FFmpeg required for removal'
                    ]
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Step 3 error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Failed to generate silent video',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Start async video generation and return operation ID
     */
    private function startAsyncVideoGeneration($prompt, $duration)
    {
        try {
            $apiKey = env('GEMINI_API_KEY');
            if (!$apiKey) {
                throw new \Exception('GEMINI_API_KEY not configured');
            }

            Log::info('Starting async video generation with Veo', [
                'prompt_length' => strlen($prompt),
                'duration' => $duration
            ]);

            // Call Veo API
            $endpoint = "https://generativelanguage.googleapis.com/v1beta/models/veo-3.0-generate-001:predictLongRunning?key={$apiKey}";

            $response = Http::timeout(30)->withHeaders([
                'Content-Type' => 'application/json',
            ])->post($endpoint, [
                'instances' => [
                    [
                        'prompt' => $prompt,
                    ]
                ],
                'parameters' => [
                    'aspectRatio' => '16:9',
                    'resolution' => '720p'
                ]
            ]);

            if (!$response->successful()) {
                Log::error('Veo API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);
                throw new \Exception('Veo API request failed: ' . $response->body());
            }

            $data = $response->json();
            $operationName = $data['name'] ?? null;

            if (!$operationName) {
                throw new \Exception('No operation name returned from Veo API');
            }

            Log::info('Async Veo operation started', ['operation' => $operationName]);

            return $operationName;
        } catch (\Exception $e) {
            Log::error('Async video generation error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check status of video generation operation
     */
    public function checkVideoStatus(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'operation_id' => 'required|string',
            ]);

            $operationId = $validated['operation_id'];
            $apiKey = env('GEMINI_API_KEY');

            if (!$apiKey) {
                throw new \Exception('GEMINI_API_KEY not configured');
            }

            Log::info('Checking video operation status', ['operation_id' => $operationId]);

            $endpoint = "https://generativelanguage.googleapis.com/v1beta/{$operationId}?key={$apiKey}";

            $response = Http::timeout(30)->get($endpoint);

            if (!$response->successful()) {
                Log::error('Operation status check failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);
                throw new \Exception('Failed to check operation status: ' . $response->body());
            }

            $statusData = $response->json();

            if (isset($statusData['done']) && $statusData['done']) {
                // Operation completed
                Log::info('Video operation completed', ['operation_id' => $operationId]);

                // Check for errors first
                if (
                    isset($statusData['response']['generateVideoResponse']['raiMediaFilteredCount']) &&
                    $statusData['response']['generateVideoResponse']['raiMediaFilteredCount'] > 0
                ) {

                    $reasons = $statusData['response']['generateVideoResponse']['raiMediaFilteredReasons'] ?? ['Unknown safety filter issue'];
                    Log::warning('Video generation blocked by safety filters', [
                        'operation_id' => $operationId,
                        'reasons' => $reasons
                    ]);

                    return response()->json([
                        'success' => false,
                        'status' => 'failed',
                        'error' => 'Video generation blocked by safety filters',
                        'message' => 'The video prompt was blocked by safety filters. Please try with different content: ' . implode(', ', $reasons),
                        'operation_id' => $operationId,
                        'retry_suggestion' => 'Try modifying your product description or using different keywords'
                    ], 400);
                }

                // Try different response structures for video URI
                $videoUri = $statusData['response']['generateVideoResponse']['generatedSamples'][0]['video']['uri'] ??
                    $statusData['response']['predictions'][0]['video']['uri'] ??
                    $statusData['response']['video']['uri'] ?? null;

                if (!$videoUri) {
                    Log::error('No video URI found in response', ['response' => $statusData['response'] ?? 'no response']);

                    return response()->json([
                        'success' => false,
                        'status' => 'failed',
                        'error' => 'Video generation failed',
                        'message' => 'No video was generated. This might be due to content restrictions or technical issues.',
                        'operation_id' => $operationId,
                        'retry_suggestion' => 'Please try again with different product description or style'
                    ], 400);
                }

                // Download and save video
                $videoWithAudioUrl = $this->downloadVeoVideo($videoUri, $apiKey);

                // Try to remove audio to make it truly silent
                $result = $this->removeAudioFromVideo($videoWithAudioUrl);
                $silentVideoUrl = $result['video_url'];
                $audioRemoved = $result['audio_removed'];

                return response()->json([
                    'success' => true,
                    'status' => 'completed',
                    'data' => [
                        'video_url' => $silentVideoUrl,
                        'operation_id' => $operationId,
                        'audio_removed' => $audioRemoved,
                        'note' => $audioRemoved ? 'Audio successfully removed' : 'Video may contain audio - FFmpeg required for removal'
                    ]
                ]);
            } else {
                // Still processing
                return response()->json([
                    'success' => true,
                    'status' => 'processing',
                    'message' => 'Video generation is still in progress',
                    'operation_id' => $operationId
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Video status check error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Failed to check video status',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get audio duration from file
     */
    private function getAudioDuration($audioUrl)
    {
        try {
            // Extract filename from URL
            $filename = basename(parse_url($audioUrl, PHP_URL_PATH));
            $filePath = storage_path('app/public/video-voice-overs/' . $filename);

            if (!file_exists($filePath)) {
                Log::warning('Audio file not found for duration check', ['path' => $filePath]);
                return null;
            }

            // Use ffprobe to get duration (if available)
            $ffprobePath = trim(shell_exec('which ffprobe'));
            if (!empty($ffprobePath)) {
                $command = sprintf(
                    '%s -v quiet -show_entries format=duration -of csv=p=0 %s',
                    $ffprobePath,
                    escapeshellarg($filePath)
                );

                $output = trim(shell_exec($command));
                if (is_numeric($output)) {
                    return (int) ceil(floatval($output));
                }
            }

            // Fallback: estimate from file size (rough approximation)
            $fileSize = filesize($filePath);
            $estimatedDuration = max(5, min(60, $fileSize / 50000)); // rough estimate

            Log::info('Estimated audio duration from file size', [
                'file_size' => $fileSize,
                'estimated_duration' => $estimatedDuration
            ]);

            return (int) ceil($estimatedDuration);
        } catch (\Exception $e) {
            Log::warning('Failed to get audio duration: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Build video prompt from concept
     */
    private function buildVideoPrompt($concept, $product, $style, $duration)
    {
        // Sanitize product name to avoid safety filters
        $safeProduct = $this->sanitizeProductName($product);

        // Extract key visual elements from concept
        $visualStyle = $this->extractVisualStyle($concept);
        $targetAudience = $this->extractTargetAudience($concept);

        // Build safe, generic prompt
        $prompt = "A {$duration}-second commercial video featuring {$safeProduct}. ";

        // Add visual style
        $prompt .= "Style: {$style} cinematography with {$visualStyle} aesthetic. ";

        // Add safe visual elements
        $prompt .= "Show product in attractive setting with good lighting. ";
        $prompt .= "Professional camera work with smooth movements. ";
        $prompt .= "Clean, appealing visuals suitable for marketing. ";

        // Emphasize no audio requirement
        $prompt .= "Create video without any audio track or sound. ";
        $prompt .= "Visual-only content with no spoken words or music.";

        Log::info('Built video prompt', [
            'prompt_length' => strlen($prompt),
            'style' => $style,
            'duration' => $duration,
            'safe_product' => $safeProduct
        ]);

        return $prompt;
    }

    /**
     * Sanitize product name to avoid safety filters
     */
    private function sanitizeProductName($product)
    {
        // Remove potentially problematic words and keep it generic
        $product = preg_replace('/\b(premium|luxury|exclusive|best|perfect|amazing)\b/i', '', $product);
        $product = preg_replace('/[^a-zA-Z0-9\s]/', '', $product);
        $product = trim(preg_replace('/\s+/', ' ', $product));

        // If product name is too long or empty, use generic term
        if (strlen($product) > 30 || empty($product)) {
            return 'product';
        }

        return strtolower($product);
    }

    /**
     * Extract visual style from concept
     */
    private function extractVisualStyle($concept)
    {
        $defaultStyle = "modern, clean, professional";

        // Look for visual style keywords in concept
        $styleKeywords = [
            'modern' => 'modern, sleek, contemporary',
            'vintage' => 'vintage, retro, classic',
            'minimalist' => 'minimalist, clean, simple',
            'colorful' => 'vibrant, colorful, energetic',
            'elegant' => 'elegant, sophisticated, premium',
            'casual' => 'casual, relaxed, friendly',
            'professional' => 'professional, corporate, polished'
        ];

        foreach ($styleKeywords as $keyword => $description) {
            if (stripos($concept, $keyword) !== false) {
                return $description;
            }
        }

        return $defaultStyle;
    }

    /**
     * Extract target audience from concept
     */
    private function extractTargetAudience($concept)
    {
        $audienceKeywords = [
            'anak muda' => 'young adults, millennials',
            'remaja' => 'teenagers, young people',
            'dewasa' => 'adults, professionals',
            'keluarga' => 'families, parents',
            'profesional' => 'business professionals',
            'ibu rumah tangga' => 'homemakers, mothers',
            'pelajar' => 'students, learners'
        ];

        foreach ($audienceKeywords as $keyword => $description) {
            if (stripos($concept, $keyword) !== false) {
                return $description;
            }
        }

        return 'general audience';
    }

    /**
     * Generate silent video using Veo
     */
    private function generateVideoWithVeo($prompt, $duration)
    {
        try {
            $apiKey = env('GEMINI_API_KEY');
            if (!$apiKey) {
                throw new \Exception('GEMINI_API_KEY not configured');
            }

            Log::info('Generating silent video with Veo', [
                'prompt_length' => strlen($prompt),
                'duration' => $duration
            ]);

            // Call Veo API
            $endpoint = "https://generativelanguage.googleapis.com/v1beta/models/veo-3.0-generate-001:predictLongRunning?key={$apiKey}";

            $response = Http::timeout(30)->withHeaders([
                'Content-Type' => 'application/json',
            ])->post($endpoint, [
                'instances' => [
                    [
                        'prompt' => $prompt,
                    ]
                ],
                'parameters' => [
                    'aspectRatio' => '16:9',
                    'resolution' => '720p'
                    // Note: Veo automatically generates 8-second clips
                ]
            ]);

            if (!$response->successful()) {
                Log::error('Veo API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);
                throw new \Exception('Veo API request failed: ' . $response->body());
            }

            $data = $response->json();
            $operationName = $data['name'] ?? null;

            if (!$operationName) {
                throw new \Exception('No operation name returned from Veo API');
            }

            Log::info('Veo operation started', ['operation' => $operationName]);

            // Poll for completion
            $videoUri = $this->pollVeoOperation($operationName, $apiKey);

            // Download and save video
            $videoPath = $this->downloadVeoVideo($videoUri, $apiKey);

            return $videoPath;
        } catch (\Exception $e) {
            Log::error('Veo video generation error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Poll Veo operation until video is ready
     */
    private function pollVeoOperation($operationName, $apiKey)
    {
        $maxAttempts = 60; // 60 attempts * 10 seconds = 10 minutes max
        $attempt = 0;

        while ($attempt < $maxAttempts) {
            sleep(10); // Wait 10 seconds between polls

            $endpoint = "https://generativelanguage.googleapis.com/v1beta/{$operationName}?key={$apiKey}";

            $response = Http::timeout(30)->get($endpoint);

            if (!$response->successful()) {
                Log::error('Veo operation poll failed', [
                    'attempt' => $attempt,
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);
                $attempt++;
                continue;
            }

            $statusData = $response->json();

            if (isset($statusData['done']) && $statusData['done']) {
                // Operation completed
                Log::info('Veo operation completed', ['response' => $statusData]);

                // Try different response structures
                $videoUri = $statusData['response']['generateVideoResponse']['generatedSamples'][0]['video']['uri'] ??
                    $statusData['response']['predictions'][0]['video']['uri'] ??
                    $statusData['response']['video']['uri'] ?? null;

                if (!$videoUri) {
                    Log::error('No video URI found in response', ['response' => $statusData['response'] ?? 'no response']);
                    throw new \Exception('No video URI in completed operation');
                }

                return $videoUri;
            } else {
                Log::info('Veo operation still processing', ['attempt' => $attempt + 1, 'max' => $maxAttempts]);
                $attempt++;
            }
        }

        throw new \Exception('Veo video generation timed out after 10 minutes');
    }

    /**
     * Download video from Veo and save locally
     */
    private function downloadVeoVideo($videoUri, $apiKey)
    {
        try {
            Log::info('Downloading video from Veo', ['uri' => $videoUri]);

            // Download video with authentication
            $videoResponse = Http::timeout(300)
                ->withHeaders([
                    'x-goog-api-key' => $apiKey,
                ])
                ->get($videoUri);

            if (!$videoResponse->successful()) {
                Log::error('Failed to download video', [
                    'status' => $videoResponse->status(),
                    'body' => $videoResponse->body(),
                    'uri' => $videoUri,
                ]);
                throw new \Exception('Failed to download video: ' . $videoResponse->body());
            }

            // Save video file
            $videoFilename = 'silent-video-' . time() . '-' . rand(1000, 9999) . '.mp4';
            $videoPath = 'silent-videos/' . $videoFilename;

            Storage::disk('public')->put($videoPath, $videoResponse->body());

            // Return public URL via API endpoint
            $videoUrl = url('api/storage/' . $videoPath);

            Log::info('Silent video saved successfully', [
                'path' => $videoPath,
                'url' => $videoUrl,
                'size' => strlen($videoResponse->body()),
            ]);

            return $videoUrl;
        } catch (\Exception $e) {
            Log::error('Video download error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Remove audio from video to make it truly silent
     */
    private function removeAudioFromVideo($videoUrl)
    {
        try {
            // Extract filename from URL
            $originalFilename = basename(parse_url($videoUrl, PHP_URL_PATH));
            $originalPath = storage_path('app/public/silent-videos/' . $originalFilename);

            if (!file_exists($originalPath)) {
                Log::warning('Original video file not found', ['path' => $originalPath]);
                return ['video_url' => $videoUrl, 'audio_removed' => false]; // Return original if file not found
            }

            // Check if FFmpeg is installed - try multiple detection methods
            $ffmpegPath = $this->findFFmpegPath();
            if (empty($ffmpegPath)) {
                Log::warning('FFmpeg not found in system PATH, returning video with audio');
                return ['video_url' => $videoUrl, 'audio_removed' => false]; // Return original if FFmpeg not available
            }

            Log::info('FFmpeg found', ['path' => $ffmpegPath]);

            // Create silent version filename
            $silentFilename = 'silent-' . time() . '-' . rand(1000, 9999) . '.mp4';
            $silentPath = storage_path('app/public/silent-videos/' . $silentFilename);

            // FFmpeg command to remove audio
            $command = sprintf(
                '%s -i %s -c:v copy -an %s 2>&1',
                $ffmpegPath,
                escapeshellarg($originalPath),
                escapeshellarg($silentPath)
            );

            Log::info('Removing audio from video', [
                'command' => $command,
                'original_file' => $originalFilename,
                'silent_file' => $silentFilename
            ]);

            exec($command, $output, $returnCode);

            if ($returnCode !== 0) {
                Log::error('FFmpeg failed to remove audio', [
                    'return_code' => $returnCode,
                    'output' => implode("\n", $output),
                ]);
                return ['video_url' => $videoUrl, 'audio_removed' => false]; // Return original if FFmpeg fails
            }

            if (!file_exists($silentPath)) {
                Log::error('Silent video file was not created');
                return ['video_url' => $videoUrl, 'audio_removed' => false]; // Return original if silent file not created
            }

            // Delete original video with audio
            if (file_exists($originalPath)) {
                unlink($originalPath);
                Log::info('Original video with audio deleted', ['file' => $originalFilename]);
            }

            // Return URL to silent video via API endpoint
            $silentVideoUrl = url('api/storage/silent-videos/' . $silentFilename);

            Log::info('Audio removed successfully', [
                'silent_video_url' => $silentVideoUrl,
                'file_size' => filesize($silentPath),
            ]);

            return ['video_url' => $silentVideoUrl, 'audio_removed' => true];
        } catch (\Exception $e) {
            Log::error('Error removing audio from video: ' . $e->getMessage());
            return ['video_url' => $videoUrl, 'audio_removed' => false]; // Return original video if error occurs
        }
    }

    /**
     * Find FFmpeg executable path using multiple detection methods
     */
    private function findFFmpegPath()
    {
        // Common FFmpeg paths to check
        $commonPaths = [
            '/usr/bin/ffmpeg',
            '/usr/local/bin/ffmpeg',
            '/opt/homebrew/bin/ffmpeg',
            '/c/ffmpeg/bin/ffmpeg',
            '/c/ffmpeg/bin/ffmpeg.exe',
            'C:\\ffmpeg\\bin\\ffmpeg.exe',
            'ffmpeg', // Try direct command
        ];

        // First try 'which' command
        $whichResult = trim(shell_exec('which ffmpeg 2>/dev/null'));
        if (!empty($whichResult) && file_exists($whichResult)) {
            return $whichResult;
        }

        // Try 'where' command (Windows)
        $whereResult = trim(shell_exec('where ffmpeg 2>/dev/null'));
        if (!empty($whereResult)) {
            $paths = explode("\n", $whereResult);
            $firstPath = trim($paths[0]);
            if (file_exists($firstPath)) {
                return $firstPath;
            }
        }

        // Check common paths
        foreach ($commonPaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        // Try to execute ffmpeg directly to see if it's in PATH
        $testResult = shell_exec('ffmpeg -version 2>/dev/null');
        if (!empty($testResult) && strpos($testResult, 'ffmpeg version') !== false) {
            return 'ffmpeg'; // Available in PATH
        }

        return null; // FFmpeg not found
    }

    /**
     * Test FFmpeg detection (for debugging)
     */
    public function testFFmpeg(): JsonResponse
    {
        try {
            $ffmpegPath = $this->findFFmpegPath();

            if ($ffmpegPath) {
                // Test FFmpeg execution
                $testCommand = $ffmpegPath . ' -version 2>&1';
                $output = shell_exec($testCommand);

                return response()->json([
                    'success' => true,
                    'ffmpeg_found' => true,
                    'ffmpeg_path' => $ffmpegPath,
                    'version_info' => substr($output, 0, 200) // First 200 chars
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'ffmpeg_found' => false,
                    'message' => 'FFmpeg not found in system'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * STEP 4: Gabungkan voice over dengan silent video
     */
    public function combineVideoWithAudio(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'video_url' => 'required|url',
                'audio_url' => 'required|url',
                'duration' => 'nullable|integer|min:5|max:300',
                'product' => 'nullable|string|max:500',
            ]);

            $videoUrl = $validated['video_url'];
            $audioUrl = $validated['audio_url'];
            $duration = $validated['duration'] ?? 30;
            $product = $validated['product'] ?? 'Product';

            Log::info('Starting Step 4: Combine video with audio', [
                'video_url' => $videoUrl,
                'audio_url' => $audioUrl,
                'duration' => $duration,
            ]);

            // Check if FFmpeg is available
            $ffmpegPath = $this->findFFmpegPath();
            if (empty($ffmpegPath)) {
                throw new \Exception('FFmpeg is required for video and audio combination');
            }

            // Get local file paths
            $videoPath = $this->getLocalFilePath($videoUrl, 'silent-videos');
            $audioPath = $this->getLocalFilePath($audioUrl, 'video-voice-overs');

            if (!$videoPath || !$audioPath) {
                throw new \Exception('Video or audio file not found locally');
            }

            // Combine video and audio
            $finalVideoUrl = $this->combineVideoAndAudio($videoPath, $audioPath, $ffmpegPath, $product);

            if (!$finalVideoUrl) {
                throw new \Exception('Failed to combine video and audio');
            }

            Log::info('Step 4 completed successfully', [
                'final_video_url' => $finalVideoUrl,
                'duration' => $duration,
            ]);

            return response()->json([
                'success' => true,
                'step' => 4,
                'data' => [
                    'final_video_url' => $finalVideoUrl,
                    'video_url' => $videoUrl,
                    'audio_url' => $audioUrl,
                    'duration' => $duration,
                    'product' => $product
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Step 4 error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Failed to combine video and audio',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get local file path from URL
     */
    private function getLocalFilePath($url, $expectedFolder)
    {
        try {
            // Extract filename from URL
            $filename = basename(parse_url($url, PHP_URL_PATH));
            $localPath = storage_path('app/public/' . $expectedFolder . '/' . $filename);

            if (file_exists($localPath)) {
                return $localPath;
            }

            Log::warning('Local file not found', [
                'url' => $url,
                'expected_path' => $localPath,
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Error getting local file path: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Combine video and audio using FFmpeg
     */
    private function combineVideoAndAudio($videoPath, $audioPath, $ffmpegPath, $product)
    {
        try {
            // Create output filename
            $outputFilename = 'final-video-' . time() . '-' . rand(1000, 9999) . '.mp4';
            $outputPath = storage_path('app/public/final-videos/' . $outputFilename);

            // Ensure output directory exists
            $outputDir = dirname($outputPath);
            if (!is_dir($outputDir)) {
                mkdir($outputDir, 0755, true);
            }

            // Get video and audio durations
            $videoDuration = $this->getMediaDuration($videoPath, $ffmpegPath);
            $audioDuration = $this->getMediaDuration($audioPath, $ffmpegPath);

            Log::info('Media durations', [
                'video_duration' => $videoDuration,
                'audio_duration' => $audioDuration,
            ]);

            // FFmpeg command to combine video and audio
            // If video is longer than audio, trim video to audio length
            // If audio is longer than video, loop video or extend video
            if ($videoDuration > $audioDuration) {
                // Trim video to match audio duration
                $command = sprintf(
                    '%s -i %s -i %s -t %s -c:v copy -c:a aac -b:a 128k %s -y 2>&1',
                    $ffmpegPath,
                    escapeshellarg($videoPath),
                    escapeshellarg($audioPath),
                    $audioDuration,
                    escapeshellarg($outputPath)
                );
            } else {
                // Video is shorter or equal to audio
                // Use video duration and trim audio if needed
                $command = sprintf(
                    '%s -i %s -i %s -t %s -c:v copy -c:a aac -b:a 128k %s -y 2>&1',
                    $ffmpegPath,
                    escapeshellarg($videoPath),
                    escapeshellarg($audioPath),
                    $videoDuration,
                    escapeshellarg($outputPath)
                );
            }

            Log::info('Combining video and audio', [
                'command' => $command,
                'output_file' => $outputFilename,
            ]);

            exec($command, $output, $returnCode);

            if ($returnCode !== 0) {
                Log::error('FFmpeg failed to combine video and audio', [
                    'return_code' => $returnCode,
                    'output' => implode("\n", $output),
                ]);
                throw new \Exception('FFmpeg failed: ' . implode("\n", $output));
            }

            if (!file_exists($outputPath)) {
                throw new \Exception('Final video file was not created');
            }

            // Return public URL via API endpoint
            $finalVideoUrl = url('api/storage/final-videos/' . $outputFilename);

            Log::info('Video and audio combined successfully', [
                'final_video_url' => $finalVideoUrl,
                'file_size' => filesize($outputPath),
                'output_path' => $outputPath,
            ]);

            return $finalVideoUrl;
        } catch (\Exception $e) {
            Log::error('Error combining video and audio: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get media duration using FFmpeg
     */
    private function getMediaDuration($filePath, $ffmpegPath)
    {
        try {
            $command = sprintf(
                '%s -i %s 2>&1 | grep "Duration"',
                $ffmpegPath,
                escapeshellarg($filePath)
            );

            $output = shell_exec($command);

            if (preg_match('/Duration: (\d{2}):(\d{2}):(\d{2})\.(\d{2})/', $output, $matches)) {
                $hours = (int)$matches[1];
                $minutes = (int)$matches[2];
                $seconds = (int)$matches[3];
                $centiseconds = (int)$matches[4];

                $totalSeconds = $hours * 3600 + $minutes * 60 + $seconds + $centiseconds / 100;
                return $totalSeconds;
            }

            Log::warning('Could not parse media duration', [
                'file' => $filePath,
                'output' => $output,
            ]);

            return 8; // Default fallback
        } catch (\Exception $e) {
            Log::error('Error getting media duration: ' . $e->getMessage());
            return 8; // Default fallback
        }
    }
}
