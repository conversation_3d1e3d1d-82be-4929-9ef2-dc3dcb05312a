<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('has_subscription')->default(false);
            $table->timestamp('subscription_start')->nullable();
            $table->timestamp('subscription_end')->nullable();
            $table->unsignedBigInteger('subscription_package_id')->nullable();
            $table->enum('subscription_status', ['active', 'expired', 'cancelled'])->nullable();

            $table->foreign('subscription_package_id')->references('id')->on('subscription_packages')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['subscription_package_id']);
            $table->dropColumn([
                'has_subscription',
                'subscription_start',
                'subscription_end',
                'subscription_package_id',
                'subscription_status'
            ]);
        });
    }
};
