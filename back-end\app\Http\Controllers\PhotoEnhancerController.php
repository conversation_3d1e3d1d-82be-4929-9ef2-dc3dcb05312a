<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class PhotoEnhancerController extends Controller
{
    public function generate(Request $request)
    {
        // Allow long-running
        if (function_exists('set_time_limit')) { @set_time_limit(0); }
        @ini_set('max_execution_time', '0');

        $validated = $request->validate([
            'image' => 'required|file|image|max:8192', // <= 8MB
            'headline' => 'nullable|string',
            'subHeadline' => 'nullable|string',
            'instruksi' => 'nullable|string',
            'temaName' => 'nullable|string',
            'temaImageUrl' => 'nullable|url',
            'detailing' => 'nullable|array',
            'detailing.*' => 'string',
            'sudut' => 'nullable|string',
            'aspect' => 'required|string|in:1:1,4:5,3:4,16:9,9:16',
            'count' => 'required|integer|min:1|max:4',
            'hd' => 'nullable|boolean',
        ]);

        $apiKey = config('services.openai.key') ?: env('OPENAI_API_KEY');
        if (!$apiKey) {
            return response()->json(['error' => 'OPENAI_API_KEY is not configured in environment.'], 500);
        }

        $size = match ($validated['aspect']) {
            '1:1' => '1024x1024',
            '16:9' => '1792x1024',
            '9:16' => '1024x1792',
            // Fallbacks for 4:5 and 3:4 to square due to API constraints
            '4:5' => '1024x1024',
            '3:4' => '1024x1024',
            default => '1024x1024',
        };

        $headline = (string) ($validated['headline'] ?? '');
        $subHeadline = (string) ($validated['subHeadline'] ?? '');
        $instruksi = (string) ($validated['instruksi'] ?? '');
        $temaName = (string) ($validated['temaName'] ?? '');
        $temaImageUrl = (string) ($validated['temaImageUrl'] ?? '');
        $detailing = (array) ($validated['detailing'] ?? []);
        $sudut = (string) ($validated['sudut'] ?? '');
        $count = (int) $validated['count'];

        $prompt = "📸 Photo Enhancer:\n\n";
        $prompt .= "🖼️ Gambar Produk: gunakan file produk yang diunggah (produk utama tetap jelas).\n\n";
        if ($headline) $prompt .= "📝 Headline: \"{$headline}\"\n";
        if ($subHeadline) $prompt .= "📝 Subheadline: \"{$subHeadline}\"\n\n";
        if ($instruksi || $temaName || $temaImageUrl) {
            $prompt .= "🎨 Instruksi Gaya & Latar Belakang:\n";
            if ($instruksi) $prompt .= $instruksi . "\n";
            if ($temaName) $prompt .= "- Tema: {$temaName}\n";
            if ($temaImageUrl) $prompt .= "- Referensi gaya: {$temaImageUrl}\n";
            $prompt .= "\n";
        }
        if (!empty($detailing)) {
            $prompt .= "✨ Detailing Produk:\n";
            foreach ($detailing as $d) { $prompt .= "- {$d}\n"; }
            $prompt .= "\n";
        }
        if ($sudut) {
            $prompt .= "🎥 Sudut Kamera: {$sudut}\n\n";
        }
        $prompt .= "Susun komposisi yang profesional untuk materi iklan, tipografi rapi bila teks diminta.\n";

        try {
            $file = $request->file('image');
            $filename = $file->getClientOriginalName() ?: ('upload.' . $file->extension());

            $http = Http::withToken($apiKey)->timeout(0)->connectTimeout(0);
            $http = $http->asMultipart();
            $http = $http->attach('image', file_get_contents($file->getRealPath()), $filename);

            $response = $http->post('https://api.openai.com/v1/images/edits', [
                ['name' => 'model', 'contents' => 'gpt-image-1'],
                ['name' => 'prompt', 'contents' => $prompt],
                ['name' => 'n', 'contents' => (string) $count],
                ['name' => 'size', 'contents' => $size],
            ]);

            if (!$response->successful()) {
                return response()->json([
                    'error' => 'OpenAI request failed',
                    'details' => $response->json(),
                ], $response->status());
            }

            $json = $response->json();
            $images = [];
            if (!empty($json['data']) && is_array($json['data'])) {
                foreach ($json['data'] as $item) {
                    if (!empty($item['b64_json'])) {
                        $images[] = 'data:image/png;base64,' . $item['b64_json'];
                    } elseif (!empty($item['url'])) {
                        $images[] = $item['url'];
                    }
                }
            }

            return response()->json(['images' => $images, 'size' => $size]);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Server error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}

