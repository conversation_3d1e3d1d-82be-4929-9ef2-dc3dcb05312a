import React, { useState } from 'react';
import { Sparkles, FileText, Target, Wand2, Copy, Check, Type, Mic, Video } from 'lucide-react';

const API_BASE = (import.meta as any).env?.VITE_BACKEND_URL || 'http://localhost:8000';

const MarketingContentGeneratorPage: React.FC = () => {
  const [mainTab, setMainTab] = useState<'text' | 'voice' | 'video'>('text');
  const [activeTab, setActiveTab] = useState<'caption' | 'email' | 'ads-copy'>('caption');

  // Caption Generator State
  const [captionProduct, setCaptionProduct] = useState('');
  const [captionTone, setCaptionTone] = useState('casual');
  const [captionPlatform, setCaptionPlatform] = useState('instagram');
  const [captionUrl, setCaptionUrl] = useState('');
  const [captionGenerated, setCaptionGenerated] = useState('');
  const [captionLoading, setCaptionLoading] = useState(false);
  const [captionError, setCaptionError] = useState<string | null>(null);
  const [captionCopied, setCaptionCopied] = useState(false);

  // Email Generator State
  const [emailProduct, setEmailProduct] = useState('');
  const [emailPurpose, setEmailPurpose] = useState('promo');
  const [emailTone, setEmailTone] = useState('professional');
  const [emailUrl, setEmailUrl] = useState('');
  const [emailGenerated, setEmailGenerated] = useState({ subject: '', body: '' });
  const [emailLoading, setEmailLoading] = useState(false);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [emailCopied, setEmailCopied] = useState({ subject: false, body: false });

  // Ads Copy Generator State
  const [adsProduct, setAdsProduct] = useState('');
  const [adsTarget, setAdsTarget] = useState('');
  const [adsPlatform, setAdsPlatform] = useState('facebook');
  const [adsUrl, setAdsUrl] = useState('');
  const [adsGenerated, setAdsGenerated] = useState({ headline: '', description: '', cta: '' });
  const [adsLoading, setAdsLoading] = useState(false);
  const [adsError, setAdsError] = useState<string | null>(null);
  const [adsCopied, setAdsCopied] = useState({ headline: false, description: false, cta: false });

  // Voice Generator State
  const [voiceProduct, setVoiceProduct] = useState('');
  const [voicePurpose, setVoicePurpose] = useState('promo');
  const [voiceTone, setVoiceTone] = useState('friendly');
  const [voiceLanguage, setVoiceLanguage] = useState('id-ID');
  const [voiceGender, setVoiceGender] = useState('female');
  const [voiceUrl, setVoiceUrl] = useState('');
  const [voiceGenerated, setVoiceGenerated] = useState({ script: '', audioUrl: '' });
  const [voiceLoading, setVoiceLoading] = useState(false);
  const [voiceError, setVoiceError] = useState<string | null>(null);
  const [voiceCopied, setVoiceCopied] = useState(false);

  // Video Generator State (4-Step Algorithm)
  const [videoProduct, setVideoProduct] = useState('');
  const [videoPurpose, setVideoPurpose] = useState('marketing');
  const [videoTone, setVideoTone] = useState('friendly');
  const [videoDuration, setVideoDuration] = useState('30');
  const [videoStyle, setVideoStyle] = useState('modern');
  const [videoTargetAudience, setVideoTargetAudience] = useState('');
  const [videoKeyMessage, setVideoKeyMessage] = useState('');
  const [videoLanguage, setVideoLanguage] = useState('id-ID');
  const [videoGender, setVideoGender] = useState('female');

  // 2-Step Results (Step 3 & 4 Coming Soon)
  const [step1Result, setStep1Result] = useState<any>(null);
  const [step2Result, setStep2Result] = useState<any>(null);

  // Current Step & Loading States (Only Step 1 & 2 active)
  const [currentStep, setCurrentStep] = useState(1);
  const [stepLoading, setStepLoading] = useState({ 1: false, 2: false });
  const [stepError, setStepError] = useState<{ [key: number]: string | null }>({});

  const handleCaptionGenerate = async () => {
    try {
      setCaptionError(null);
      setCaptionLoading(true);
      setCaptionGenerated('');

      const response = await fetch(`${API_BASE}/api/marketing-content/generate-text`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contentType: 'caption',
          product: captionProduct,
          tone: captionTone,
          platform: captionPlatform,
          url: captionUrl || undefined,
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data?.error || 'Gagal membuat caption');

      setCaptionGenerated(data.caption || '');
    } catch (err: any) {
      setCaptionError(err?.message || 'Terjadi kesalahan');
    } finally {
      setCaptionLoading(false);
    }
  };

  const handleEmailGenerate = async () => {
    try {
      setEmailError(null);
      setEmailLoading(true);
      setEmailGenerated({ subject: '', body: '' });

      const response = await fetch(`${API_BASE}/api/marketing-content/generate-text`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contentType: 'email',
          product: emailProduct,
          purpose: emailPurpose,
          tone: emailTone,
          url: emailUrl || undefined,
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data?.error || 'Gagal membuat email');

      setEmailGenerated({
        subject: data.subject || '',
        body: data.body || '',
      });
    } catch (err: any) {
      setEmailError(err?.message || 'Terjadi kesalahan');
    } finally {
      setEmailLoading(false);
    }
  };

  const handleAdsGenerate = async () => {
    try {
      setAdsError(null);
      setAdsLoading(true);
      setAdsGenerated({ headline: '', description: '', cta: '' });

      const response = await fetch(`${API_BASE}/api/marketing-content/generate-text`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contentType: 'ads-copy',
          product: adsProduct,
          target: adsTarget,
          platform: adsPlatform,
          url: adsUrl || undefined,
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data?.error || 'Gagal membuat ads copy');

      setAdsGenerated({
        headline: data.headline || '',
        description: data.description || '',
        cta: data.cta || '',
      });
    } catch (err: any) {
      setAdsError(err?.message || 'Terjadi kesalahan');
    } finally {
      setAdsLoading(false);
    }
  };

  const handleVoiceGenerate = async () => {
    try {
      setVoiceError(null);
      setVoiceLoading(true);
      setVoiceGenerated({ script: '', audioUrl: '' });

      const response = await fetch(`${API_BASE}/api/marketing-content/generate-voice`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          product: voiceProduct,
          purpose: voicePurpose,
          tone: voiceTone,
          language: voiceLanguage,
          gender: voiceGender,
          url: voiceUrl || undefined,
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data?.error || 'Gagal membuat voice over');

      setVoiceGenerated({
        script: data.script || '',
        audioUrl: data.audioUrl || '',
      });
    } catch (err: any) {
      setVoiceError(err?.message || 'Terjadi kesalahan');
    } finally {
      setVoiceLoading(false);
    }
  };

  // Step 1: Generate Concept & Narration
  const handleStep1 = async () => {
    try {
      setStepError({ ...stepError, 1: null });
      setStepLoading({ ...stepLoading, 1: true });
      setStep1Result(null);

      const response = await fetch(`${API_BASE}/api/marketing-content/generate-video-step1`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          product: videoProduct,
          purpose: videoPurpose,
          tone: videoTone,
          duration: parseInt(videoDuration),
          style: videoStyle,
          target_audience: videoTargetAudience,
          key_message: videoKeyMessage,
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data?.error || 'Gagal membuat konsep dan narasi');

      setStep1Result(data.data);
      setCurrentStep(2);
      // Auto-start Step 2
      setTimeout(() => {
        handleStep2();
      }, 500);
    } catch (err: any) {
      setStepError({ ...stepError, 1: err?.message || 'Terjadi kesalahan' });
    } finally {
      setStepLoading({ ...stepLoading, 1: false });
    }
  };

  // Step 2: Generate Voice Over
  const handleStep2 = async () => {
    if (!step1Result) return;

    try {
      setStepError({ ...stepError, 2: null });
      setStepLoading({ ...stepLoading, 2: true });
      setStep2Result(null);

      const response = await fetch(`${API_BASE}/api/marketing-content/generate-video-step2`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          narration: step1Result.narration,
          language: videoLanguage,
          gender: videoGender,
          tone: videoTone,
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data?.error || 'Gagal membuat voice over');

      setStep2Result(data.data);
      setCurrentStep(3);
      // Step 3 is coming soon, so we stop here
    } catch (err: any) {
      setStepError({ ...stepError, 2: err?.message || 'Terjadi kesalahan' });
    } finally {
      setStepLoading({ ...stepLoading, 2: false });
    }
  };

  // Reset Video Generator
  const resetVideoGenerator = () => {
    setCurrentStep(1);
    setStep1Result(null);
    setStep2Result(null);
    setStepLoading({ 1: false, 2: false });
    setStepError({});
  };

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);

    if (activeTab === 'caption') {
      setCaptionCopied(true);
      setTimeout(() => setCaptionCopied(false), 2000);
    } else if (activeTab === 'email') {
      setEmailCopied(prev => ({ ...prev, [type]: true }));
      setTimeout(() => setEmailCopied(prev => ({ ...prev, [type]: false })), 2000);
    } else if (activeTab === 'ads-copy') {
      setAdsCopied(prev => ({ ...prev, [type]: true }));
      setTimeout(() => setAdsCopied(prev => ({ ...prev, [type]: false })), 2000);
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Marketing Content Generator</h1>
        <p className="text-gray-600">Buat konten marketing yang menarik dengan AI menggunakan Vertex AI.</p>
      </div>

      {/* Main Tabs: Text, Voice, Video */}
      <div className="border-b border-gray-200 mb-4">
        <nav className="flex gap-6 text-sm">
          {([
            { id: 'text', label: 'Text Generator', icon: Type },
            { id: 'voice', label: 'Voice Generator', icon: Mic },
            { id: 'video', label: 'Video Generator', icon: Video },
          ] as const).map((t) => (
            <button
              key={t.id}
              onClick={() => setMainTab(t.id)}
              className={`pb-3 -mb-px border-b-2 flex items-center gap-2 ${mainTab === t.id
                ? 'border-green-600 text-green-700 font-medium'
                : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
            >
              <t.icon className="w-4 h-4" />
              {t.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Text Generator Content */}
      {mainTab === 'text' && (
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="border-b border-gray-200 px-6 pt-4">
            <nav className="flex gap-4 text-sm">
              {([
                { id: 'caption', label: 'Caption Generator' },
                { id: 'email', label: 'Email Marketing' },
                { id: 'ads-copy', label: 'Ads Copy' },
              ] as const).map((t) => (
                <button
                  key={t.id}
                  onClick={() => setActiveTab(t.id)}
                  className={`pb-3 -mb-px border-b-2 ${activeTab === t.id ? 'border-green-600 text-green-700 font-medium' : 'border-transparent text-gray-500 hover:text-gray-700'}`}
                >
                  {t.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">

            {activeTab === 'caption' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                      <FileText className="w-4 h-4 text-gray-500" />
                      <span>Detail Produk</span>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Deskripsi Produk/Layanan</label>
                        <textarea
                          value={captionProduct}
                          onChange={(e) => setCaptionProduct(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                          rows={4}
                          placeholder="Contoh: Sepatu sneakers putih minimalis, nyaman untuk aktivitas sehari-hari"
                        />
                      </div>

                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Platform</label>
                        <select
                          value={captionPlatform}
                          onChange={(e) => setCaptionPlatform(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                        >
                          <option value="instagram">Instagram</option>
                          <option value="facebook">Facebook</option>
                          <option value="twitter">Twitter/X</option>
                          <option value="tiktok">TikTok</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Tone</label>
                        <select
                          value={captionTone}
                          onChange={(e) => setCaptionTone(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                        >
                          <option value="casual">Casual</option>
                          <option value="professional">Professional</option>
                          <option value="friendly">Friendly</option>
                          <option value="enthusiastic">Enthusiastic</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm text-gray-700 mb-1">
                          Website URL <span className="text-gray-400 text-xs">(Opsional)</span>
                        </label>
                        <input
                          type="url"
                          value={captionUrl}
                          onChange={(e) => setCaptionUrl(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                          placeholder="https://example.com"
                        />
                        <p className="text-xs text-gray-500 mt-1">AI akan mempelajari konten dari website ini</p>
                      </div>
                    </div>

                    <div className="mt-4">
                      <button
                        onClick={handleCaptionGenerate}
                        disabled={captionLoading || !captionProduct}
                        className={`inline-flex items-center justify-center px-4 py-2 text-white text-sm rounded-md ${captionLoading || !captionProduct
                          ? 'bg-green-500 cursor-not-allowed opacity-70'
                          : 'bg-green-600 hover:bg-green-700'
                          }`}
                      >
                        <Wand2 className={`w-4 h-4 mr-2 ${captionLoading ? 'animate-pulse' : ''}`} />
                        <span>{captionLoading ? 'Membuat...' : 'Generate Caption'}</span>
                      </button>
                      {captionError && <p className="text-sm text-red-600 mt-2">{captionError}</p>}
                    </div>
                  </div>
                </div>

                <div>
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="font-medium text-gray-800 mb-3">Hasil Caption</div>
                    {captionGenerated ? (
                      <div className="space-y-3">
                        <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-sm text-gray-800 whitespace-pre-wrap">
                          {captionGenerated}
                        </div>
                        <button
                          onClick={() => copyToClipboard(captionGenerated, 'caption')}
                          className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                        >
                          {captionCopied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                          {captionCopied ? 'Tersalin!' : 'Salin Caption'}
                        </button>
                      </div>
                    ) : (
                      <div className="h-32 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500">
                        Belum ada hasil. Isi form dan klik "Generate Caption".
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'email' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                      <FileText className="w-4 h-4 text-gray-500" />
                      <span>Detail Email</span>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Deskripsi Produk/Layanan</label>
                        <textarea
                          value={emailProduct}
                          onChange={(e) => setEmailProduct(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                          rows={4}
                          placeholder="Contoh: Kursus online digital marketing untuk pemula"
                        />
                      </div>

                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Tujuan Email</label>
                        <select
                          value={emailPurpose}
                          onChange={(e) => setEmailPurpose(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                        >
                          <option value="promo">Promosi</option>
                          <option value="newsletter">Newsletter</option>
                          <option value="announcement">Pengumuman</option>
                          <option value="follow-up">Follow Up</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Tone</label>
                        <select
                          value={emailTone}
                          onChange={(e) => setEmailTone(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                        >
                          <option value="professional">Professional</option>
                          <option value="casual">Casual</option>
                          <option value="friendly">Friendly</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm text-gray-700 mb-1">
                          Website URL <span className="text-gray-400 text-xs">(Opsional)</span>
                        </label>
                        <input
                          type="url"
                          value={emailUrl}
                          onChange={(e) => setEmailUrl(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                          placeholder="https://example.com"
                        />
                        <p className="text-xs text-gray-500 mt-1">AI akan mempelajari konten dari website ini</p>
                      </div>
                    </div>

                    <div className="mt-4">
                      <button
                        onClick={handleEmailGenerate}
                        disabled={emailLoading || !emailProduct}
                        className={`inline-flex items-center justify-center px-4 py-2 text-white text-sm rounded-md ${emailLoading || !emailProduct
                          ? 'bg-green-500 cursor-not-allowed opacity-70'
                          : 'bg-green-600 hover:bg-green-700'
                          }`}
                      >
                        <Wand2 className={`w-4 h-4 mr-2 ${emailLoading ? 'animate-pulse' : ''}`} />
                        <span>{emailLoading ? 'Membuat...' : 'Generate Email'}</span>
                      </button>
                      {emailError && <p className="text-sm text-red-600 mt-2">{emailError}</p>}
                    </div>
                  </div>
                </div>

                <div>
                  <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
                    <div>
                      <div className="font-medium text-gray-800 mb-2">Subject Line</div>
                      {emailGenerated.subject ? (
                        <div className="space-y-2">
                          <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-sm text-gray-800">
                            {emailGenerated.subject}
                          </div>
                          <button
                            onClick={() => copyToClipboard(emailGenerated.subject, 'subject')}
                            className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                          >
                            {emailCopied.subject ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                            {emailCopied.subject ? 'Tersalin!' : 'Salin Subject'}
                          </button>
                        </div>
                      ) : (
                        <div className="h-16 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500">
                          Belum ada hasil
                        </div>
                      )}
                    </div>

                    <div>
                      <div className="font-medium text-gray-800 mb-2">Email Body</div>
                      {emailGenerated.body ? (
                        <div className="space-y-2">
                          <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-sm text-gray-800 whitespace-pre-wrap max-h-96 overflow-y-auto">
                            {emailGenerated.body}
                          </div>
                          <button
                            onClick={() => copyToClipboard(emailGenerated.body, 'body')}
                            className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                          >
                            {emailCopied.body ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                            {emailCopied.body ? 'Tersalin!' : 'Salin Body'}
                          </button>
                        </div>
                      ) : (
                        <div className="h-64 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500">
                          Belum ada hasil
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'ads-copy' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                      <Target className="w-4 h-4 text-gray-500" />
                      <span>Detail Iklan</span>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Deskripsi Produk/Layanan</label>
                        <textarea
                          value={adsProduct}
                          onChange={(e) => setAdsProduct(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                          rows={4}
                          placeholder="Contoh: Aplikasi manajemen keuangan untuk UMKM"
                        />
                      </div>

                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Target Audiens</label>
                        <input
                          type="text"
                          value={adsTarget}
                          onChange={(e) => setAdsTarget(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                          placeholder="Contoh: Pemilik UMKM usia 25-45 tahun"
                        />
                      </div>

                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Platform Iklan</label>
                        <select
                          value={adsPlatform}
                          onChange={(e) => setAdsPlatform(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                        >
                          <option value="facebook">Facebook Ads</option>
                          <option value="google">Google Ads</option>
                          <option value="instagram">Instagram Ads</option>
                          <option value="tiktok">TikTok Ads</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm text-gray-700 mb-1">
                          Website URL <span className="text-gray-400 text-xs">(Opsional)</span>
                        </label>
                        <input
                          type="url"
                          value={adsUrl}
                          onChange={(e) => setAdsUrl(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                          placeholder="https://example.com"
                        />
                        <p className="text-xs text-gray-500 mt-1">AI akan mempelajari konten dari website ini</p>
                      </div>
                    </div>

                    <div className="mt-4">
                      <button
                        onClick={handleAdsGenerate}
                        disabled={adsLoading || !adsProduct || !adsTarget}
                        className={`inline-flex items-center justify-center px-4 py-2 text-white text-sm rounded-md ${adsLoading || !adsProduct || !adsTarget
                          ? 'bg-green-500 cursor-not-allowed opacity-70'
                          : 'bg-green-600 hover:bg-green-700'
                          }`}
                      >
                        <Wand2 className={`w-4 h-4 mr-2 ${adsLoading ? 'animate-pulse' : ''}`} />
                        <span>{adsLoading ? 'Membuat...' : 'Generate Ads Copy'}</span>
                      </button>
                      {adsError && <p className="text-sm text-red-600 mt-2">{adsError}</p>}
                    </div>
                  </div>
                </div>

                <div>
                  <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
                    <div>
                      <div className="font-medium text-gray-800 mb-2">Headline</div>
                      {adsGenerated.headline ? (
                        <div className="space-y-2">
                          <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-sm text-gray-800">
                            {adsGenerated.headline}
                          </div>
                          <button
                            onClick={() => copyToClipboard(adsGenerated.headline, 'headline')}
                            className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                          >
                            {adsCopied.headline ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                            {adsCopied.headline ? 'Tersalin!' : 'Salin Headline'}
                          </button>
                        </div>
                      ) : (
                        <div className="h-16 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500">
                          Belum ada hasil
                        </div>
                      )}
                    </div>

                    <div>
                      <div className="font-medium text-gray-800 mb-2">Description</div>
                      {adsGenerated.description ? (
                        <div className="space-y-2">
                          <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-sm text-gray-800 whitespace-pre-wrap">
                            {adsGenerated.description}
                          </div>
                          <button
                            onClick={() => copyToClipboard(adsGenerated.description, 'description')}
                            className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                          >
                            {adsCopied.description ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                            {adsCopied.description ? 'Tersalin!' : 'Salin Description'}
                          </button>
                        </div>
                      ) : (
                        <div className="h-32 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500">
                          Belum ada hasil
                        </div>
                      )}
                    </div>

                    <div>
                      <div className="font-medium text-gray-800 mb-2">Call to Action</div>
                      {adsGenerated.cta ? (
                        <div className="space-y-2">
                          <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-sm text-gray-800">
                            {adsGenerated.cta}
                          </div>
                          <button
                            onClick={() => copyToClipboard(adsGenerated.cta, 'cta')}
                            className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                          >
                            {adsCopied.cta ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                            {adsCopied.cta ? 'Tersalin!' : 'Salin CTA'}
                          </button>
                        </div>
                      ) : (
                        <div className="h-16 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500">
                          Belum ada hasil
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Voice Generator */}
      {mainTab === 'voice' && (
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Input Form */}
            <div className="space-y-4">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                  <Mic className="w-4 h-4 text-gray-500" />
                  <span>Voice Over Settings</span>
                </div>

                <div className="space-y-3">
                  <div>
                    <label className="block text-sm text-gray-700 mb-1">Deskripsi Produk/Layanan</label>
                    <textarea
                      value={voiceProduct}
                      onChange={(e) => setVoiceProduct(e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                      rows={4}
                      placeholder="Contoh: Aplikasi kasir digital untuk UMKM, mudah digunakan, fitur lengkap"
                    />
                  </div>

                  <div>
                    <label className="block text-sm text-gray-700 mb-1">Tujuan Voice Over</label>
                    <select
                      value={voicePurpose}
                      onChange={(e) => setVoicePurpose(e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                    >
                      <option value="promo">Promosi Produk</option>
                      <option value="explainer">Explainer Video</option>
                      <option value="tutorial">Tutorial</option>
                      <option value="testimonial">Testimonial</option>
                      <option value="announcement">Pengumuman</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm text-gray-700 mb-1">Tone</label>
                    <select
                      value={voiceTone}
                      onChange={(e) => setVoiceTone(e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                    >
                      <option value="friendly">Friendly</option>
                      <option value="professional">Professional</option>
                      <option value="enthusiastic">Enthusiastic</option>
                      <option value="calm">Calm</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm text-gray-700 mb-1">Bahasa</label>
                      <select
                        value={voiceLanguage}
                        onChange={(e) => setVoiceLanguage(e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                      >
                        <option value="id-ID">Bahasa Indonesia</option>
                        <option value="en-US">English (US)</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm text-gray-700 mb-1">Suara</label>
                      <select
                        value={voiceGender}
                        onChange={(e) => setVoiceGender(e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                      >
                        <option value="female">Female</option>
                        <option value="male">Male</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm text-gray-700 mb-1">
                      Website URL <span className="text-gray-400 text-xs">(Opsional)</span>
                    </label>
                    <input
                      type="url"
                      value={voiceUrl}
                      onChange={(e) => setVoiceUrl(e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                      placeholder="https://example.com"
                    />
                    <p className="text-xs text-gray-500 mt-1">AI akan mempelajari konten dari website ini</p>
                  </div>
                </div>

                <div className="mt-4">
                  <button
                    onClick={handleVoiceGenerate}
                    disabled={voiceLoading || !voiceProduct}
                    className={`inline-flex items-center justify-center px-4 py-2 text-white text-sm rounded-md w-full ${voiceLoading || !voiceProduct
                      ? 'bg-green-500 cursor-not-allowed opacity-70'
                      : 'bg-green-600 hover:bg-green-700'
                      }`}
                  >
                    <Wand2 className={`w-4 h-4 mr-2 ${voiceLoading ? 'animate-pulse' : ''}`} />
                    <span>{voiceLoading ? 'Membuat Voice Over...' : 'Generate Voice Over'}</span>
                  </button>
                  {voiceError && <p className="text-sm text-red-600 mt-2">{voiceError}</p>}
                </div>
              </div>
            </div>

            {/* Output */}
            <div className="space-y-4">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="font-medium text-gray-800 mb-3">Narasi Script</div>
                {voiceGenerated.script ? (
                  <div className="space-y-2">
                    <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-sm text-gray-800 whitespace-pre-wrap max-h-64 overflow-y-auto">
                      {voiceGenerated.script}
                    </div>
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(voiceGenerated.script);
                        setVoiceCopied(true);
                        setTimeout(() => setVoiceCopied(false), 2000);
                      }}
                      className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                    >
                      {voiceCopied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                      {voiceCopied ? 'Tersalin!' : 'Salin Script'}
                    </button>
                  </div>
                ) : (
                  <div className="h-32 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500">
                    Belum ada hasil
                  </div>
                )}
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="font-medium text-gray-800 mb-3">Audio Voice Over</div>
                {voiceGenerated.audioUrl ? (
                  <div className="space-y-3">
                    <audio controls className="w-full">
                      <source src={voiceGenerated.audioUrl} type="audio/wav" />
                      <source src={voiceGenerated.audioUrl} type="audio/mpeg" />
                      Browser Anda tidak support audio player.
                    </audio>
                    <div className="flex gap-2">
                      <a
                        href={voiceGenerated.audioUrl}
                        download="voice-over.wav"
                        className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-white bg-green-600 hover:bg-green-700 rounded-md"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Download Audio
                      </a>
                      <span className="text-xs text-gray-500 self-center">
                        Powered by Gemini 2.5 Pro TTS
                      </span>
                    </div>
                  </div>
                ) : (
                  <div className="h-32 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500">
                    Belum ada audio
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Video Generator - 4 Step Algorithm */}
      {mainTab === 'video' && (
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
          {/* Progress Steps */}
          <div className="border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-800">Video Generator (4-Step Algorithm)</h3>
              <button
                onClick={resetVideoGenerator}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Reset
              </button>
            </div>
            <div className="mt-4 flex items-center">
              {[1, 2, 3, 4].map((step) => (
                <div key={step} className="flex items-center">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${currentStep > step
                      ? 'bg-green-600 text-white'
                      : currentStep === step
                        ? step >= 3
                          ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                          : 'bg-blue-600 text-white'
                        : step >= 3
                          ? 'bg-gray-300 text-gray-400'
                          : 'bg-gray-200 text-gray-500'
                      }`}
                  >
                    {currentStep > step ? '✓' : step >= 3 ? '🚀' : step}
                  </div>
                  <div className="ml-2 text-sm">
                    <div className={`font-medium ${currentStep >= step ? 'text-gray-800' : step >= 3 ? 'text-gray-400' : 'text-gray-500'}`}>
                      {step === 1 && 'Konsep & Narasi'}
                      {step === 2 && 'Voice Over'}
                      {step === 3 && 'Silent Video (Coming Soon)'}
                      {step === 4 && 'Final Video (Coming Soon)'}
                    </div>
                  </div>
                  {step < 4 && <div className="mx-4 w-8 h-px bg-gray-300" />}
                </div>
              ))}
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Input Form */}
              <div className="space-y-4">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                    <Video className="w-4 h-4 text-gray-500" />
                    <span>Video Settings</span>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm text-gray-700 mb-1">Nama Produk/Layanan</label>
                      <input
                        type="text"
                        value={videoProduct}
                        onChange={(e) => setVideoProduct(e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                        placeholder="Contoh: Kopi Arabica Premium"
                        disabled={currentStep > 1}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Tujuan</label>
                        <select
                          value={videoPurpose}
                          onChange={(e) => setVideoPurpose(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                          disabled={currentStep > 1}
                        >
                          <option value="marketing">Marketing</option>
                          <option value="promo">Promosi</option>
                          <option value="explainer">Explainer</option>
                          <option value="brand">Brand Story</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Tone</label>
                        <select
                          value={videoTone}
                          onChange={(e) => setVideoTone(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                          disabled={currentStep > 1}
                        >
                          <option value="friendly">Friendly</option>
                          <option value="professional">Professional</option>
                          <option value="enthusiastic">Enthusiastic</option>
                          <option value="calm">Calm</option>
                        </select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Durasi</label>
                        <select
                          value={videoDuration}
                          onChange={(e) => setVideoDuration(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                          disabled={currentStep > 1}
                        >
                          <option value="10">10 detik</option>
                          <option value="15">15 detik</option>
                          <option value="30">30 detik</option>
                          <option value="60">60 detik</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm text-gray-700 mb-1">Style</label>
                        <select
                          value={videoStyle}
                          onChange={(e) => setVideoStyle(e.target.value)}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                          disabled={currentStep > 1}
                        >
                          <option value="modern">Modern</option>
                          <option value="cinematic">Cinematic</option>
                          <option value="minimal">Minimal</option>
                          <option value="dynamic">Dynamic</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm text-gray-700 mb-1">Target Audience</label>
                      <input
                        type="text"
                        value={videoTargetAudience}
                        onChange={(e) => setVideoTargetAudience(e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                        placeholder="Contoh: coffee lovers, anak muda"
                        disabled={currentStep > 1}
                      />
                    </div>

                    <div>
                      <label className="block text-sm text-gray-700 mb-1">Key Message</label>
                      <input
                        type="text"
                        value={videoKeyMessage}
                        onChange={(e) => setVideoKeyMessage(e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                        placeholder="Contoh: Kopi terbaik dengan cita rasa yang kaya"
                        disabled={currentStep > 1}
                      />
                    </div>

                    {currentStep >= 2 && (
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm text-gray-700 mb-1">Bahasa Voice</label>
                          <select
                            value={videoLanguage}
                            onChange={(e) => setVideoLanguage(e.target.value)}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                            disabled={currentStep > 2}
                          >
                            <option value="id-ID">Bahasa Indonesia</option>
                            <option value="en-US">English (US)</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm text-gray-700 mb-1">Gender Voice</label>
                          <select
                            value={videoGender}
                            onChange={(e) => setVideoGender(e.target.value)}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600"
                            disabled={currentStep > 2}
                          >
                            <option value="female">Female</option>
                            <option value="male">Male</option>
                          </select>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-4 space-y-2">
                    {currentStep === 1 && (
                      <button
                        onClick={handleStep1}
                        disabled={stepLoading[1] || !videoProduct}
                        className={`inline-flex items-center justify-center px-4 py-2 text-white text-sm rounded-md w-full ${stepLoading[1] || !videoProduct
                          ? 'bg-green-500 cursor-not-allowed opacity-70'
                          : 'bg-green-600 hover:bg-green-700'
                          }`}
                      >
                        <Wand2 className={`w-4 h-4 mr-2 ${stepLoading[1] ? 'animate-pulse' : ''}`} />
                        <span>{stepLoading[1] ? 'Membuat Konsep...' : 'Step 1: Generate Konsep & Narasi'}</span>
                      </button>
                    )}

                    {currentStep === 2 && (
                      <button
                        onClick={handleStep2}
                        disabled={stepLoading[2]}
                        className={`inline-flex items-center justify-center px-4 py-2 text-white text-sm rounded-md w-full ${stepLoading[2]
                          ? 'bg-green-500 cursor-not-allowed opacity-70'
                          : 'bg-green-600 hover:bg-green-700'
                          }`}
                      >
                        <Wand2 className={`w-4 h-4 mr-2 ${stepLoading[2] ? 'animate-pulse' : ''}`} />
                        <span>{stepLoading[2] ? 'Membuat Voice Over...' : 'Step 2: Generate Voice Over'}</span>
                      </button>
                    )}

                    {currentStep === 3 && (
                      <div className="space-y-2">
                        <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm rounded-md p-4 text-center">
                          <div className="flex items-center justify-center gap-2 mb-2">
                            <Video className="w-5 h-5" />
                            <span className="font-medium">Step 3: Silent Video Generation</span>
                          </div>
                          <div className="text-blue-100 text-xs mb-3">
                            Fitur ini menggunakan Google Veo 3.0 untuk generate video berkualitas tinggi
                          </div>
                          <div className="bg-white/20 rounded-md p-3 mb-3">
                            <div className="text-lg font-bold mb-1">🚀 Coming Soon</div>
                            <div className="text-xs text-blue-100">
                              Sedang dalam pengembangan untuk memberikan pengalaman terbaik
                            </div>
                          </div>
                          <div className="text-xs text-blue-100">
                            Saat ini Anda sudah bisa mendapatkan konsep video dan voice over yang siap digunakan!
                          </div>
                        </div>
                      </div>
                    )}

                    {currentStep === 4 && (
                      <div className="bg-gradient-to-r from-purple-500 to-pink-600 text-white text-sm rounded-md p-4 text-center">
                        <div className="flex items-center justify-center gap-2 mb-2">
                          <Video className="w-5 h-5" />
                          <span className="font-medium">Step 4: Final Video Creation</span>
                        </div>
                        <div className="text-purple-100 text-xs mb-3">
                          Menggabungkan silent video dengan voice over menggunakan FFmpeg
                        </div>
                        <div className="bg-white/20 rounded-md p-3 mb-3">
                          <div className="text-lg font-bold mb-1">🚀 Coming Soon</div>
                          <div className="text-xs text-purple-100">
                            Akan tersedia setelah Step 3 selesai dikembangkan
                          </div>
                        </div>
                        <div className="text-xs text-purple-100">
                          Sementara ini, Anda bisa menggunakan konsep dan voice over untuk membuat video secara manual
                        </div>
                      </div>
                    )}

                    {/* Error Messages */}
                    {Object.entries(stepError).map(([step, error]) =>
                      error && (
                        <p key={step} className="text-sm text-red-600 mt-2">
                          Step {step}: {error}
                        </p>
                      )
                    )}
                  </div>
                </div>
              </div>

              {/* Output Results */}
              <div className="space-y-4">
                {/* Step 1 Results */}
                {step1Result && (
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                      <span className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs">✓</span>
                      Step 1: Konsep & Narasi
                    </div>
                    <div className="space-y-3">
                      <div>
                        <div className="text-sm font-medium text-gray-700 mb-1">Konsep Video</div>
                        <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-sm text-gray-800 whitespace-pre-wrap max-h-32 overflow-y-auto">
                          {step1Result.concept}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-700 mb-1">Narasi Script</div>
                        <div className="bg-gray-50 border border-gray-200 rounded-md p-3 text-sm text-gray-800 whitespace-pre-wrap max-h-24 overflow-y-auto">
                          {step1Result.narration}
                        </div>
                      </div>
                      <div className="text-xs text-gray-500">
                        Durasi: {step1Result.duration} detik
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 2 Results */}
                {step2Result && (
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                      <span className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs">✓</span>
                      Step 2: Voice Over
                    </div>
                    <div className="space-y-3">
                      <div>
                        <div className="text-sm font-medium text-gray-700 mb-1">Audio Voice Over</div>
                        <audio controls className="w-full">
                          <source src={step2Result.audio_url} type="audio/wav" />
                          Browser Anda tidak support audio player.
                        </audio>
                      </div>
                      <div className="flex gap-2">
                        <a
                          href={step2Result.audio_url}
                          download="voice-over.wav"
                          className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-white bg-green-600 hover:bg-green-700 rounded-md"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                          </svg>
                          Download Audio
                        </a>
                        <span className="text-xs text-gray-500 self-center">
                          {step2Result.language} • {step2Result.gender} • {step2Result.tone}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 3 & 4 Coming Soon Message */}
                {currentStep >= 3 && (
                  <div className="bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 text-center">
                    <div className="text-2xl mb-3">🚀</div>
                    <div className="text-lg font-semibold text-gray-800 mb-2">Video Generation Coming Soon!</div>
                    <div className="text-sm text-gray-600 mb-4">
                      Fitur Step 3 (Silent Video) dan Step 4 (Final Video) sedang dalam pengembangan untuk memberikan pengalaman terbaik.
                    </div>
                    <div className="bg-white rounded-md p-4 mb-4">
                      <div className="text-sm font-medium text-gray-700 mb-2">Yang sudah bisa Anda gunakan:</div>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                          Konsep video yang detail dan menarik
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                          Voice over audio berkualitas tinggi
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                          Panduan untuk membuat video secara manual
                        </div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      Sementara ini, Anda bisa menggunakan konsep dan voice over untuk membuat video marketing secara manual menggunakan tools video editing favorit Anda.
                    </div>
                  </div>
                )}



                {/* Empty State */}
                {!step1Result && !step2Result && currentStep < 3 && (
                  <div className="bg-gray-50 border border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <Video className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <div className="text-sm text-gray-600 mb-2">Belum ada hasil</div>
                    <div className="text-xs text-gray-500">
                      Isi form di sebelah kiri dan mulai dengan Step 1 untuk membuat konsep dan voice over
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketingContentGeneratorPage;

