import OpenAI, { toFile } from "openai";
import fs from "fs";

const openai = new OpenAI({ apiKey: '***************************************************' });

async function generateWithReference() {
    const imageFiles = [
        "keyboard.png",
    ];

    const images = await Promise.all(
        imageFiles.map(async (file) =>
            await toFile(fs.createReadStream(file), null, {
                type: "image/png",
            })
        ),
    );

    const result = await openai.images.edit({
        model: "gpt-image-1",
        prompt: `
📸 Photo Enhancer untuk Produk Ajazz Keyboard:

🖼️ Gambar Produk: gunakan file Ajazz Keyboard (produk utama tetap jelas).
📝 Headline: "Unleash Your Typing Experience"
📝 Subheadline: "Ajazz Mechanical Keyboard – Tactile Precision, RGB Elegance"

🎨 Instruksi Gaya & Latar Belakang:
- Clean, modern, dan futuristik
- <PERSON>tar belakang warna gelap elegan (hitam, abu-abu metalik) dengan aksen neon atau RGB
- Nuansa gaming premium namun tetap stylish untuk workstation
- Efek cahaya RGB berpendar di sekitar keyboard

🌐 Referensi Gaya:
https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-enhanced-1758265409809.png

✨ Detailing Produk:
- Tonjolkan tekstur keycaps (matte / glossy)
- Detail bordir/jahitan pada wrist rest (jika ada)
- Refleksi metalik pada body keyboard
- Highlight cahaya RGB tiap tombol
- Komposisi material (ABS/PBT keycaps, alumunium body)

🎥 Sudut Kamera:
Variasi sudut: macro shot pada keycaps, close-up tombol dengan RGB menyala, wide angle untuk keseluruhan keyboard di meja kerja, low angle dramatis dengan pencahayaan neon, high angle untuk tampilan layout penuh.

📐 Aspect Ratio: 16:9 (banner & iklan landscape)
📷 Jumlah Gambar: 4 variasi desain
    `,
        size: "1024x1024",
        n: 1,
        image: images,
    });

    const imageBase64 = result.data[0].b64_json;
    const buffer = Buffer.from(imageBase64, "base64");
    fs.writeFileSync("output-imgenh.png", buffer);
}

generateWithReference();
