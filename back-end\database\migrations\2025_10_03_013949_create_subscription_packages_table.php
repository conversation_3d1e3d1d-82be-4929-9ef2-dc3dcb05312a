<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_packages', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "Paket Mulai Jualan", "Paket Serius Cuan"
            $table->string('slug')->unique(); // e.g., "mulai-jualan", "serius-cuan"
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2); // Harga dalam rupiah
            $table->integer('duration_months'); // Du<PERSON>i dalam bulan (1, 3, 12)
            $table->string('duration_label'); // e.g., "per bulan", "per 3 bulan", "per tahun"
            $table->json('features')->nullable(); // Fitur-fitur yang didapat
            $table->string('badge')->nullable(); // e.g., "Popular", "Best Value"
            $table->string('color')->default('blue'); // Warna theme untuk UI
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_packages');
    }
};
