<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "Bank Transfer", "Credit Card", "E-Wallet"
            $table->string('type'); // e.g., "bank", "card", "ewallet", "cash"
            $table->text('description')->nullable();
            $table->json('details')->nullable(); // Store bank details, account numbers, etc.
            $table->string('icon')->nullable(); // Icon URL or name
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};
