<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\StreamedResponse;

class HppExportController extends Controller
{
    public function exportRitel(Request $request)
    {
        $validated = $request->validate([
            'produk' => 'nullable|array',
            'produk.nama' => 'nullable|string',
            'ringkasan' => 'nullable|array',
            'ringkasan.total_var' => 'nullable|numeric',
            'ringkasan.total_alloc' => 'nullable|numeric',
            'ringkasan.hpp' => 'nullable|numeric',
            'var_costs' => 'nullable|array',
            'var_costs.*.bahan' => 'nullable|string',
            'var_costs.*.pakai_qty' => 'nullable|numeric',
            'var_costs.*.pakai_unit' => 'nullable|string',
            'var_costs.*.biaya_per_produk' => 'nullable|numeric',
            'var_costs.*.total_harga' => 'nullable|numeric',
            'var_costs.*.beli_qty' => 'nullable|numeric',
            'var_costs.*.beli_unit' => 'nullable|string',
            'fix_allocs' => 'nullable|array',
            'fix_allocs.*.nama' => 'nullable|string',
            'fix_allocs.*.alokasi' => 'nullable|numeric',
            'price' => 'nullable|array',
            'price.harga_pilihan' => 'nullable|numeric',
            'price.margin_pct' => 'nullable|numeric',
            'price.profit_per_unit' => 'nullable|numeric',
            'price_suggestions' => 'nullable|array',
            'price_suggestions.*.level' => 'nullable|string',
            'price_suggestions.*.harga' => 'nullable|numeric',
            'price_suggestions.*.profit_per_unit' => 'nullable|numeric',
            'price_suggestions.*.margin_pct' => 'nullable|numeric',
            'price_suggestions.*.catatan' => 'nullable|string',
            'projection' => 'nullable|array',
            'projection.target_laba_bulan' => 'nullable|numeric',
            'projection.qty_day' => 'nullable|numeric',
            'projection.qty_month' => 'nullable|numeric',
            'projection.omset' => 'nullable|numeric',
            'projection.biaya_produksi' => 'nullable|numeric',
            'projection.fix_month' => 'nullable|numeric',
            'projection.laba_bersih' => 'nullable|numeric',
        ]);

        $produk = $validated['produk']['nama'] ?? 'Produk';
        $ringkasan = $validated['ringkasan'] ?? [];
        $totalVar = (int) round((float)($ringkasan['total_var'] ?? 0));
        $totalAlloc = (int) round((float)($ringkasan['total_alloc'] ?? 0));
        $hpp = (int) round((float)($ringkasan['hpp'] ?? ($totalVar + $totalAlloc)));
        $varCosts = $validated['var_costs'] ?? [];
        $fixAllocs = $validated['fix_allocs'] ?? [];
        $price = $validated['price'] ?? [];
        $priceSugs = $validated['price_suggestions'] ?? [];
        $proj = $validated['projection'] ?? [];

        $spreadsheet = new Spreadsheet();
        $sheet1 = $spreadsheet->getActiveSheet();
        $sheet1->setTitle('Ringkasan HPP');

        $r = 1;
        $sheet1->setCellValue("A{$r}", 'Produk');
        $sheet1->setCellValue("B{$r}", $produk);
        $r += 2;

        // Biaya variabel per produk
        $sheet1->setCellValue("A{$r}", 'Biaya variabel per produk');
        $r++;
        $sheet1->fromArray(['Bahan', 'Pakai Qty', 'Pakai Unit', 'Biaya/Produk'], null, "A{$r}");
        $r++;
        foreach ($varCosts as $v) {
            $sheet1->fromArray([
                (string)($v['bahan'] ?? ''),
                (string)($v['pakai_qty'] ?? ''),
                (string)($v['pakai_unit'] ?? ''),
                (int) round((float)($v['biaya_per_produk'] ?? 0)),
            ], null, "A{$r}");
            $r++;
        }
        $sheet1->fromArray(['Total biaya variabel', '', '', $totalVar], null, "A{$r}");
        $r += 2;

        // Alokasi tetap per produk
        $sheet1->setCellValue("A{$r}", 'Alokasi biaya tetap per produk');
        $r++;
        $sheet1->fromArray(['Nama biaya', 'Alokasi/Produk'], null, "A{$r}");
        $r++;
        foreach ($fixAllocs as $f) {
            $sheet1->fromArray([
                (string)($f['nama'] ?? ''),
                (int) round((float)($f['alokasi'] ?? 0)),
            ], null, "A{$r}");
            $r++;
        }
        $sheet1->fromArray(['Total alokasi tetap', $totalAlloc], null, "A{$r}");
        $r += 2;

        // HPP, Harga & Profit
        $hargaPilihan = (int) round((float)($price['harga_pilihan'] ?? 0));
        $profitUnit = (int) round((float)($price['profit_per_unit'] ?? max(0, $hargaPilihan - $totalVar)));
        $marginPct = (float)($price['margin_pct'] ?? ($hargaPilihan > 0 ? ($profitUnit / $hargaPilihan) * 100.0 : 0));
        $sheet1->fromArray(['HPP/Produk', $hpp], null, "A{$r}");
        $r++;
        $sheet1->fromArray(['Harga jual pilihan', $hargaPilihan], null, "A{$r}");
        $r++;
        $sheet1->fromArray(['Profit/Unit', $profitUnit], null, "A{$r}");
        $r++;
        $sheet1->fromArray(['Margin (%)', round($marginPct, 1)], null, "A{$r}");
        $r += 2;

        // Sheet 2: Saran Harga
        $sheet2 = $spreadsheet->createSheet();
        $sheet2->setTitle('Saran Harga AI');
        $sheet2->fromArray(['Level', 'Harga', 'Profit/Unit', 'Margin (%)', 'Catatan'], null, 'A1');
        $r2 = 2;
        foreach ($priceSugs as $s) {
            $sheet2->fromArray([
                (string)($s['level'] ?? ''),
                (int) round((float)($s['harga'] ?? 0)),
                (int) round((float)($s['profit_per_unit'] ?? 0)),
                (float)($s['margin_pct'] ?? 0),
                (string)($s['catatan'] ?? ''),
            ], null, "A{$r2}");
            $r2++;
        }

        // Sheet 3: Proyeksi (jika ada)
        if (!empty($proj)) {
            $sheet3 = $spreadsheet->createSheet();
            $sheet3->setTitle('Proyeksi');
            $sheet3->fromArray(['Target laba/bulan', (int) round((float)($proj['target_laba_bulan'] ?? 0))], null, 'A1');
            $sheet3->fromArray(['Target jual/hari', (int) round((float)($proj['qty_day'] ?? 0))], null, 'A2');
            $sheet3->fromArray(['Total jual/bulan', (int) round((float)($proj['qty_month'] ?? 0))], null, 'A3');
            $sheet3->fromArray(['Omset/bulan', (int) round((float)($proj['omset'] ?? 0))], null, 'A4');
            $sheet3->fromArray(['Total biaya produksi/bulan', (int) round((float)($proj['biaya_produksi'] ?? 0))], null, 'A5');
            $sheet3->fromArray(['Total biaya tetap/bulan', (int) round((float)($proj['fix_month'] ?? 0))], null, 'A6');
            $sheet3->fromArray(['Proyeksi laba bersih/bulan', (int) round((float)($proj['laba_bersih'] ?? 0))], null, 'A7');
        }

        $filename = 'HPP_' . preg_replace('/[^A-Za-z0-9_\-]+/', '_', $produk) . '_' . date('Ymd') . '.xlsx';

        $response = new StreamedResponse(function () use ($spreadsheet) {
            $writer = new Xlsx($spreadsheet);
            // Output to php://output
            $writer->save('php://output');
        });
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $filename . '"');
        $response->headers->set('Cache-Control', 'max-age=0');
        return $response;
    }

    public function exportManufaktur(Request $request)
    {
        $validated = $request->validate([
            'produk' => 'nullable|array',
            'produk.nama' => 'nullable|string',

            'bahan' => 'nullable|array',
            'bahan.*.nama' => 'nullable|string',
            'bahan.*.total_bulan' => 'nullable|numeric',
            'bahan.*.alokasi_per_produk' => 'nullable|numeric',

            'tkl' => 'nullable|array',
            'tkl.*.jenis' => 'nullable|string',
            'tkl.*.total_upah' => 'nullable|numeric',
            'tkl.*.alokasi_per_produk' => 'nullable|numeric',

            'ohp' => 'nullable|array',
            'ohp.*.nama' => 'nullable|string',
            'ohp.*.total_bulan' => 'nullable|numeric',
            'ohp.*.alokasi_per_produk' => 'nullable|numeric',

            'ringkasan' => 'nullable|array',
            'ringkasan.total_bahan_bln' => 'nullable|numeric',
            'ringkasan.total_tkl_bln' => 'nullable|numeric',
            'ringkasan.total_ohp_bln' => 'nullable|numeric',
            'ringkasan.total_biaya_bln' => 'nullable|numeric',
            'ringkasan.target_output' => 'nullable|numeric',
            'ringkasan.hpp_per_pcs' => 'nullable|numeric',

            'price' => 'nullable|array',
            'price.harga_pilihan' => 'nullable|numeric',
            'price.profit_per_unit' => 'nullable|numeric',
            'price.margin_pct' => 'nullable|numeric',

            'price_suggestions' => 'nullable|array',
            'price_suggestions.*.level' => 'nullable|string',
            'price_suggestions.*.harga' => 'nullable|numeric',
            'price_suggestions.*.profit_per_unit' => 'nullable|numeric',
            'price_suggestions.*.margin_pct' => 'nullable|numeric',
            'price_suggestions.*.catatan' => 'nullable|string',

            'projection' => 'nullable|array',
            'projection.target_laba_bulan' => 'nullable|numeric',
            'projection.qty_day' => 'nullable|numeric',
            'projection.qty_month' => 'nullable|numeric',
            'projection.omset' => 'nullable|numeric',
            'projection.biaya_produksi' => 'nullable|numeric',
            'projection.fix_month' => 'nullable|numeric',
            'projection.laba_bersih' => 'nullable|numeric',
        ]);

        $produk = $validated['produk']['nama'] ?? 'Produk';
        $bahan = $validated['bahan'] ?? [];
        $tkl = $validated['tkl'] ?? [];
        $ohp = $validated['ohp'] ?? [];
        $ring = $validated['ringkasan'] ?? [];
        $price = $validated['price'] ?? [];
        $priceSugs = $validated['price_suggestions'] ?? [];
        $proj = $validated['projection'] ?? [];

        $spreadsheet = new Spreadsheet();
        $sheet1 = $spreadsheet->getActiveSheet();
        $sheet1->setTitle('Ringkasan HPP');

        $r = 1;
        $sheet1->setCellValue("A{$r}", 'Produk');
        $sheet1->setCellValue("B{$r}", $produk);
        $r += 2;

        // Biaya variabel per produk (Bahan + TKL)
        $sheet1->setCellValue("A{$r}", 'Biaya variabel per produk');
        $r++;
        $sheet1->fromArray(['Nama', 'Jenis', 'Total/Bulan', 'Alokasi/Produk'], null, "A{$r}");
        $r++;
        foreach ($bahan as $b) {
            $sheet1->fromArray([
                (string)($b['nama'] ?? ''),
                'Bahan',
                (int) round((float)($b['total_bulan'] ?? 0)),
                (int) round((float)($b['alokasi_per_produk'] ?? 0)),
            ], null, "A{$r}");
            $r++;
        }
        foreach ($tkl as $t) {
            $sheet1->fromArray([
                (string)($t['jenis'] ?? ''),
                'TKL',
                (int) round((float)($t['total_upah'] ?? 0)),
                (int) round((float)($t['alokasi_per_produk'] ?? 0)),
            ], null, "A{$r}");
            $r++;
        }
        $sheet1->fromArray([
            'Total variabel/bulan',
            '',
            (int) round((float)($ring['total_bahan_bln'] ?? 0) + (float)($ring['total_tkl_bln'] ?? 0)),
            ''
        ], null, "A{$r}");
        $r += 2;

        // Alokasi biaya tetap per produk (OHP)
        $sheet1->setCellValue("A{$r}", 'Alokasi biaya tetap per produk');
        $r++;
        $sheet1->fromArray(['Nama biaya', 'Total/Bulan', 'Alokasi/Produk'], null, "A{$r}");
        $r++;
        foreach ($ohp as $o) {
            $sheet1->fromArray([
                (string)($o['nama'] ?? ''),
                (int) round((float)($o['total_bulan'] ?? 0)),
                (int) round((float)($o['alokasi_per_produk'] ?? 0)),
            ], null, "A{$r}");
            $r++;
        }
        $sheet1->fromArray([
            'Total tetap/bulan',
            (int) round((float)($ring['total_ohp_bln'] ?? 0)),
            ''
        ], null, "A{$r}");
        $r += 2;

        // Ringkasan HPP, Harga, Profit
        $sheet1->fromArray(['Output/bulan (pcs)', (int) round((float)($ring['target_output'] ?? 0))], null, "A{$r}");
        $r++;
        $sheet1->fromArray(['HPP/Produk', (int) round((float)($ring['hpp_per_pcs'] ?? 0))], null, "A{$r}");
        $r++;

        $hargaPilihan = (int) round((float)($price['harga_pilihan'] ?? 0));
        $profitUnit = (int) round((float)($price['profit_per_unit'] ?? 0));
        $marginPct = (float)($price['margin_pct'] ?? 0);
        $sheet1->fromArray(['Harga jual pilihan', $hargaPilihan], null, "A{$r}");
        $r++;
        $sheet1->fromArray(['Profit/Unit', $profitUnit], null, "A{$r}");
        $r++;
        $sheet1->fromArray(['Margin (%)', round($marginPct, 1)], null, "A{$r}");
        $r += 2;

        // Sheet 2: Saran Harga
        $sheet2 = $spreadsheet->createSheet();
        $sheet2->setTitle('Saran Harga AI');
        $sheet2->fromArray(['Level', 'Harga', 'Profit/Unit', 'Margin (%)', 'Catatan'], null, 'A1');
        $r2 = 2;
        foreach ($priceSugs as $s) {
            $sheet2->fromArray([
                (string)($s['level'] ?? ''),
                (int) round((float)($s['harga'] ?? 0)),
                (int) round((float)($s['profit_per_unit'] ?? 0)),
                (float)($s['margin_pct'] ?? 0),
                (string)($s['catatan'] ?? ''),
            ], null, "A{$r2}");
            $r2++;
        }

        // Sheet 3: Proyeksi (jika ada)
        if (!empty($proj)) {
            $sheet3 = $spreadsheet->createSheet();
            $sheet3->setTitle('Proyeksi');
            $sheet3->fromArray(['Target laba/bulan', (int) round((float)($proj['target_laba_bulan'] ?? 0))], null, 'A1');
            $sheet3->fromArray(['Target jual/hari', (int) round((float)($proj['qty_day'] ?? 0))], null, 'A2');
            $sheet3->fromArray(['Total jual/bulan', (int) round((float)($proj['qty_month'] ?? 0))], null, 'A3');
            $sheet3->fromArray(['Omset/bulan', (int) round((float)($proj['omset'] ?? 0))], null, 'A4');
            $sheet3->fromArray(['Total biaya produksi/bulan', (int) round((float)($proj['biaya_produksi'] ?? 0))], null, 'A5');
            $sheet3->fromArray(['Total biaya tetap/bulan', (int) round((float)($proj['fix_month'] ?? 0))], null, 'A6');
            $sheet3->fromArray(['Proyeksi laba bersih/bulan', (int) round((float)($proj['laba_bersih'] ?? 0))], null, 'A7');
        }

        $filename = 'HPP_' . preg_replace('/[^A-Za-z0-9_\-]+/', '_', $produk) . '_' . date('Ymd') . '.xlsx';
        $response = new StreamedResponse(function () use ($spreadsheet) {
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
        });
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $filename . '"');
        $response->headers->set('Cache-Control', 'max-age=0');
        return $response;
    }


    public function exportJasa(Request $request)
    {
        $validated = $request->validate([
            'layanan' => 'nullable|array',
            'layanan.nama' => 'nullable|string',

            'direct' => 'nullable|array',
            'direct.*.nama' => 'nullable|string',
            'direct.*.harga' => 'nullable|numeric',

            'opex' => 'nullable|array',
            'opex.*.nama' => 'nullable|string',
            'opex.*.total_bulan' => 'nullable|numeric',
            'opex.*.alokasi_per_layanan' => 'nullable|numeric',

            'ringkasan' => 'nullable|array',
            'ringkasan.direct_per_layanan' => 'nullable|numeric',
            'ringkasan.opex_bulan' => 'nullable|numeric',
            'ringkasan.target_layanan' => 'nullable|numeric',
            'ringkasan.hpp_per_layanan' => 'nullable|numeric',

            'price' => 'nullable|array',
            'price.harga_pilihan' => 'nullable|numeric',
            'price.profit_per_unit' => 'nullable|numeric',
            'price.margin_pct' => 'nullable|numeric',

            'price_suggestions' => 'nullable|array',
            'price_suggestions.*.level' => 'nullable|string',
            'price_suggestions.*.harga' => 'nullable|numeric',
            'price_suggestions.*.profit_per_unit' => 'nullable|numeric',
            'price_suggestions.*.margin_pct' => 'nullable|numeric',
            'price_suggestions.*.catatan' => 'nullable|string',

            'projection' => 'nullable|array',
            'projection.target_laba_bulan' => 'nullable|numeric',
            'projection.qty_day' => 'nullable|numeric',
            'projection.qty_month' => 'nullable|numeric',
            'projection.omset' => 'nullable|numeric',
            'projection.biaya_produksi' => 'nullable|numeric',
            'projection.fix_month' => 'nullable|numeric',
            'projection.laba_bersih' => 'nullable|numeric',
        ]);

        $nama = $validated['layanan']['nama'] ?? 'Layanan';
        $direct = $validated['direct'] ?? [];
        $opex = $validated['opex'] ?? [];
        $ring = $validated['ringkasan'] ?? [];
        $price = $validated['price'] ?? [];
        $priceSugs = $validated['price_suggestions'] ?? [];
        $proj = $validated['projection'] ?? [];

        $spreadsheet = new Spreadsheet();
        $sheet1 = $spreadsheet->getActiveSheet();
        $sheet1->setTitle('Ringkasan HPP');

        $r = 1;
        $sheet1->setCellValue("A{$r}", 'Layanan');
        $sheet1->setCellValue("B{$r}", $nama);
        $r += 2;

        // Biaya langsung per layanan
        $sheet1->setCellValue("A{$r}", 'Biaya langsung per layanan');
        $r++;
        $sheet1->fromArray(['Nama biaya', 'Harga/Layanan'], null, "A{$r}");
        $r++;
        foreach ($direct as $d) {
            $sheet1->fromArray([
                (string)($d['nama'] ?? ''),
                (int) round((float)($d['harga'] ?? 0)),
            ], null, "A{$r}");
            $r++;
        }
        $sheet1->fromArray(['Total biaya langsung', (int) round((float)($ring['direct_per_layanan'] ?? 0))], null, "A{$r}");
        $r += 2;

        // Alokasi biaya operasional per layanan
        $sheet1->setCellValue("A{$r}", 'Alokasi biaya operasional per layanan');
        $r++;
        $sheet1->fromArray(['Nama biaya', 'Total/Bulan', 'Alokasi/Layanan'], null, "A{$r}");
        $r++;
        foreach ($opex as $o) {
            $sheet1->fromArray([
                (string)($o['nama'] ?? ''),
                (int) round((float)($o['total_bulan'] ?? 0)),
                (int) round((float)($o['alokasi_per_layanan'] ?? 0)),
            ], null, "A{$r}");
            $r++;
        }
        $sheet1->fromArray(['Total opex/bulan', (int) round((float)($ring['opex_bulan'] ?? 0)), ''], null, "A{$r}");
        $r += 2;

        // Ringkasan HPP & Harga
        $sheet1->fromArray(['Target layanan/bulan', (int) round((float)($ring['target_layanan'] ?? 0))], null, "A{$r}");
        $r++;
        $sheet1->fromArray(['HPP/Layanan', (int) round((float)($ring['hpp_per_layanan'] ?? 0))], null, "A{$r}");
        $r++;

        $hargaPilihan = (int) round((float)($price['harga_pilihan'] ?? 0));
        $profitUnit = (int) round((float)($price['profit_per_unit'] ?? 0));
        $marginPct = (float)($price['margin_pct'] ?? 0);
        $sheet1->fromArray(['Harga jual pilihan', $hargaPilihan], null, "A{$r}");
        $r++;
        $sheet1->fromArray(['Profit/Unit', $profitUnit], null, "A{$r}");
        $r++;
        $sheet1->fromArray(['Margin (%)', round($marginPct, 1)], null, "A{$r}");
        $r += 2;

        // Sheet 2: Saran Harga
        $sheet2 = $spreadsheet->createSheet();
        $sheet2->setTitle('Saran Harga AI');
        $sheet2->fromArray(['Level', 'Harga', 'Profit/Unit', 'Margin (%)', 'Catatan'], null, 'A1');
        $r2 = 2;
        foreach ($priceSugs as $s) {
            $sheet2->fromArray([
                (string)($s['level'] ?? ''),
                (int) round((float)($s['harga'] ?? 0)),
                (int) round((float)($s['profit_per_unit'] ?? 0)),
                (float)($s['margin_pct'] ?? 0),
                (string)($s['catatan'] ?? ''),
            ], null, "A{$r2}");
            $r2++;
        }

        // Sheet 3: Proyeksi (jika ada)
        if (!empty($proj)) {
            $sheet3 = $spreadsheet->createSheet();
            $sheet3->setTitle('Proyeksi');
            $sheet3->fromArray(['Target laba/bulan', (int) round((float)($proj['target_laba_bulan'] ?? 0))], null, 'A1');
            $sheet3->fromArray(['Target jual/hari', (int) round((float)($proj['qty_day'] ?? 0))], null, 'A2');
            $sheet3->fromArray(['Total jual/bulan', (int) round((float)($proj['qty_month'] ?? 0))], null, 'A3');
            $sheet3->fromArray(['Omset/bulan', (int) round((float)($proj['omset'] ?? 0))], null, 'A4');
            $sheet3->fromArray(['Total biaya produksi/bulan', (int) round((float)($proj['biaya_produksi'] ?? 0))], null, 'A5');
            $sheet3->fromArray(['Total biaya tetap/bulan', (int) round((float)($proj['fix_month'] ?? 0))], null, 'A6');
            $sheet3->fromArray(['Proyeksi laba bersih/bulan', (int) round((float)($proj['laba_bersih'] ?? 0))], null, 'A7');
        }

        $filename = 'HPP_Jasa_' . preg_replace('/[^A-Za-z0-9_\-]+/', '_', $nama) . '_' . date('Ymd') . '.xlsx';
        $response = new StreamedResponse(function () use ($spreadsheet) {
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
        });
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $filename . '"');
        $response->headers->set('Cache-Control', 'max-age=0');
        return $response;
    }

    public function exportTurunan(Request $request)
    {
        $validated = $request->validate([
            'produk_utama' => 'nullable|array',
            'produk_utama.nama' => 'nullable|string',

            'ringkasan' => 'nullable|array',
            'ringkasan.batch_per_bulan' => 'nullable|numeric',
            'ringkasan.biaya_bahan_bulan' => 'nullable|numeric',
            'ringkasan.biaya_olah_bulan' => 'nullable|numeric',
            'ringkasan.total_biaya_bulan' => 'nullable|numeric',
            'ringkasan.biaya_per_batch' => 'nullable|numeric',
            'ringkasan.revenue_per_batch' => 'nullable|numeric',
            'ringkasan.profit_per_batch' => 'nullable|numeric',
            'ringkasan.margin_pct' => 'nullable|numeric',

            'rincian_per_batch' => 'nullable|array',
            'rincian_per_batch.bahan' => 'nullable|array',
            'rincian_per_batch.bahan.*.nama' => 'nullable|string',
            'rincian_per_batch.bahan.*.biaya_per_batch' => 'nullable|numeric',
            'rincian_per_batch.bahan.*.jumlah' => 'nullable|numeric',
            'rincian_per_batch.bahan.*.satuan' => 'nullable|string',
            'rincian_per_batch.olah' => 'nullable|array',
            'rincian_per_batch.olah.*.nama' => 'nullable|string',
            'rincian_per_batch.olah.*.biaya_per_batch' => 'nullable|numeric',
            'rincian_per_batch.olah.*.periode' => 'nullable|string',

            'produk_turunan' => 'nullable|array',
            'produk_turunan.*.nama' => 'nullable|string',
            'produk_turunan.*.qty_batch' => 'nullable|numeric',
            'produk_turunan.*.satuan' => 'nullable|string',
            'produk_turunan.*.harga_jual' => 'nullable|numeric',
            'produk_turunan.*.hpp_unit_batch' => 'nullable|numeric',
            'produk_turunan.*.hpp_total_batch' => 'nullable|numeric',
            'produk_turunan.*.revenue_batch' => 'nullable|numeric',
            'produk_turunan.*.profit_batch' => 'nullable|numeric',
            'produk_turunan.*.margin_pct' => 'nullable|numeric',
        ]);

        $produkUtama = $validated['produk_utama']['nama'] ?? 'Produk Turunan';
        $ring = $validated['ringkasan'] ?? [];
        $rincian = $validated['rincian_per_batch'] ?? [];
        $produkList = $validated['produk_turunan'] ?? [];

        $spreadsheet = new Spreadsheet();
        $sheet1 = $spreadsheet->getActiveSheet();
        $sheet1->setTitle('Ringkasan HPP');

        $r = 1;
        $sheet1->fromArray(['Produk utama', $produkUtama], null, 'A' . $r);
        $r += 2;
        $sheet1->fromArray(['Batch/bulan', (int) round((float)($ring['batch_per_bulan'] ?? 0))], null, 'A' . $r);
        $r++;
        $sheet1->fromArray(['Biaya bahan/bln', (int) round((float)($ring['biaya_bahan_bulan'] ?? 0))], null, 'A' . $r);
        $r++;
        $sheet1->fromArray(['Biaya olah/bln', (int) round((float)($ring['biaya_olah_bulan'] ?? 0))], null, 'A' . $r);
        $r++;
        $sheet1->fromArray(['Total biaya/bln', (int) round((float)($ring['total_biaya_bulan'] ?? 0))], null, 'A' . $r);
        $r += 2;
        $sheet1->fromArray(['Biaya/batch', (int) round((float)($ring['biaya_per_batch'] ?? 0))], null, 'A' . $r);
        $r++;
        $sheet1->fromArray(['Revenue/batch', (int) round((float)($ring['revenue_per_batch'] ?? 0))], null, 'A' . $r);
        $r++;
        $sheet1->fromArray(['Profit/batch', (int) round((float)($ring['profit_per_batch'] ?? 0))], null, 'A' . $r);
        $r++;
        $sheet1->fromArray(['Margin (%)', (float)($ring['margin_pct'] ?? 0)], null, 'A' . $r);

        // Sheet 2: Rincian per batch
        $sheet2 = $spreadsheet->createSheet();
        $sheet2->setTitle('Rincian per Batch');
        $r2 = 1;
        $sheet2->setCellValue('A' . $r2, 'Bahan');
        $r2++;
        $sheet2->fromArray(['Nama', 'Biaya/Batch', 'Jumlah', 'Satuan'], null, 'A' . $r2);
        $r2++;
        foreach (($rincian['bahan'] ?? []) as $b) {
            $sheet2->fromArray([
                (string)($b['nama'] ?? ''),
                (int) round((float)($b['biaya_per_batch'] ?? 0)),
                (float)($b['jumlah'] ?? 0),
                (string)($b['satuan'] ?? ''),
            ], null, 'A' . $r2);
            $r2++;
        }
        $r2 += 1;
        $sheet2->setCellValue('A' . $r2, 'Pengolahan');
        $r2++;
        $sheet2->fromArray(['Nama', 'Biaya/Batch', 'Periode'], null, 'A' . $r2);
        $r2++;
        foreach (($rincian['olah'] ?? []) as $o) {
            $sheet2->fromArray([
                (string)($o['nama'] ?? ''),
                (int) round((float)($o['biaya_per_batch'] ?? 0)),
                (string)($o['periode'] ?? ''),
            ], null, 'A' . $r2);
            $r2++;
        }

        // Sheet 3: Produk Turunan
        $sheet3 = $spreadsheet->createSheet();
        $sheet3->setTitle('Produk Turunan');
        $sheet3->fromArray(['Nama', 'Qty/Batch', 'Satuan', 'Harga/Unit', 'HPP/Unit', 'HPP Total/Batch', 'Revenue/Batch', 'Profit/Batch', 'Margin (%)'], null, 'A1');
        $r3 = 2;
        foreach ($produkList as $p) {
            $sheet3->fromArray([
                (string)($p['nama'] ?? ''),
                (float)($p['qty_batch'] ?? 0),
                (string)($p['satuan'] ?? ''),
                (int) round((float)($p['harga_jual'] ?? 0)),
                (int) round((float)($p['hpp_unit_batch'] ?? 0)),
                (int) round((float)($p['hpp_total_batch'] ?? 0)),
                (int) round((float)($p['revenue_batch'] ?? 0)),
                (int) round((float)($p['profit_batch'] ?? 0)),
                (float)($p['margin_pct'] ?? 0),
            ], null, 'A' . $r3);
            $r3++;
        }

        $filename = 'HPP_Turunan_' . preg_replace('/[^A-Za-z0-9_\-]+/', '_', $produkUtama) . '_' . date('Ymd') . '.xlsx';
        $response = new StreamedResponse(function () use ($spreadsheet) {
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
        });
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $filename . '"');
        $response->headers->set('Cache-Control', 'max-age=0');
        return $response;
    }
}
