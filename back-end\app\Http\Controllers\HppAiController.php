<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class HppAiController extends Controller
{
    public function adsSuggestions(Request $request)
    {
        $validated = $request->validate([
            'items' => 'required|array|min:1',
            'items.*.nama' => 'nullable|string',
            'items.*.hpp' => 'nullable|numeric',
            'items.*.harga' => 'nullable|numeric',
            'items.*.qty' => 'nullable|numeric',
            'biayaIklan' => 'nullable|numeric',
            'adminPct' => 'nullable|numeric',
            'returnPct' => 'nullable|numeric',
        ]);

        $apiKey = env('OPENAI_API_KEY');
        if (!$apiKey) {
            return response()->json(['error' => 'OPENAI_API_KEY is not set in environment.'], 500);
        }

        // Hitung ringkasan agar AI punya konteks angka yang rapi
        $items = array_map(function ($p) {
            return [
                'nama' => (string)($p['nama'] ?? ''),
                'hpp' => (float)($p['hpp'] ?? 0),
                'harga' => (float)($p['harga'] ?? 0),
                'qty' => (float)($p['qty'] ?? 0),
            ];
        }, $validated['items']);

        $totalQty = 0;
        $totalRevenue = 0;
        $totalHpp = 0;
        foreach ($items as $i) {
            $totalQty += $i['qty'];
            $totalRevenue += $i['harga'] * $i['qty'];
            $totalHpp += $i['hpp'] * $i['qty'];
        }
        $biayaIklan = (float)($validated['biayaIklan'] ?? 0);
        $admin = ((float)($validated['adminPct'] ?? 0) / 100.0) * $totalRevenue;
        $retur = ((float)($validated['returnPct'] ?? 0) / 100.0) * $totalRevenue;
        $profit = $totalRevenue - $totalHpp - $biayaIklan - $admin - $retur;
        $margin = $totalRevenue > 0 ? ($profit / $totalRevenue) * 100.0 : 0.0;
        $roas = $biayaIklan > 0 ? ($totalRevenue / $biayaIklan) : null;
        $cpr = $totalQty > 0 ? ($biayaIklan / $totalQty) : null;
        $labaPerUnit = $totalQty > 0 ? ($profit / $totalQty) : 0.0;

        $payload = [
            'ringkasan' => [
                'total_qty' => $totalQty,
                'total_revenue' => $totalRevenue,
                'total_hpp' => $totalHpp,
                'biaya_iklan' => $biayaIklan,
                'admin' => $admin,
                'retur' => $retur,
                'profit' => $profit,
                'margin_pct' => $margin,
                'roas' => $roas,
                'cpr' => $cpr,
                'laba_per_unit' => $labaPerUnit,
            ],
            'produk' => $items,
        ];

        $system = <<<SYS
Anda adalah asisten analis pemasaran kinerja untuk iklan direct-response (Iklan & COD).
Beri saran evaluasi iklan berbasis data input pengguna, dalam bahasa Indonesia, singkat, actionable.
Balas HANYA dalam JSON object berikut tanpa teks lain:
{ "saran": string[] }
Ketentuan:
- 4–7 butir saran, spesifik dan dapat ditindaklanjuti.
- Pertimbangkan metrik: ROAS, CPR, margin, laba per unit, retur, dan komposisi produk.
- Jangan menulis angka jika tidak ada di data; gunakan istilah kualitatif seperlunya.
- Bila performa baik, rekomendasikan strategi scale bertahap dan eksperimen kreatif/penargetan.
- Bila performa buruk, prioritaskan tindakan berpengaruh: optimasi kreatif, funnel, harga, dan biaya.
SYS;

        try {
            $resp = Http::withToken($apiKey)
                ->timeout(40)
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => env('OPENAI_ADS_SUGGEST_MODEL', 'gpt-4o-mini'),
                    'messages' => [
                        ['role' => 'system', 'content' => $system],
                        ['role' => 'user', 'content' => json_encode($payload, JSON_UNESCAPED_UNICODE)],
                    ],
                    'temperature' => 0.3,
                    'max_tokens' => 600,
                    'response_format' => ['type' => 'json_object'],
                ]);

            if (!$resp->ok()) {
                return response()->json([
                    'error' => 'OpenAI request failed',
                    'details' => $resp->json(),
                ], 502);
            }

            $content = data_get($resp->json(), 'choices.0.message.content');
            if (!$content) {
                return response()->json(['error' => 'Empty content from OpenAI'], 502);
            }
            $json = json_decode($content, true);
            if (!is_array($json) || !isset($json['saran']) || !is_array($json['saran'])) {
                return response()->json(['error' => 'Invalid JSON returned by OpenAI', 'raw' => $content], 502);
            }

            // Pastikan string & batas jumlah
            $saran = array_values(array_filter(array_map(fn($s) => trim((string)$s), $json['saran'])));
            $saran = array_slice($saran, 0, 7);

            return response()->json(['saran' => $saran, 'meta' => $payload['ringkasan']]);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Exception while calling OpenAI',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function quickEstimate(Request $request)
    {
        $validated = $request->validate([
            'nama' => 'nullable|string',
            'harga' => 'required|numeric|min:0',
            'kategori' => 'nullable|string',
        ]);

        $apiKey = env('OPENAI_API_KEY');
        if (!$apiKey) {
            return response()->json(['error' => 'OPENAI_API_KEY is not set in environment.'], 500);
        }

        $nama = (string)($validated['nama'] ?? 'Produk');
        $harga = (float)$validated['harga'];
        $kategori = (string)($validated['kategori'] ?? 'umum');

        $userPayload = [
            'produk' => ['nama' => $nama, 'kategori' => $kategori],
            'harga_jual' => $harga,
            'instruksi' => 'Rinci 6-12 komponen biaya realistis (bahan, bumbu, kemasan, label, utilitas, tenaga kerja, overhead kecil) yang membentuk HPP.',
        ];

        $system = <<<SYS
Anda adalah asisten costing HPP. Keluarkan rincian komponen biaya HPP realistis berdasarkan nama produk dan harga jual.
Balas HANYA JSON object berikut:
{ "estimasi_hpp": number, "komponen_biaya": {"label": string, "nilai": number}[], "saran_ai": string[] }
Ketentuan:
- 6-12 item komponen_biaya; gunakan istilah umum & mudah dimengerti.
- nilai dalam Rupiah per pcs (bilangan bulat, tanpa satuan teks).
- estimasi_hpp ≈ jumlah komponen (Anda boleh menyesuaikan sedikit agar realistis, namun jangan melebih harga jual bila tidak masuk akal).
- saran_ai 3-6 butir, singkat & actionable (efisiensi bahan/kemasan, skala produksi, negosiasi supplier, dsb.).
SYS;

        try {
            $resp = Http::withToken($apiKey)
                ->timeout(40)
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => env('OPENAI_QUICK_MODEL', 'gpt-4o-mini'),
                    'messages' => [
                        ['role' => 'system', 'content' => $system],
                        ['role' => 'user', 'content' => json_encode($userPayload, JSON_UNESCAPED_UNICODE)],
                    ],
                    'temperature' => 0.3,
                    'max_tokens' => 700,
                    'response_format' => ['type' => 'json_object'],
                ]);

            if (!$resp->ok()) {
                return response()->json([
                    'error' => 'OpenAI request failed',
                    'details' => $resp->json(),
                ], 502);
            }

            $content = data_get($resp->json(), 'choices.0.message.content');
            if (!$content) {
                return response()->json(['error' => 'Empty content from OpenAI'], 502);
            }
            $json = json_decode($content, true);
            if (!is_array($json)) {
                return response()->json(['error' => 'Invalid JSON returned by OpenAI', 'raw' => $content], 502);
            }

            $komponen = array_map(function ($k) {
                return [
                    'label' => (string) data_get($k, 'label', ''),
                    'nilai' => (int) round((float) data_get($k, 'nilai', 0)),
                ];
            }, (array) data_get($json, 'komponen_biaya', []));
            if (empty($komponen)) {
                return response()->json(['error' => 'AI did not return komponen_biaya'], 502);
            }
            $sum = array_reduce($komponen, fn($s, $i) => $s + (int)$i['nilai'], 0);
            $estimasi = (int) round((float) ($json['estimasi_hpp'] ?? $sum));
            if ($estimasi <= 0) {
                $estimasi = $sum > 0 ? $sum : (int) round($harga * 0.6);
            }

            $potensi = max(0, (int) round($harga - $estimasi));
            $margin = $harga > 0 ? (($potensi / $harga) * 100.0) : 0.0;

            $saran = array_values(array_filter(array_map(fn($s) => trim((string)$s), (array) data_get($json, 'saran_ai', []))));
            $saran = array_slice($saran, 0, 6);

            return response()->json([
                'estimasi_hpp' => $estimasi,
                'potensi_laba' => $potensi,
                'margin_pct' => round($margin, 1),
                'komponen_biaya' => $komponen,
                'saran_ai' => $saran,
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Exception while calling OpenAI',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
    public function ritelEstimate(Request $request)
    {
        $validated = $request->validate([
            'nama' => 'nullable|string',
            'kategori' => 'nullable|string',
        ]);

        $apiKey = env('OPENAI_API_KEY');
        if (!$apiKey) {
            return response()->json(['error' => 'OPENAI_API_KEY is not set in environment.'], 500);
        }

        $nama = (string)($validated['nama'] ?? 'Produk F&B');
        $kategori = (string)($validated['kategori'] ?? 'fnb');

        $userPayload = [
            'produk' => ['nama' => $nama, 'kategori' => $kategori],
            'instruksi' => 'Rinci biaya variabel (bahan, bumbu, kemasan, dll.) dan biaya tetap bulanan (sewa, utilitas, gaji, dll.) untuk bisnis ritel/F&B skala kecil. Gunakan angka realistis.',
        ];

        $system = <<<SYS
Anda adalah asisten costing HPP untuk bisnis Ritel/F&B.
Balas HANYA JSON object berikut tanpa teks lain:
{
  "var_costs": [
    {"bahan": string, "pakai_qty": number, "pakai_unit": "g"|"kg"|"ml"|"l"|"pcs"|"buah"|"lembar", "total_harga": number, "beli_qty": number, "beli_unit": "g"|"kg"|"ml"|"l"|"pcs"|"buah"|"lembar"}
  ],
  "fix_costs": [
    {"nama": string, "total_bulan": number, "alokasi": number}
  ]
}
Ketentuan:
- 5–10 item var_costs; "total_harga" adalah harga total saat pembelian (beli_qty + beli_unit), bukan per pakai.
- 3–8 item fix_costs. Isi "alokasi" (perkiraan) per produk jika memungkinkan; angka bulat dalam Rupiah, tanpa format pemisah.
- Gunakan unit yang masuk akal untuk F&B (g/kg/ml/l/pcs/buah/lembar).
SYS;

        try {
            $resp = Http::withToken($apiKey)
                ->timeout(40)
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => env('OPENAI_RITEL_MODEL', 'gpt-4o-mini'),
                    'messages' => [
                        ['role' => 'system', 'content' => $system],
                        ['role' => 'user', 'content' => json_encode($userPayload, JSON_UNESCAPED_UNICODE)],
                    ],
                    'temperature' => 0.3,
                    'max_tokens' => 900,
                    'response_format' => ['type' => 'json_object'],
                ]);

            if (!$resp->ok()) {
                return response()->json([
                    'error' => 'OpenAI request failed',
                    'details' => $resp->json(),
                ], 502);
            }

            $content = data_get($resp->json(), 'choices.0.message.content');
            if (!$content) {
                return response()->json(['error' => 'Empty content from OpenAI'], 502);
            }
            $json = json_decode($content, true);
            if (!is_array($json)) {
                return response()->json(['error' => 'Invalid JSON returned by OpenAI', 'raw' => $content], 502);
            }

            $var = array_map(function ($v) {
                return [
                    'bahan' => (string) data_get($v, 'bahan', ''),
                    'pakai_qty' => (float) data_get($v, 'pakai_qty', 0),
                    'pakai_unit' => (string) data_get($v, 'pakai_unit', 'g'),
                    'total_harga' => (float) data_get($v, 'total_harga', 0),
                    'beli_qty' => (float) data_get($v, 'beli_qty', 0),
                    'beli_unit' => (string) data_get($v, 'beli_unit', 'g'),
                ];
            }, (array) data_get($json, 'var_costs', []));

            $fix = array_map(function ($f) {
                return [
                    'nama' => (string) data_get($f, 'nama', ''),
                    'total_bulan' => (float) data_get($f, 'total_bulan', 0),
                    'alokasi' => (float) data_get($f, 'alokasi', 0),
                ];
            }, (array) data_get($json, 'fix_costs', []));

            if (empty($var) && empty($fix)) {
                return response()->json(['error' => 'AI did not return any costs', 'raw' => $json], 502);
            }

            return response()->json(['var_costs' => $var, 'fix_costs' => $fix]);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Exception while calling OpenAI',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
    public function priceSuggest(Request $request)
    {
        $validated = $request->validate([
            'nama' => 'nullable|string',
            'kategori' => 'nullable|string',
            'hpp' => 'required|numeric|min:0',
        ]);

        $apiKey = env('OPENAI_API_KEY');
        if (!$apiKey) {
            return response()->json(['error' => 'OPENAI_API_KEY is not set in environment.'], 500);
        }

        $nama = (string)($validated['nama'] ?? 'Produk');
        $kategori = (string)($validated['kategori'] ?? 'umum');
        $hpp = (float)$validated['hpp'];

        $payload = [
            'produk' => ['nama' => $nama, 'kategori' => $kategori],
            'hpp_per_unit' => $hpp,
            'instruksi' => 'Buat 3 rekomendasi harga (kompetitif, standar, premium) dengan rasional singkat.',
        ];

        $system = <<<SYS
Anda adalah asisten pricing. Balas HANYA JSON object berikut tanpa teks lain:
{ "saran": {"level": "kompetitif"|"standar"|"premium", "harga": number, "margin_pct": number, "profit_per_unit": number, "catatan": string }[] }
Ketentuan:
- Gunakan 3 level: kompetitif, standar, premium.
- margin_pct = (harga - HPP)/harga*100.
- profit_per_unit = harga - HPP.
- Harga dalam rupiah (bilangan bulat), rasional ringkas dan actionable.
- JANGAN memberi harga jauh di atas HPP: patokan kisaran wajar dari HPP adalah:
  * kompetitif ≈ HPP x 1.1–1.3 (margin ±10–30%)
  * standar ≈ HPP x 1.3–1.6 (margin ±30–60%)
  * premium ≈ HPP x 1.6–2.2 (margin ±60–120%)
- Jika kategori/produk membuat harga di luar kisaran wajar tersebut, berikan alasan kuat di catatan.
SYS;

        try {
            $resp = Http::withToken($apiKey)
                ->timeout(40)
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => env('OPENAI_PRICE_MODEL', 'gpt-4o-mini'),
                    'messages' => [
                        ['role' => 'system', 'content' => $system],
                        ['role' => 'user', 'content' => json_encode($payload, JSON_UNESCAPED_UNICODE)],
                    ],
                    'temperature' => 0.3,
                    'max_tokens' => 500,
                    'response_format' => ['type' => 'json_object'],
                ]);

            if (!$resp->ok()) {
                return response()->json([
                    'error' => 'OpenAI request failed',
                    'details' => $resp->json(),
                ], 502);
            }

            $content = data_get($resp->json(), 'choices.0.message.content');
            if (!$content) return response()->json(['error' => 'Empty content from OpenAI'], 502);
            $json = json_decode($content, true);
            if (!is_array($json) || !isset($json['saran']) || !is_array($json['saran'])) {
                return response()->json(['error' => 'Invalid JSON returned by OpenAI', 'raw' => $content], 502);
            }

            $saran = array_map(function ($x) use ($hpp) {
                $harga = (int) round((float) data_get($x, 'harga', 0));
                $profit = $harga - (int) round($hpp);
                $margin = $harga > 0 ? ($profit / $harga) * 100.0 : 0.0;
                return [
                    'level' => (string) data_get($x, 'level', ''),
                    'harga' => $harga,
                    'profit_per_unit' => (int) round((float) data_get($x, 'profit_per_unit', $profit)),
                    'margin_pct' => round((float) data_get($x, 'margin_pct', $margin), 1),
                    'catatan' => (string) data_get($x, 'catatan', ''),
                ];
            }, (array) $json['saran']);

            return response()->json(['saran' => $saran]);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Exception while calling OpenAI',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function manufakturEstimate(Request $request)
    {
        $validated = $request->validate([
            'nama' => 'nullable|string',
            'kategori' => 'nullable|string',
        ]);

        $apiKey = env('OPENAI_API_KEY');
        if (!$apiKey) {
            return response()->json(['error' => 'OPENAI_API_KEY is not set in environment.'], 500);
        }

        $nama = (string)($validated['nama'] ?? 'Produk manufaktur');
        $kategori = (string)($validated['kategori'] ?? 'umum');

        $userPayload = [
            'produk' => ['nama' => $nama, 'kategori' => $kategori],
            'instruksi' => 'Rinci biaya bulanan untuk manufaktur: bahan baku langsung (nama + total per bulan), tenaga kerja langsung (jenis + total upah per bulan), overhead pabrik (nama + total per bulan). Gunakan angka realistis untuk skala kecil-menengah.',
        ];

        $system = <<<SYS
Anda adalah asisten costing HPP manufaktur/pabrik. Balas HANYA JSON object berikut tanpa teks lain:
{
  "bahan": [{"nama": string, "total_bulan": number}],
  "tkl":   [{"jenis": string, "total_upah": number}],
  "ohp":   [{"nama": string, "total_bulan": number}]
}
Ketentuan:
- 5–10 item bahan; 2–6 item tkl; 3–8 item ohp.
- Semua angka bilangan bulat rupiah per bulan (tanpa format pemisah).
- Gunakan istilah umum (contoh bahan: bahan utama, bahan pendukung; tkl: operator, QC; ohp: listrik pabrik, perawatan mesin, sewa, utilitas).
SYS;

        try {
            $resp = Http::withToken($apiKey)
                ->timeout(40)
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => env('OPENAI_MANUFAKTUR_MODEL', 'gpt-4o-mini'),
                    'messages' => [
                        ['role' => 'system', 'content' => $system],
                        ['role' => 'user', 'content' => json_encode($userPayload, JSON_UNESCAPED_UNICODE)],
                    ],
                    'temperature' => 0.3,
                    'max_tokens' => 900,
                    'response_format' => ['type' => 'json_object'],
                ]);

            if (!$resp->ok()) {
                return response()->json([
                    'error' => 'OpenAI request failed',
                    'details' => $resp->json(),
                ], 502);
            }

            $content = data_get($resp->json(), 'choices.0.message.content');
            if (!$content) return response()->json(['error' => 'Empty content from OpenAI'], 502);
            $json = json_decode($content, true);
            if (!is_array($json)) return response()->json(['error' => 'Invalid JSON returned by OpenAI', 'raw' => $content], 502);

            $bahan = array_map(function ($v) {
                return [
                    'nama' => (string) data_get($v, 'nama', ''),
                    'total_bulan' => (int) round((float) data_get($v, 'total_bulan', 0)),
                ];
            }, (array) data_get($json, 'bahan', []));

            $tkl = array_map(function ($v) {
                return [
                    'jenis' => (string) data_get($v, 'jenis', ''),
                    'total_upah' => (int) round((float) data_get($v, 'total_upah', 0)),
                ];
            }, (array) data_get($json, 'tkl', []));

            $ohp = array_map(function ($v) {
                return [
                    'nama' => (string) data_get($v, 'nama', ''),
                    'total_bulan' => (int) round((float) data_get($v, 'total_bulan', 0)),
                ];
            }, (array) data_get($json, 'ohp', []));

            if (empty($bahan) && empty($tkl) && empty($ohp)) {
                return response()->json(['error' => 'AI did not return any costs', 'raw' => $json], 502);
            }

            return response()->json(['bahan' => $bahan, 'tkl' => $tkl, 'ohp' => $ohp]);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Exception while calling OpenAI',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function jasaEstimate(Request $request)
    {
        $validated = $request->validate([
            'nama' => 'nullable|string',
            'kategori' => 'nullable|string',
        ]);

        $apiKey = env('OPENAI_API_KEY');
        if (!$apiKey) {
            return response()->json(['error' => 'OPENAI_API_KEY is not set in environment.'], 500);
        }

        $nama = (string)($validated['nama'] ?? 'Layanan');
        $kategori = (string)($validated['kategori'] ?? 'umum');

        $userPayload = [
            'layanan' => ['nama' => $nama, 'kategori' => $kategori],
            'instruksi' => 'Rinci biaya langsung per layanan dan biaya operasional bulanan untuk bisnis jasa skala kecil-menengah.',
        ];

        $system = <<<SYS
Anda adalah asisten costing HPP untuk produk jasa.
Balas HANYA JSON object berikut tanpa teks lain:
{
  "direct": [{"nama": string, "harga": number}],
  "opex":   [{"nama": string, "biaya": number, "periode": "per-bulan"|"per-minggu"|"per-hari"}]
}
Ketentuan:
- 2–6 item direct (biaya langsung per layanan, contoh: material pendukung, subkontraktor, lisensi per job).
- 3–8 item opex (biaya operasional bulanan/mingguan/harian: sewa, utilitas, software, gaji admin dsb.).
- Semua angka bilangan bulat rupiah, tanpa format pemisah.
SYS;

        try {
            $resp = Http::withToken($apiKey)
                ->timeout(40)
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => env('OPENAI_JASA_MODEL', 'gpt-4o-mini'),
                    'messages' => [
                        ['role' => 'system', 'content' => $system],
                        ['role' => 'user', 'content' => json_encode($userPayload, JSON_UNESCAPED_UNICODE)],
                    ],
                    'temperature' => 0.3,
                    'max_tokens' => 900,
                    'response_format' => ['type' => 'json_object'],
                ]);

            if (!$resp->ok()) {
                return response()->json([
                    'error' => 'OpenAI request failed',
                    'details' => $resp->json(),
                ], 502);
            }

            $content = data_get($resp->json(), 'choices.0.message.content');
            if (!$content) return response()->json(['error' => 'Empty content from OpenAI'], 502);
            $json = json_decode($content, true);
            if (!is_array($json)) return response()->json(['error' => 'Invalid JSON returned by OpenAI', 'raw' => $content], 502);

            $direct = array_map(function ($v) {
                return [
                    'nama' => (string) data_get($v, 'nama', ''),
                    'harga' => (int) round((float) data_get($v, 'harga', 0)),
                ];
            }, (array) data_get($json, 'direct', []));

            $opex = array_map(function ($v) {
                $periode = (string) data_get($v, 'periode', 'per-bulan');
                if (!in_array($periode, ['per-bulan', 'per-minggu', 'per-hari'], true)) $periode = 'per-bulan';
                return [
                    'nama' => (string) data_get($v, 'nama', ''),
                    'biaya' => (int) round((float) data_get($v, 'biaya', 0)),
                    'periode' => $periode,
                ];
            }, (array) data_get($json, 'opex', []));

            if (empty($direct) && empty($opex)) {
                return response()->json(['error' => 'AI did not return any costs', 'raw' => $json], 502);
            }

            return response()->json(['direct' => $direct, 'opex' => $opex]);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Exception while calling OpenAI',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function turunanEstimate(Request $request)
    {
        $validated = $request->validate([
            'nama' => 'nullable|string',
            'batch' => 'nullable|numeric',
        ]);

        $apiKey = env('OPENAI_API_KEY');
        if (!$apiKey) {
            return response()->json(['error' => 'OPENAI_API_KEY is not set in environment.'], 500);
        }

        $nama = (string)($validated['nama'] ?? 'Produk turunan');
        $batch = (int)($validated['batch'] ?? 1);

        $payload = [
            'produk_utama' => $nama,
            'batch_per_bulan' => $batch,
            'instruksi' => 'Keluarkan estimasi satu bahan baku utama (nama, biaya total per bulan, jumlah & satuan), beberapa biaya pengolahan (nama, harga, periode), dan 2–5 produk turunan (nama, qty per bulan, satuan, harga jual per unit).'
        ];

        $system = <<<SYS
Anda adalah asisten costing untuk model Produksi Turunan.
Balas HANYA JSON object berikut tanpa teks lain:
{
  "bahan": [{"nama": string, "biaya": number, "jumlah": number, "satuan": string}],
  "olah":  [{"nama": string, "harga": number, "periode": "per-batch"|"per-bulan"|"per-minggu"|"per-hari"}],
  "produk": [{"nama": string, "qty": number, "satuan": "pcs"|"kg", "hargaJual": number}]
}
Ketentuan:
- Isi tepat 1 item pada array "bahan" (bahan baku utama) dengan angka rupiah bulat.
- 3–8 item pada "olah". Harga adalah rupiah bulat. Periode salah satu dari: per-batch, per-bulan, per-minggu, per-hari.
- 2–5 item pada "produk". Gunakan satuan "pcs" untuk barang satuan, atau "kg" bila relevan.
- Angka tanpa pemisah ribuan.
SYS;

        try {
            $resp = Http::withToken($apiKey)
                ->timeout(40)
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => env('OPENAI_TURUNAN_MODEL', 'gpt-4o-mini'),
                    'messages' => [
                        ['role' => 'system', 'content' => $system],
                        ['role' => 'user', 'content' => json_encode($payload, JSON_UNESCAPED_UNICODE)],
                    ],
                    'temperature' => 0.3,
                    'max_tokens' => 900,
                    'response_format' => ['type' => 'json_object'],
                ]);

            if (!$resp->ok()) {
                return response()->json([
                    'error' => 'OpenAI request failed',
                    'details' => $resp->json(),
                ], 502);
            }

            $content = data_get($resp->json(), 'choices.0.message.content');
            if (!$content) return response()->json(['error' => 'Empty content from OpenAI'], 502);
            $json = json_decode($content, true);
            if (!is_array($json)) return response()->json(['error' => 'Invalid JSON returned by OpenAI', 'raw' => $content], 502);

            $bahan = array_values(array_map(function ($v) {
                return [
                    'nama' => (string) data_get($v, 'nama', ''),
                    'biaya' => (int) round((float) data_get($v, 'biaya', 0)),
                    'jumlah' => (float) data_get($v, 'jumlah', 0),
                    'satuan' => (string) data_get($v, 'satuan', ''),
                ];
            }, (array) data_get($json, 'bahan', [])));

            $olah = array_map(function ($v) {
                $periode = (string) data_get($v, 'periode', 'per-bulan');
                if (!in_array($periode, ['per-batch', 'per-bulan', 'per-minggu', 'per-hari'], true)) $periode = 'per-bulan';
                return [
                    'nama' => (string) data_get($v, 'nama', ''),
                    'harga' => (int) round((float) data_get($v, 'harga', 0)),
                    'periode' => $periode,
                ];
            }, (array) data_get($json, 'olah', []));

            $produk = array_map(function ($v) {
                $satuan = (string) data_get($v, 'satuan', 'pcs');
                if (!in_array($satuan, ['pcs', 'kg'], true)) $satuan = 'pcs';
                return [
                    'nama' => (string) data_get($v, 'nama', ''),
                    'qty' => (float) data_get($v, 'qty', 0),
                    'satuan' => $satuan,
                    'hargaJual' => (int) round((float) data_get($v, 'hargaJual', 0)),
                ];
            }, (array) data_get($json, 'produk', []));

            if (empty($bahan) && empty($olah) && empty($produk)) {
                return response()->json(['error' => 'AI did not return any costs', 'raw' => $json], 502);
            }

            return response()->json(['bahan' => $bahan, 'olah' => $olah, 'produk' => $produk]);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Exception while calling OpenAI',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
