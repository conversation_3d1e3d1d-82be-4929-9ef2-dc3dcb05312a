import React, { useMemo, useRef, useState } from 'react';
import { FileText, Palette, Type, Settings, Images, Camera, Wand2, Layers } from 'lucide-react';



const API_BASE = (import.meta as any).env?.VITE_BACKEND_URL || 'http://localhost:8000';

const AdsImageGeneratorPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'ads-image-gen' | 'photo-enhancer' | 'image-blender'>('ads-image-gen');

  // Form state (Ads Image Gen)
  const [desc, setDesc] = useState('');
  const [selectedStyle, setSelectedStyle] = useState<string>('gaya-hidup');
  const [customPreview, setCustomPreview] = useState<string | null>(null);
  const [headline, setHeadline] = useState('');
  const [subHeadline, setSubHeadline] = useState('');
  const [cta, setCta] = useState('');
  const [urgency, setUrgency] = useState('');
  const [aspect, setAspect] = useState<'1:1' | '4:5' | '3:4' | '16:9' | '9:16'>('1:1');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [count, setCount] = useState<1 | 2 | 3 | 4>(2);
  const [generated, setGenerated] = useState<string[]>([]);
  // Photo Enhancer state
  const [peFile, setPeFile] = useState<File | null>(null);
  const [pePreview, setPePreview] = useState<string | null>(null);
  const peFileInputRef = useRef<HTMLInputElement>(null);
  const [peDragOver, setPeDragOver] = useState(false);

  const [peHeadline, setPeHeadline] = useState('');
  const [peSubHeadline, setPeSubHeadline] = useState('');
  const [peInstruksi, setPeInstruksi] = useState('');
  const [peHD, setPeHD] = useState(true);
  const [peCount, setPeCount] = useState<1 | 2 | 3 | 4>(2);
  const [peAspect, setPeAspect] = useState<'1:1' | '4:5' | '3:4' | '16:9' | '9:16'>('1:1');
  const [peTema, setPeTema] = useState<string>('studio-minimalis');
  const [peDetailing, setPeDetailing] = useState<string[]>([]);
  const [peSudut, setPeSudut] = useState<string>('close-up');
  const [peGenerated, setPeGenerated] = useState<string[]>([]);
  const [peLoading, setPeLoading] = useState(false);
  const [peError, setPeError] = useState<string | null>(null);

  const peTemaItems = useMemo(() => ([
    { id: 'studio-minimalis', name: 'Studio Minimalis', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-enhanced-1758265043937.png' },
    { id: 'putih-gradien', name: 'Latar Putih Gradien', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-enhanced-1758265062722.png' },
    { id: 'abu-netral', name: 'Latar Abu-abu Netral', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-enhanced-1758265082630.png' },
    { id: 'marmer-mewah', name: 'Meja Marmer Mewah', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-enhanced-1758265131608.png' },
    { id: 'gelap-dramatis', name: 'Latar Gelap Dramatis', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-enhanced-1758265151598.png' },
    { id: 'podium', name: 'Tampilan Podium', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-enhanced-1758265170723.png' },
    { id: 'iklan-pro', name: 'Foto Iklan Profesional', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-enhanced-1758266513896.png' },
    { id: 'kayu-rustic', name: 'Kayu Rustic Hangat', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-enhanced-1758265363892.png' },
    { id: 'alam-segar', name: 'Alam Terbuka Segar', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-enhanced-1758265389410.png' },
    { id: 'flat-lay', name: 'Gaya Flat Lay', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-enhanced-1758265409809.png' },
  ]), []);


  const onPeFileChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const f = e.target.files?.[0];
    if (!f) return;
    setPeFile(f);
    setPePreview(URL.createObjectURL(f));
  };

  const toggleDetailing = (id: string) => {
    setPeDetailing((prev) => prev.includes(id) ? prev.filter((x) => x !== id) : [...prev, id]);
  };

  const handlePeGenerate = async () => {
    try {
      if (!peFile) throw new Error('Silakan unggah gambar produk lebih dulu');
      setPeError(null);
      setPeLoading(true);
      setPeGenerated([]);
      const tema = peTemaItems.find(t => t.id === peTema);
      const fd = new FormData();
      fd.append('image', peFile);
      if (peHeadline) fd.append('headline', peHeadline);
      if (peSubHeadline) fd.append('subHeadline', peSubHeadline);
      if (peInstruksi) fd.append('instruksi', peInstruksi);
      if (tema?.name) fd.append('temaName', tema.name);
      if (tema?.img) fd.append('temaImageUrl', tema.img);
      peDetailing.forEach(d => fd.append('detailing[]', d));
      if (peSudut) fd.append('sudut', peSudut);
      fd.append('aspect', peAspect);
      fd.append('count', String(peCount));
      fd.append('hd', peHD ? '1' : '0');

      const res = await fetch(`${API_BASE}/api/photo-enhancer/generate`, {
        method: 'POST',
        body: fd,
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal membuat gambar');
      setPeGenerated(Array.isArray(data?.images) ? data.images : []);
    } catch (e: any) {
      setPeError(e?.message || 'Terjadi kesalahan');
    } finally {
      setPeLoading(false);
    }
  };

  // Image Blender state
  const ibModelInputRef = useRef<HTMLInputElement>(null);
  const ibProductInputRef = useRef<HTMLInputElement>(null);
  const [ibDragModel, setIbDragModel] = useState(false);
  const [ibDragProduct, setIbDragProduct] = useState(false);
  const [ibModelFile, setIbModelFile] = useState<File | null>(null);
  const [ibProductFile, setIbProductFile] = useState<File | null>(null);
  const [ibModelPreview, setIbModelPreview] = useState<string | null>(null);
  const [ibProductPreview, setIbProductPreview] = useState<string | null>(null);
  const [ibIntegrasi, setIbIntegrasi] = useState<string>('Produk pada Kaos');
  const [ibGayaPose, setIbGayaPose] = useState<string[]>([]);
  const [ibInteraksi, setIbInteraksi] = useState<string[]>([]);
  const [ibCamera, setIbCamera] = useState<string>('Close-up');
  const [ibBackground, setIbBackground] = useState<string>('Studio Profesional');
  const [ibArt, setIbArt] = useState<string>('Pencahayaan Dramatis');
  const [ibManual, setIbManual] = useState('');
  const [ibAspect, setIbAspect] = useState<'1:1' | '4:5' | '3:4' | '16:9' | '9:16'>('1:1');
  const [ibCount, setIbCount] = useState<1 | 2 | 3 | 4>(2);
  const [ibGenerated, setIbGenerated] = useState<string[]>([]);
  const [ibLoading, setIbLoading] = useState(false);
  const [ibError, setIbError] = useState<string | null>(null);


  const ibIntegrasiOptions = useMemo(() => [
    'Produk pada Kaos', 'Produk di Tangan', 'Presentasi Produk ke Kamera', 'Motif pada Pakaian',
    'Produk di Meja', 'Produk sebagai Aksesoris', 'Model menunjuk Produk', 'Produk di Dalam Tas'
  ], []);
  const ibGayaPoseOptions = useMemo(() => [
    'Gaya Casual (Jeans & Kaos)', 'Gaya Formal (Blazer)', 'Streetwear (Hoodie & Sneaker)', 'Pakaian Olahraga',
    'Pose Berdiri Percaya Diri', 'Pose Duduk Santai', 'Pose Berjalan Dinamis', 'Tersenyum ke Kamera', 'Pose Candid'
  ], []);
  const ibInteraksiOptions = useMemo(() => [
    'Model menggunakan Produk', 'Tatapan Kagum pada Produk', 'Aksi & Gerakan', 'Ekspresi Puas (Hasil Produk)'
  ], []);
  const ibCameraOptions = useMemo(() => [
    'Macro Shot', 'Close-up', 'Portrait', 'Wide Angle', 'Low Angle', 'High Angle'
  ], []);
  const ibBackgroundOptions = useMemo(() => [
    'Studio Profesional', 'Urban/Perkotaan', 'Alam Terbuka', 'Interior Mewah'
  ], []);
  const ibArtOptions = useMemo(() => [
    'Pencahayaan Dramatis', 'Gaya Vintage', 'Pop-Art Cerah', 'Hitam Putih Elegan'
  ], []);

  const onIBModelChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const f = e.target.files?.[0];
    if (!f) return;
    setIbModelFile(f);
    setIbModelPreview(URL.createObjectURL(f));
  };
  const onIBProductChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const f = e.target.files?.[0];
    if (!f) return;
    setIbProductFile(f);
    setIbProductPreview(URL.createObjectURL(f));
  };
  const toggleIbGaya = (id: string) => setIbGayaPose((prev) => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id]);
  const toggleIbInteraksi = (id: string) => setIbInteraksi((prev) => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id]);
  const handleIBGenerate = async () => {
    try {
      if (!ibModelFile || !ibProductFile) throw new Error('Unggah keduanya: model & produk');
      setIbError(null);
      setIbLoading(true);
      setIbGenerated([]);
      const fd = new FormData();
      fd.append('model', ibModelFile);
      fd.append('product', ibProductFile);
      fd.append('integrasi', ibIntegrasi);
      ibGayaPose.forEach(v => fd.append('gayaPose[]', v));
      ibInteraksi.forEach(v => fd.append('interaksi[]', v));
      fd.append('camera', ibCamera);
      fd.append('background', ibBackground);
      fd.append('art', ibArt);
      if (ibManual) fd.append('manual', ibManual);
      fd.append('aspect', ibAspect);
      fd.append('count', String(ibCount));

      const res = await fetch(`${API_BASE}/api/image-blender/generate`, { method: 'POST', body: fd });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal membuat gambar');
      setIbGenerated(Array.isArray(data?.images) ? data.images : []);
    } catch (e: any) {
      setIbError(e?.message || 'Terjadi kesalahan');
    } finally {
      setIbLoading(false);
    }
  };


  const styles = useMemo(
    () => ([
      { id: 'gaya-hidup', name: 'Gaya Hidup', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-ai-image-1758254198971.png' },
      { id: 'ilustrasi-kartun', name: 'Ilustrasi Kartun', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-ai-image-1758254261354.png' },
      { id: 'perbandingan', name: 'Perbandingan', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-ai-image-1758254308374.png' },
      { id: 'iklan-teks', name: 'Iklan Teks Dominan', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-ai-image-1758254547999.png' },
      { id: 'fokus-produk', name: 'Fokus Produk', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-ai-image-1758255117982.png' },
      { id: 'minimalis', name: 'Minimalis', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-ai-image-1758254628658.png' },
      { id: 'vintage', name: 'Vintage', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-ai-image-1758254679792.png' },
      { id: 'poster-propaganda', name: 'Poster Propaganda', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-ai-image-1758254732452.png' },
      { id: 'konseptual', name: 'Konseptual', img: 'https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-ai-image-1758254776398.png' },
      { id: 'custom', name: 'Gaya Custom', img: customPreview || '' },
    ]),
    [customPreview]
  );


  const onPickCustom: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const f = e.target.files?.[0];
    if (!f) return;
    const url = URL.createObjectURL(f);
    setCustomPreview(url);
    setSelectedStyle('custom');
  };

  const [genLoading, setGenLoading] = useState(false);
  const [genError, setGenError] = useState<string | null>(null);

  const handleGenerate = async () => {
    try {
      setGenError(null);
      setGenLoading(true);
      setGenerated([]);
      const styleObj = styles.find((s) => s.id === selectedStyle);
      const payload = {
        desc,
        styleName: styleObj?.name || null,
        styleImageUrl: selectedStyle !== 'custom' ? (styleObj?.img || null) : null,
        headline: headline || null,
        subHeadline: subHeadline || null,
        cta: cta || null,
        urgency: urgency || null,
        aspect,
        count,
      };
      const res = await fetch(`${API_BASE}/api/ads-image-gen/generate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal membuat gambar');
      const imgs = Array.isArray(data?.images) ? data.images : [];
      setGenerated(imgs);
    } catch (e: any) {
      setGenError(e?.message || 'Terjadi kesalahan');
    } finally {
      setGenLoading(false);
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Ads Image Generator</h1>
        <p className="text-gray-600">Buat dan optimalkan aset gambar iklan Anda.</p>
      </div>

      <div className="border-b border-gray-200 mb-4">
        <nav className="flex gap-4 text-sm">
          {([
            { id: 'ads-image-gen', label: 'Ads Image Gen' },
            { id: 'photo-enhancer', label: 'Photo Enhancer' },
            { id: 'image-blender', label: 'Image Blender' },
          ] as const).map((t) => (
            <button
              key={t.id}
              onClick={() => setActiveTab(t.id)}
              className={`pb-2 -mb-px border-b-2 ${activeTab === t.id ? 'border-green-600 text-green-700' : 'border-transparent text-gray-500 hover:text-gray-700'}`}
            >
              {t.label}
            </button>
          ))}
        </nav>
      </div>

      {activeTab === 'ads-image-gen' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left: Controls */}
          <div className="space-y-4">
            {/* Detail Konten */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="font-medium text-gray-800 mb-2 flex items-center gap-2">
                <FileText className="w-4 h-4 text-gray-500" />
                <span>Detail Konten</span>
              </div>
              <label className="block text-sm text-gray-700 mb-1">Deskripsi Produk, Target Audiens, & Konteks</label>
              <textarea value={desc} onChange={(e) => setDesc(e.target.value)} rows={4} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600" placeholder="Contoh: Sepatu lari ringan untuk pemula, target pria/wanita 18-35, fokus kenyamanan & harga terjangkau..." />
            </div>

            {/* Gaya Visual */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <Palette className="w-4 h-4 text-gray-500" />
                <span>Gaya Visual</span>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {styles.map((s) => (
                  <button
                    key={s.id}
                    type="button"
                    onClick={() => { if (s.id === 'custom') { setSelectedStyle('custom'); fileInputRef.current?.click(); } else { setSelectedStyle(s.id); } }}
                    onDragOver={(e) => { if (s.id === 'custom') e.preventDefault(); }}
                    onDrop={(e) => { if (s.id === 'custom') { e.preventDefault(); const f = e.dataTransfer.files?.[0]; if (f) { const url = URL.createObjectURL(f); setCustomPreview(url); setSelectedStyle('custom'); } } }}
                    className={`rounded-md border text-left overflow-hidden group ${selectedStyle === s.id ? 'border-green-600 ring-2 ring-green-200' : 'border-gray-200 hover:border-gray-300'}`}
                  >
                    <div className="aspect-video bg-gray-100 overflow-hidden">
                      {s.img ? (
                        <img src={s.img} alt={s.name} className="w-full h-full object-cover" />
                      ) : s.id === 'custom' ? (
                        <div className="flex items-center justify-center h-full px-3 text-xs text-gray-500 text-center">Tarik & Letakkan gambar atau klik untuk memilih</div>
                      ) : null}
                    </div>
                    <div className={`px-2 py-1 text-xs ${selectedStyle === s.id ? 'text-green-700' : 'text-gray-700'}`}>{s.name}</div>
                  </button>
                ))}
              </div>
              <input type="file" accept="image/*" ref={fileInputRef} onChange={onPickCustom} className="hidden" />
            </div>

            {/* Copywriting (Opsional) */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <Type className="w-4 h-4 text-gray-500" />
                <span>Copywriting (Opsional)</span>
              </div>
              <div className="grid sm:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm text-gray-700 mb-1 flex items-center justify-between">
                    <span>Headline (Opsional)</span>
                    <span className="text-xs text-gray-500">Gunakan <code className="bg-gray-100 px-1 py-0.5 rounded">**teks**</code> untuk bold</span>
                  </label>
                  <input value={headline} onChange={(e) => setHeadline(e.target.value)} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600" placeholder="Contoh: Lari Lebih Ringan, Jarak Lebih Jauh" />
                </div>
                <div>
                  <label className="block text-sm text-gray-700 mb-1 flex items-center justify-between">
                    <span>Sub Headline (Opsional)</span>
                    <span className="text-xs text-gray-500">Gunakan <code className="bg-gray-100 px-1 py-0.5 rounded">**teks**</code> untuk bold</span>
                  </label>
                  <input value={subHeadline} onChange={(e) => setSubHeadline(e.target.value)} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600" placeholder="Contoh: Nyaman dipakai harian & olahraga" />
                </div>
                <div>
                  <label className="block text-sm text-gray-700 mb-1 flex items-center justify-between">
                    <span>Call to Action (CTA) (Opsional)</span>
                    <span className="text-xs text-gray-500">Gunakan <code className="bg-gray-100 px-1 py-0.5 rounded">**teks**</code> untuk bold</span>
                  </label>
                  <input value={cta} onChange={(e) => setCta(e.target.value)} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600" placeholder="Contoh: Beli Sekarang" />
                </div>
                <div>
                  <label className="block text-sm text-gray-700 mb-1 flex items-center justify-between">
                    <span>Urgency (Opsional)</span>
                    <span className="text-xs text-gray-500">Gunakan <code className="bg-gray-100 px-1 py-0.5 rounded">**teks**</code> untuk bold</span>
                  </label>
                  <input value={urgency} onChange={(e) => setUrgency(e.target.value)} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600" placeholder="Contoh: Diskon s/d 50% minggu ini" />
                </div>
              </div>
            </div>

            {/* Pengaturan Gambar */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <Settings className="w-4 h-4 text-gray-500" />
                <span>Pengaturan Gambar</span>
              </div>
              <div className="grid sm:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Aspek Rasio</label>
                  <select value={aspect} onChange={(e) => setAspect(e.target.value as any)} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600">
                    <option value="1:1">1:1</option>
                    <option value="4:5">4:5</option>
                    <option value="3:4">3:4</option>
                    <option value="16:9">16:9</option>
                    <option value="9:16">9:16</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Jumlah Gambar</label>
                  <select value={count} onChange={(e) => setCount(parseInt(e.target.value, 10) as any)} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600">
                    <option value={1}>1</option>
                    <option value={2}>2</option>
                    <option value={3}>3</option>
                    <option value={4}>4</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={handleGenerate}
                disabled={genLoading || !desc}
                className={`inline-flex items-center justify-center px-4 py-2 text-white text-sm rounded-md ${genLoading || !desc ? 'bg-green-500 cursor-not-allowed opacity-70' : 'bg-green-600 hover:bg-green-700'}`}
                title={!desc ? 'Isi deskripsi terlebih dahulu' : ''}
              >
                <Wand2 className={`w-4 h-4 mr-2 ${genLoading ? 'animate-pulse' : ''}`} />
                <span>{genLoading ? 'Membuat…' : 'Buat Gambar dengan AI'}</span>
              </button>
              {genError && <span className="text-sm text-red-600">{genError}</span>}
            </div>
          </div>

          {/* Right: Result */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
              <Images className="w-4 h-4 text-gray-500" />
              <span>Hasil Gambar</span>
            </div>
            {genLoading ? (
              <div className="h-64 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-600">
                Membuat gambar… mohon tunggu
              </div>
            ) : generated.length ? (
              <div className="grid grid-cols-2 gap-3">
                {generated.map((src, i) => (
                  <div key={i} className="border border-gray-200 rounded-md overflow-hidden bg-gray-50">
                    <img src={src} alt={`Hasil ${i + 1}`} className="w-full h-auto block" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-64 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500">
                Belum ada hasil. Klik "Buat Gambar dengan AI" untuk melihat pratinjau.
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'photo-enhancer' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left: Controls */}
          <div className="space-y-4">
            {/* Upload */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <Images className="w-4 h-4 text-gray-500" />
                <span>Unggah Gambar Produk</span>
              </div>
              <input ref={peFileInputRef} type="file" accept="image/*" onChange={onPeFileChange} className="hidden" />
              <div
                onClick={() => peFileInputRef.current?.click()}
                onDragOver={(e) => { e.preventDefault(); setPeDragOver(true); }}
                onDragLeave={() => setPeDragOver(false)}
                onDrop={(e) => { e.preventDefault(); setPeDragOver(false); const f = e.dataTransfer.files?.[0]; if (f) { setPeFile(f); setPePreview(URL.createObjectURL(f)); } }}
                className={`${pePreview ? 'border border-gray-200 rounded-md overflow-hidden bg-gray-50 cursor-pointer' : (peDragOver ? 'h-48 border-2 border-dashed border-green-500 bg-green-50 rounded-md flex items-center justify-center text-sm text-gray-500 text-center px-4 cursor-pointer' : 'h-48 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500 text-center px-4 cursor-pointer')}`}
              >
                {pePreview ? (
                  <img src={pePreview} alt="Preview" className="w-full h-auto" />
                ) : (
                  <span>Tarik & letakkan gambar produk di sini, atau klik untuk memilih</span>
                )}
              </div>
            </div>

            {/* Gaya & Kualitas */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <Wand2 className="w-4 h-4 text-gray-500" />
                <span>Tentukan Gaya & Kualitas</span>
              </div>
              <div className="grid gap-3">
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Headline (Teks Utama)</label>
                  <textarea value={peHeadline} onChange={(e) => setPeHeadline(e.target.value)} rows={2} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600" />
                </div>
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Sub-headline (Teks Tambahakan)</label>
                  <textarea value={peSubHeadline} onChange={(e) => setPeSubHeadline(e.target.value)} rows={2} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600" />
                </div>
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Instruksi Gaya & Latar Belakang</label>
                  <textarea value={peInstruksi} onChange={(e) => setPeInstruksi(e.target.value)} rows={3} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600" />
                </div>
                <label className="inline-flex items-center gap-2 text-sm text-gray-700">
                  <input type="checkbox" checked={peHD} onChange={(e) => setPeHD(e.target.checked)} />
                  <span>Tingkatkan ke Kualitas HD</span>
                </label>
                <div className="grid sm:grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm text-gray-700 mb-1">Jumlah Gambar</label>
                    <select value={peCount} onChange={(e) => setPeCount(parseInt(e.target.value, 10) as any)} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600">
                      <option value={1}>1</option>
                      <option value={2}>2</option>
                      <option value={3}>3</option>
                      <option value={4}>4</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm text-gray-700 mb-1">Pilih Aspek Rasio</label>
                    <select value={peAspect} onChange={(e) => setPeAspect(e.target.value as any)} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600">
                      <option value="1:1">1:1</option>
                      <option value="4:5">4:5</option>
                      <option value="3:4">3:4</option>
                      <option value="16:9">16:9</option>
                      <option value="9:16">9:16</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Tema */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <Palette className="w-4 h-4 text-gray-500" />
                <span>Tema</span>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {peTemaItems.map((t) => (
                  <button key={t.id} type="button" onClick={() => setPeTema(t.id)} className={`rounded-md border text-left overflow-hidden ${peTema === t.id ? 'border-green-600 ring-2 ring-green-200' : 'border-gray-200 hover:border-gray-300'}`}>
                    <div className="aspect-video bg-gray-100 overflow-hidden">
                      <img src={t.img} alt={t.name} className="w-full h-full object-cover" />
                    </div>
                    <div className={`px-2 py-1 text-xs ${peTema === t.id ? 'text-green-700' : 'text-gray-700'}`}>{t.name}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Detailing Produk */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <Layers className="w-4 h-4 text-gray-500" />
                <span>Detailing Produk</span>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-sm">
                {['Tonjolkan tekstur', 'Detail bordir/jahitan', 'Refleksi berkilau', 'Bahan & komposisi'].map((d) => (
                  <button key={d} type="button" onClick={() => toggleDetailing(d)} className={`px-2 py-2 rounded-md border ${peDetailing.includes(d) ? 'border-green-600 text-green-700 bg-green-50' : 'border-gray-200 text-gray-700 hover:bg-gray-50'}`}>{d}</button>
                ))}
              </div>
            </div>

            {/* Sudut Kamera */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <Camera className="w-4 h-4 text-gray-500" />
                <span>Sudut Kamera</span>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-sm">
                {[
                  { id: 'macro', label: 'Macro Shot' },
                  { id: 'close-up', label: 'Close-up' },
                  { id: 'portrait', label: 'Portrait' },
                  { id: 'wide', label: 'Wide Angle' },
                  { id: 'low', label: 'Low Angle' },
                  { id: 'high', label: 'High Angle' },
                ].map((s) => (
                  <button key={s.id} type="button" onClick={() => setPeSudut(s.id)} className={`px-2 py-2 rounded-md border ${peSudut === s.id ? 'border-green-600 text-green-700 bg-green-50' : 'border-gray-200 text-gray-700 hover:bg-gray-50'}`}>{s.label}</button>
                ))}
              </div>
            </div>

            <div className="flex">
              <button
                onClick={handlePeGenerate}
                disabled={peLoading || !peFile}
                className={`inline-flex items-center justify-center px-4 py-2 text-white text-sm rounded-md ${peLoading || !peFile ? 'bg-green-500 cursor-not-allowed opacity-70' : 'bg-green-600 hover:bg-green-700'}`}
                title={!peFile ? 'Unggah gambar produk terlebih dahulu' : ''}
              >
                <Wand2 className={`w-4 h-4 mr-2 ${peLoading ? 'animate-pulse' : ''}`} />
                <span>{peLoading ? 'Membuat…' : 'Buat Gambar dengan AI'}</span>
              </button>
              {peError && <span className="text-sm text-red-600 ml-3">{peError}</span>}
            </div>
          </div>

          {/* Right: Result */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
              <Images className="w-4 h-4 text-gray-500" />
              <span>Hasil Gambar</span>
            </div>
            {peLoading ? (
              <div className="h-64 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-600 text-center px-4">
                Membuat gambar… mohon tunggu
              </div>
            ) : peGenerated.length ? (
              <div className="grid grid-cols-2 gap-3">
                {peGenerated.map((src, i) => (
                  <div key={i} className="border border-gray-200 rounded-md overflow-hidden bg-gray-50">
                    <img src={src} alt={`Hasil ${i + 1}`} className="w-full h-auto block" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-64 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500 text-center px-4">
                Belum ada hasil. Isi pengaturan lalu klik "Buat Gambar dengan AI".
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'image-blender' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left: Controls */}
          <div className="space-y-4">
            {/* Uploads */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <Images className="w-4 h-4 text-gray-500" />
                <span>Unggah Gambar Anda</span>
              </div>

              {/* Grid layout 50:50 untuk gambar dasar dan tambahan */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {/* Gambar Dasar (Model) */}
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Gambar Dasar (Model)</label>
                  <input ref={ibModelInputRef} type="file" accept="image/*" onChange={onIBModelChange} className="hidden" />
                  <div
                    onClick={() => ibModelInputRef.current?.click()}
                    onDragOver={(e) => { e.preventDefault(); setIbDragModel(true); }}
                    onDragLeave={() => setIbDragModel(false)}
                    onDrop={(e) => { e.preventDefault(); setIbDragModel(false); const f = e.dataTransfer.files?.[0]; if (f) { setIbModelFile(f); setIbModelPreview(URL.createObjectURL(f)); } }}
                    className={`${ibModelPreview ? 'border border-gray-200 rounded-md overflow-hidden bg-gray-50 cursor-pointer h-36' : (ibDragModel ? 'h-36 border-2 border-dashed border-green-500 bg-green-50 rounded-md flex items-center justify-center text-sm text-gray-500 text-center px-2 cursor-pointer' : 'h-36 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500 text-center px-2 cursor-pointer')}`}
                  >
                    {ibModelPreview ? (
                      <img src={ibModelPreview} alt="Model" className="w-full h-full max-h-36 object-cover" />
                    ) : (
                      <span>Tarik & letakkan gambar model di sini, atau klik untuk memilih</span>
                    )}
                  </div>
                </div>

                {/* Gambar Tambahan (Produk) */}
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Gambar Tambahan (Produk)</label>
                  <input ref={ibProductInputRef} type="file" accept="image/*" onChange={onIBProductChange} className="hidden" />
                  <div
                    onClick={() => ibProductInputRef.current?.click()}
                    onDragOver={(e) => { e.preventDefault(); setIbDragProduct(true); }}
                    onDragLeave={() => setIbDragProduct(false)}
                    onDrop={(e) => { e.preventDefault(); setIbDragProduct(false); const f = e.dataTransfer.files?.[0]; if (f) { setIbProductFile(f); setIbProductPreview(URL.createObjectURL(f)); } }}
                    className={`${ibProductPreview ? 'border border-gray-200 rounded-md overflow-hidden bg-gray-50 cursor-pointer h-36' : (ibDragProduct ? 'h-36 border-2 border-dashed border-green-500 bg-green-50 rounded-md flex items-center justify-center text-sm text-gray-500 text-center px-2 cursor-pointer' : 'h-36 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500 text-center px-2 cursor-pointer')}`}
                  >
                    {ibProductPreview ? (
                      <img src={ibProductPreview} alt="Produk" className="w-full h-full max-h-36 object-cover" />
                    ) : (
                      <span>Tarik & letakkan gambar produk di sini, atau klik untuk memilih</span>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Beri Instruksi AI */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
                <Wand2 className="w-4 h-4 text-gray-500" />
                <span>Beri Instruksi AI</span>
              </div>

              {/* Integrasi Produk (single) */}
              <div className="mb-4">
                <div className="text-sm font-medium text-gray-800 mb-2">Integrasi Produk</div>
                <div className="grid grid-cols-2 sm:grid-cols-2 gap-2 text-sm">
                  {ibIntegrasiOptions.map((opt) => (
                    <button key={opt} type="button" onClick={() => setIbIntegrasi(opt)} className={`px-2 py-2 rounded-md border text-left ${ibIntegrasi === opt ? 'border-green-600 text-green-700 bg-green-50' : 'border-gray-200 text-gray-700 hover:bg-gray-50'}`}>{opt}</button>
                  ))}
                </div>
              </div>

              {/* Gaya Model & Pose (multi) */}
              <div className="mb-4">
                <div className="text-sm font-medium text-gray-800 mb-2">Gaya Model & Pose</div>
                <div className="grid grid-cols-2 sm:grid-cols-2 gap-2 text-sm">
                  {ibGayaPoseOptions.map((opt) => (
                    <button key={opt} type="button" onClick={() => toggleIbGaya(opt)} className={`px-2 py-2 rounded-md border text-left ${ibGayaPose.includes(opt) ? 'border-green-600 text-green-700 bg-green-50' : 'border-gray-200 text-gray-700 hover:bg-gray-50'}`}>{opt}</button>
                  ))}
                </div>
              </div>

              {/* Interaksi dengan Produk (multi) */}
              <div className="mb-4">
                <div className="text-sm font-medium text-gray-800 mb-2">Interaksi dengan Produk</div>
                <div className="grid grid-cols-2 sm:grid-cols-2 gap-2 text-sm">
                  {ibInteraksiOptions.map((opt) => (
                    <button key={opt} type="button" onClick={() => toggleIbInteraksi(opt)} className={`px-2 py-2 rounded-md border text-left ${ibInteraksi.includes(opt) ? 'border-green-600 text-green-700 bg-green-50' : 'border-gray-200 text-gray-700 hover:bg-gray-50'}`}>{opt}</button>
                  ))}
                </div>
              </div>

              {/* Sudut Kamera (single) */}
              <div className="mb-4">
                <div className="text-sm font-medium text-gray-800 mb-2">Sudut Kamera</div>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-sm">
                  {ibCameraOptions.map((opt) => (
                    <button key={opt} type="button" onClick={() => setIbCamera(opt)} className={`px-2 py-2 rounded-md border ${ibCamera === opt ? 'border-green-600 text-green-700 bg-green-50' : 'border-gray-200 text-gray-700 hover:bg-gray-50'}`}>{opt}</button>
                  ))}
                </div>
              </div>

              {/* Latar Belakang & Lingkungan (single) */}
              <div className="mb-4">
                <div className="text-sm font-medium text-gray-800 mb-2">Latar Belakang & Lingkungan</div>
                <div className="grid grid-cols-2 sm:grid-cols-2 gap-2 text-sm">
                  {ibBackgroundOptions.map((opt) => (
                    <button key={opt} type="button" onClick={() => setIbBackground(opt)} className={`px-2 py-2 rounded-md border ${ibBackground === opt ? 'border-green-600 text-green-700 bg-green-50' : 'border-gray-200 text-gray-700 hover:bg-gray-50'}`}>{opt}</button>
                  ))}
                </div>
              </div>

              {/* Gaya Artistik (single) */}
              <div className="mb-4">
                <div className="text-sm font-medium text-gray-800 mb-2">Gaya Artistik</div>
                <div className="grid grid-cols-2 sm:grid-cols-2 gap-2 text-sm">
                  {ibArtOptions.map((opt) => (
                    <button key={opt} type="button" onClick={() => setIbArt(opt)} className={`px-2 py-2 rounded-md border ${ibArt === opt ? 'border-green-600 text-green-700 bg-green-50' : 'border-gray-200 text-gray-700 hover:bg-gray-50'}`}>{opt}</button>
                  ))}
                </div>
              </div>

              {/* Instruksi Manual */}
              <div className="mb-4">
                <label className="block text-sm text-gray-700 mb-1">Tulis Instruksi Manual</label>
                <textarea value={ibManual} onChange={(e) => setIbManual(e.target.value)} rows={3} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600" />
              </div>

              {/* Aspek Rasio & Jumlah */}
              <div className="grid sm:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Aspek Rasio</label>
                  <select value={ibAspect} onChange={(e) => setIbAspect(e.target.value as any)} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600">
                    <option value="1:1">1:1</option>
                    <option value="4:5">4:5</option>
                    <option value="3:4">3:4</option>
                    <option value="16:9">16:9</option>
                    <option value="9:16">9:16</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Jumlah Output</label>
                  <select value={ibCount} onChange={(e) => setIbCount(parseInt(e.target.value, 10) as any)} className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-green-600">
                    <option value={1}>1</option>
                    <option value={2}>2</option>
                    <option value={3}>3</option>
                    <option value={4}>4</option>
                  </select>
                </div>
              </div>

              <div className="mt-4 flex">
                <button
                  onClick={handleIBGenerate}
                  disabled={ibLoading || !ibModelFile || !ibProductFile}
                  className={`inline-flex items-center justify-center px-4 py-2 text-white text-sm rounded-md ${ibLoading || !ibModelFile || !ibProductFile ? 'bg-green-500 cursor-not-allowed opacity-70' : 'bg-green-600 hover:bg-green-700'}`}
                  title={!ibModelFile || !ibProductFile ? 'Unggah gambar model & produk terlebih dahulu' : ''}
                >
                  <Wand2 className={`w-4 h-4 mr-2 ${ibLoading ? 'animate-pulse' : ''}`} />
                  <span>{ibLoading ? 'Membuat\u2026' : 'Buat Gambar dengan AI'}</span>
                </button>
                {ibError && <span className="text-sm text-red-600 ml-3">{ibError}</span>}
              </div>
            </div>
          </div>

          {/* Right: Result */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
              <Images className="w-4 h-4 text-gray-500" />
              <span>Hasil Gambar</span>
            </div>
            {ibLoading ? (
              <div className="h-64 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-600 text-center px-4">
                Membuat gambar\u2026 mohon tunggu
              </div>
            ) : ibGenerated.length ? (
              <div className="grid grid-cols-2 gap-3">
                {ibGenerated.map((src, i) => (
                  <div key={i} className="border border-gray-200 rounded-md overflow-hidden bg-gray-50">
                    <img src={src} alt={`Hasil ${i + 1}`} className="w-full h-auto block" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-64 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500 text-center px-4">
                Belum ada hasil. Isi pengaturan lalu klik "Buat Gambar dengan AI".
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdsImageGeneratorPage;
