<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class StorageAccessTest extends TestCase
{
    use RefreshDatabase;

    public function test_storage_route_serves_files_with_cors_headers()
    {
        // Create a test file in public storage
        Storage::fake('public');
        $file = UploadedFile::fake()->image('test-payment-proof.jpg', 800, 600);
        $path = $file->storeAs('payment_proofs', 'test_payment_proof.jpg', 'public');

        // Create actual file for testing
        $testFilePath = storage_path('app/public/payment_proofs/test_payment_proof.jpg');
        $testDir = dirname($testFilePath);

        if (!is_dir($testDir)) {
            mkdir($testDir, 0755, true);
        }

        // Create a simple test image
        $imageContent = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==');
        file_put_contents($testFilePath, $imageContent);

        // Test the storage route
        $response = $this->get('/api/storage/payment_proofs/test_payment_proof.jpg');

        $response->assertStatus(200);
        $this->assertTrue(str_contains($response->headers->get('Content-Type'), 'image/'));
        $this->assertTrue(str_contains($response->headers->get('Cache-Control'), 'max-age=3600'));
        $response->assertHeader('Access-Control-Allow-Origin');
        $this->assertTrue(str_contains($response->headers->get('Access-Control-Allow-Methods'), 'GET'));

        // Clean up
        if (file_exists($testFilePath)) {
            unlink($testFilePath);
        }
    }

    public function test_storage_route_with_cors_origin()
    {
        // Create a test file
        $testFilePath = storage_path('app/public/payment_proofs/test_cors.jpg');
        $testDir = dirname($testFilePath);

        if (!is_dir($testDir)) {
            mkdir($testDir, 0755, true);
        }

        $imageContent = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==');
        file_put_contents($testFilePath, $imageContent);

        // Test with production origin
        $response = $this->withHeaders([
            'Origin' => 'https://app.gooap.com'
        ])->get('/api/storage/payment_proofs/test_cors.jpg');

        $response->assertStatus(200);
        $response->assertHeader('Access-Control-Allow-Origin', 'https://app.gooap.com');

        // Test with localhost origin
        $response = $this->withHeaders([
            'Origin' => 'http://localhost:5173'
        ])->get('/api/storage/payment_proofs/test_cors.jpg');

        $response->assertStatus(200);
        $response->assertHeader('Access-Control-Allow-Origin', 'http://localhost:5173');

        // Test with unknown origin
        $response = $this->withHeaders([
            'Origin' => 'https://unknown-domain.com'
        ])->get('/api/storage/payment_proofs/test_cors.jpg');

        $response->assertStatus(200);
        $response->assertHeader('Access-Control-Allow-Origin', 'http://localhost:5173');

        // Clean up
        if (file_exists($testFilePath)) {
            unlink($testFilePath);
        }
    }

    public function test_storage_route_returns_404_for_missing_files()
    {
        $response = $this->get('/api/storage/payment_proofs/non-existent-file.jpg');

        $response->assertStatus(404);
    }

    public function test_storage_route_handles_nested_paths()
    {
        // Create nested directory structure
        $testFilePath = storage_path('app/public/payment_proofs/2025/01/nested_test.jpg');
        $testDir = dirname($testFilePath);

        if (!is_dir($testDir)) {
            mkdir($testDir, 0755, true);
        }

        $imageContent = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==');
        file_put_contents($testFilePath, $imageContent);

        // Test nested path
        $response = $this->get('/api/storage/payment_proofs/2025/01/nested_test.jpg');

        $response->assertStatus(200);
        $this->assertTrue(str_contains($response->headers->get('Content-Type'), 'image/'));

        // Clean up
        if (file_exists($testFilePath)) {
            unlink($testFilePath);
        }

        // Clean up directories
        $dirs = [
            dirname($testFilePath),
            dirname(dirname($testFilePath)),
            dirname(dirname(dirname($testFilePath)))
        ];

        foreach ($dirs as $dir) {
            if (is_dir($dir) && count(scandir($dir)) == 2) { // Only . and ..
                rmdir($dir);
            }
        }
    }
}
