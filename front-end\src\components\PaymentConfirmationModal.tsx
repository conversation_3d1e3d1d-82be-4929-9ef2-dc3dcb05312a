import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';
import {
  X,
  Upload,
  FileImage,
  AlertCircle,
  Loader2,
  CheckCircle,
  CreditCard,
  Calendar,
  DollarSign
} from 'lucide-react';

const API_BASE = import.meta.env.VITE_API_BASE || 'http://127.0.0.1:8000';

interface Invoice {
  id: number;
  invoice_number: string;
  amount: number;
  due_date: string;
  subscription_package: {
    name: string;
    duration_label: string;
  } | null;
  payment_method: {
    name: string;
    type: string;
  } | null;
}

interface PaymentConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: Invoice | null;
  onPaymentSubmitted: () => void;
}

const PaymentConfirmationModal: React.FC<PaymentConfirmationModalProps> = ({
  isOpen,
  onClose,
  invoice,
  onPaymentSubmitted
}) => {
  const { startSubscriptionPolling } = useAuth();
  const [proofFile, setProofFile] = useState<File | null>(null);
  const [proofPreview, setProofPreview] = useState<string>('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isDragOver, setIsDragOver] = useState(false);

  // Helper function to get auth headers
  const getAuthHeaders = () => {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      throw new Error('No authentication token found');
    }
    return {
      'Authorization': `Bearer ${token}`,
    };
  };

  const handleFileSelect = (file: File) => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file (JPG, PNG, etc.)');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('File size must be less than 5MB');
      return;
    }

    setProofFile(file);
    setError('');

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setProofPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleSubmitPayment = async () => {
    if (!invoice) return;

    if (!proofFile) {
      setError('Please upload payment proof');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const formData = new FormData();
      formData.append('payment_proof', proofFile);
      formData.append('notes', notes);

      const response = await fetch(`${API_BASE}/api/invoices/${invoice.id}/submit-payment`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        onPaymentSubmitted();
        onClose();

        // Start polling for subscription status changes
        startSubscriptionPolling();

        // Reset form
        setProofFile(null);
        setProofPreview('');
        setNotes('');
      } else {
        setError(data.message || 'Failed to submit payment proof');
      }

    } catch (error) {
      console.error('Error submitting payment:', error);
      setError('Failed to submit payment proof');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleClose = () => {
    setProofFile(null);
    setProofPreview('');
    setNotes('');
    setError('');
    onClose();
  };

  if (!isOpen || !invoice) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Konfirmasi Pembayaran</h2>
              <p className="text-gray-600 mt-1">Upload bukti transfer untuk konfirmasi pembayaran</p>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Invoice Details */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-gray-900 mb-3">Detail Tagihan</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Nomor Invoice:</span>
                <p className="font-medium">{invoice.invoice_number}</p>
              </div>
              <div>
                <span className="text-gray-600">Paket:</span>
                <p className="font-medium">{invoice.subscription_package?.name || 'Tidak tersedia'}</p>
              </div>
              <div>
                <span className="text-gray-600">Durasi:</span>
                <p className="font-medium">{invoice.subscription_package?.duration_label || ''}</p>
              </div>
              <div>
                <span className="text-gray-600">Metode Pembayaran:</span>
                <p className="font-medium">{invoice.payment_method?.name || 'Tidak tersedia'}</p>
              </div>
              <div>
                <span className="text-gray-600">Jatuh Tempo:</span>
                <p className="font-medium">{formatDate(invoice.due_date)}</p>
              </div>
              <div>
                <span className="text-gray-600">Total Pembayaran:</span>
                <p className="font-bold text-lg text-emerald-600">{formatPrice(invoice.amount)}</p>
              </div>
            </div>
          </div>

          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
                <span className="text-red-700">{error}</span>
              </div>
            </div>
          )}

          {/* Upload Area */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bukti Transfer <span className="text-red-500">*</span>
            </label>

            <div
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${isDragOver
                ? 'border-emerald-500 bg-emerald-50'
                : 'border-gray-300 hover:border-gray-400'
                }`}
            >
              {proofPreview ? (
                <div className="relative">
                  <img
                    src={proofPreview}
                    alt="Payment proof preview"
                    className="max-w-full max-h-64 mx-auto rounded-lg"
                  />
                  <button
                    onClick={() => {
                      setProofFile(null);
                      setProofPreview('');
                    }}
                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                  <p className="mt-2 text-sm text-gray-600">{proofFile?.name}</p>
                </div>
              ) : (
                <div>
                  <FileImage className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-2">
                    Drag & drop bukti transfer atau{' '}
                    <label className="text-emerald-600 hover:text-emerald-700 cursor-pointer font-medium">
                      browse file
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleFileInputChange}
                        className="hidden"
                      />
                    </label>
                  </p>
                  <p className="text-xs text-gray-500">
                    Format: JPG, PNG, GIF (Max: 5MB)
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Catatan (Opsional)
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Tambahkan catatan jika diperlukan..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 resize-none"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-end">
            <button
              onClick={handleClose}
              disabled={loading}
              className="px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              Batal
            </button>
            <button
              onClick={handleSubmitPayment}
              disabled={loading || !proofFile}
              className="px-6 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  Mengirim...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  Kirim Bukti Pembayaran
                </>
              )}
            </button>
          </div>

          {/* Info */}
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-blue-500 mr-2 mt-0.5" />
              <div className="text-sm text-blue-700">
                <p className="font-medium mb-1">Informasi Penting:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Bukti transfer akan direview oleh admin</li>
                  <li>Proses verifikasi membutuhkan waktu 1x24 jam</li>
                  <li>Anda akan mendapat notifikasi setelah pembayaran dikonfirmasi</li>
                  <li>Pastikan bukti transfer jelas dan dapat dibaca</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentConfirmationModal;
