import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';
import PaymentConfirmationModal from './PaymentConfirmationModal';
import {
  FileText,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  CreditCard,
  Package,
  Loader2,
  RefreshCw
} from 'lucide-react';

const API_BASE = import.meta.env.VITE_API_BASE || 'http://127.0.0.1:8000';

interface Invoice {
  id: number;
  invoice_number: string;
  user_id: number;
  subscription_package_id: number;
  payment_method_id: number | null;
  amount: number;
  status: 'pending' | 'pending_verification' | 'paid' | 'failed' | 'cancelled';
  due_date: string;
  paid_at: string | null;
  payment_details: any;
  notes: string | null;
  created_at: string;
  updated_at: string;
  subscription_package: {
    id: number;
    name: string;
    duration_label: string;
    color: string;
  } | null;
  payment_method: {
    id: number;
    name: string;
    type: string;
  } | null;
}

interface InvoicesPageProps {
  selectedInvoiceId?: number;
  onInvoiceSelect?: (invoice: Invoice | null) => void;
}

const InvoicesPage: React.FC<InvoicesPageProps> = ({ selectedInvoiceId, onInvoiceSelect }) => {
  const { user, refreshUser } = useAuth();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [paymentProcessing, setPaymentProcessing] = useState(false);
  const [error, setError] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentInvoice, setPaymentInvoice] = useState<Invoice | null>(null);

  // Helper function to get auth headers
  const getAuthHeaders = () => {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      throw new Error('No authentication token found');
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    };
  };

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      setError('');

      const headers = getAuthHeaders();
      const response = await fetch(`${API_BASE}/api/invoices`, {
        method: 'GET',
        headers: headers,
      });

      const data = await response.json();
      if (data.success) {
        setInvoices(data.invoices);

        // Auto-select invoice if selectedInvoiceId is provided
        if (selectedInvoiceId) {
          const invoice = data.invoices.find((inv: Invoice) => inv.id === selectedInvoiceId);
          if (invoice) {
            setSelectedInvoice(invoice);
            onInvoiceSelect?.(invoice);
          }
        }
      } else {
        setError(data.message || 'Failed to fetch invoices');
      }
    } catch (error) {
      console.error('Error fetching invoices:', error);
      if (error instanceof Error && error.message === 'No authentication token found') {
        setError('Please login to view invoices');
      } else {
        setError('Failed to fetch invoices');
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePayInvoice = (invoice: Invoice) => {
    setPaymentInvoice(invoice);
    setShowPaymentModal(true);
  };

  const handlePaymentSubmitted = async () => {
    await fetchInvoices();
    setShowPaymentModal(false);
    setPaymentInvoice(null);
    toast.success('Bukti Pembayaran Terkirim!\nBukti pembayaran berhasil dikirim. Menunggu konfirmasi admin dalam 1x24 jam.', {
      duration: 6000,
    });
  };

  const handleRefreshStatus = async () => {
    try {
      setLoading(true);
      await refreshUser();
      await fetchInvoices();
      toast.success('Status Diperbarui\nStatus subscription dan invoice telah diperbarui.');
    } catch (error) {
      toast.error('Gagal Memperbarui\nTerjadi kesalahan saat memperbarui status.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Paid
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </span>
        );
      case 'pending_verification':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <AlertCircle className="w-3 h-3 mr-1" />
            Menunggu Verifikasi
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="w-3 h-3 mr-1" />
            Failed
          </span>
        );
      case 'cancelled':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            <XCircle className="w-3 h-3 mr-1" />
            Cancelled
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const isOverdue = (invoice: Invoice) => {
    return invoice.status === 'pending' && new Date(invoice.due_date) < new Date();
  };

  useEffect(() => {
    if (user) {
      fetchInvoices();
    }
  }, [user]);

  // Show login message if user is not authenticated
  if (!user) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Login Required</h3>
          <p className="text-gray-500">Please login to view your invoices.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <FileText className="w-6 h-6 mr-2 text-emerald-600" />
              Riwayat Tagihan
            </h1>
            <p className="text-gray-600 mt-1">Kelola dan lihat riwayat tagihan langganan Anda</p>
          </div>
          <button
            onClick={handleRefreshStatus}
            disabled={loading}
            className="flex items-center px-4 py-2 text-emerald-600 border border-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh Status
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Invoices List */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="w-6 h-6 animate-spin text-emerald-600" />
                <span className="ml-2 text-gray-600">Loading invoices...</span>
              </div>
            ) : invoices.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada tagihan</h3>
                <p className="text-gray-500">Tagihan akan muncul setelah Anda berlangganan paket.</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {invoices.map((invoice) => (
                  <div
                    key={invoice.id}
                    onClick={() => {
                      setSelectedInvoice(invoice);
                      onInvoiceSelect?.(invoice);
                    }}
                    className={`p-6 cursor-pointer hover:bg-gray-50 transition-colors ${selectedInvoice?.id === invoice.id ? 'bg-emerald-50 border-l-4 border-emerald-500' : ''
                      }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-lg font-medium text-gray-900">
                            {invoice.invoice_number}
                          </h3>
                          {getStatusBadge(invoice.status)}
                        </div>

                        <div className="flex items-center text-sm text-gray-600 mb-2">
                          <Package className="w-4 h-4 mr-1" />
                          <span>{invoice.subscription_package?.name || 'Paket tidak tersedia'}</span>
                          <span className="mx-2">•</span>
                          <span>{invoice.subscription_package?.duration_label || ''}</span>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-sm text-gray-600">
                            <Calendar className="w-4 h-4 mr-1" />
                            <span>Dibuat: {formatDate(invoice.created_at)}</span>
                            {invoice.status === 'pending' && (
                              <>
                                <span className="mx-2">•</span>
                                <span className={isOverdue(invoice) ? 'text-red-600' : ''}>
                                  Jatuh tempo: {formatDate(invoice.due_date)}
                                </span>
                              </>
                            )}
                          </div>
                          <div className="text-lg font-bold text-gray-900">
                            {formatPrice(invoice.amount)}
                          </div>
                        </div>

                        {isOverdue(invoice) && (
                          <div className="mt-2 text-sm text-red-600 font-medium">
                            ⚠️ Tagihan sudah jatuh tempo
                          </div>
                        )}
                      </div>

                      <div className="ml-4">
                        <Eye className="w-5 h-5 text-gray-400" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Invoice Detail */}
        <div className="lg:col-span-1">
          {selectedInvoice ? (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Detail Tagihan</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Nomor Invoice</label>
                  <p className="text-sm text-gray-900">{selectedInvoice.invoice_number}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <div className="mt-1">
                    {getStatusBadge(selectedInvoice.status)}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Paket</label>
                  <p className="text-sm text-gray-900">
                    {selectedInvoice.subscription_package?.name || 'Tidak tersedia'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {selectedInvoice.subscription_package?.duration_label || ''}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Metode Pembayaran</label>
                  <p className="text-sm text-gray-900">
                    {selectedInvoice.payment_method?.name || 'Tidak tersedia'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Jumlah</label>
                  <p className="text-lg font-bold text-gray-900">{formatPrice(selectedInvoice.amount)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Tanggal Dibuat</label>
                  <p className="text-sm text-gray-900">{formatDate(selectedInvoice.created_at)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Jatuh Tempo</label>
                  <p className={`text-sm ${isOverdue(selectedInvoice) ? 'text-red-600 font-medium' : 'text-gray-900'}`}>
                    {formatDate(selectedInvoice.due_date)}
                  </p>
                </div>

                {selectedInvoice.paid_at && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Tanggal Dibayar</label>
                    <p className="text-sm text-gray-900">{formatDate(selectedInvoice.paid_at)}</p>
                  </div>
                )}

                {selectedInvoice.notes && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Catatan</label>
                    <p className="text-sm text-gray-900">{selectedInvoice.notes}</p>
                  </div>
                )}
              </div>

              {selectedInvoice.status === 'pending' && (
                <div className="mt-6">
                  <button
                    onClick={() => handlePayInvoice(selectedInvoice)}
                    className="w-full flex items-center justify-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
                  >
                    <CreditCard className="w-4 h-4 mr-2" />
                    Bayar Sekarang
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-center text-gray-500">
                <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Pilih tagihan untuk melihat detail</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Payment Confirmation Modal */}
      <PaymentConfirmationModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        invoice={paymentInvoice}
        onPaymentSubmitted={handlePaymentSubmitted}
      />
    </div>
  );
};

export default InvoicesPage;
