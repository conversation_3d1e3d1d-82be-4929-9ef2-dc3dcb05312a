import OpenAI from "openai";
import fs from "fs";

const openai = new OpenAI({ apiKey: '***************************************************' });

async function generateWithReference() {
    const result = await openai.images.generate({
        model: "gpt-image-1",
        prompt: `
Buatkan visual iklan skincare premium:

✨ **<PERSON><PERSON>i Produk, Target Audiens & Konteks**  
RadiGlow Brightening Serum adalah serum pencerah wajah dengan Niacinamide 10%, Vitamin C Stabil, dan Hyaluronic Acid berteknologi NanoEncapsulation. Produk ini ditujukan untuk remaja hingga dewasa muda (18–35 tahun) yang tinggal di perkotaan, aktif di media sosial, peduli penampilan, dan mencari skincare aman serta efektif. Konteksnya adalah kampanye iklan lifestyle brand lokal berkualitas internasional dengan filosofi “Glowing is Confidence”, menghadirkan inovasi perpaduan sains modern dan natural extract.

🎨 **Gaya Visual**  
Gunakan mood & tone dari gambar referensi ini: https://yusarkhoiri.com/wp-content/uploads/2025/09/santai-scale-ai-image-1758254198971.png.
Nuansa clean, minimalis, elegan, Instagrammable, dengan palet warna pastel modern (putih, gold, rose pink), lighting lembut, kesan mewah tapi tetap natural.

📝 **Copywriting di Visual**  
- Headline: "Glowing is Confidence"  
- Subheadline: "Cerahkan kulitmu dengan RadiGlow Brightening Serum"  
- Call to Action: "Dapatkan Sekarang!"  
- Urgency: "Promo Terbatas – Hanya Hari Ini!"  

📐 **Aspect Ratio**: 1:1 (Instagram Feed)  
📷 **Jumlah Gambar**: 4 variasi desain  
  `,
        size: "1024x1024",
        n: 1,
    });

    const imageBase64 = result.data[0].b64_json;
    const buffer = Buffer.from(imageBase64, "base64");
    fs.writeFileSync("output-imggen.png", buffer);
}

generateWithReference();
