<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class BusinessIdeasController extends Controller
{
    public function suggest(Request $request)
    {
        $validated = $request->validate([
            'lat' => 'required|numeric',
            'lng' => 'required|numeric',
            'address' => 'nullable|string',
            'businessType' => 'nullable|string',
            'targetCustomers' => 'nullable|string',
            'estimatedCapital' => 'nullable|string',
            'businessScale' => 'nullable|string',
            'advantages' => 'nullable|string',
        ]);

        // Perpanjang eksekusi endpoint ini agar tidak mati di 60s saat menunggu OpenAI
        @\set_time_limit(120);

        $apiKey = env('OPENAI_API_KEY');
        if (!$apiKey) {
            return response()->json([
                'error' => 'OPENAI_API_KEY is not set in environment.'
            ], 500);
        }
        $model = env('OPENAI_LBA_MODEL', 'gpt-4o-mini');

        // System + Schema sama seperti business-analysis/lba.js
        $systemPrompt = <<<SYS
Analis bisnis lokasi. Jawab HANYA JSON sesuai skema. Skor 0–100.
Ide_bisnis: maksimal 1 ide, urut terbaik. Estimasi isi rentang IDR: biaya_awal_idr, balik_modal_bulan.

Wajib: ringkasan.temuan_kunci (5 poin):
- Jam sibuk: daftar rentang jam; tiap rentang berisi status (sibuk/sedang/rendah) dan alasan singkat
- Rentang usia
- Pekerjaan umum
- Gaya hidup (1 kalimat)
- Daya beli (kategori: menengah ke bawah/menengah/menengah ke atas)

Evidence: minimal 4/ide; sebut kata kunci jam, demografi, dan akses/visibilitas/lingkungan/kompetitor.

Estimasi detail (singkat, tanpa field baru):
- rincian_biaya_awal + total
- rincian_biaya_operasional_bulanan + total
- proyeksi_pendapatan {harian_idr, bulanan_idr, tahunan_idr, keterangan}
- bep_detail {asumsi_transaksi_harian, penjualan_bep_bulanan, pendapatan_bep_bulanan_idr, estimasi_waktu_bep_bulan, keterangan}

Strategi_akselerasi_profit: array {kategori, judul, keterangan}.

Ikuti skema JSON dengan ketat dan jangan menambah bidang di luar skema.
Gunakan bahasa sangat singkat: 1 kalimat per poin, tanpa penjelasan panjang.
SYS;

        // Bentuk payload user mengikuti lba.js
        $userPayload = [
            'koordinat' => [
                'lat' => (float)$validated['lat'],
                'lng' => (float)$validated['lng'],
            ],
            'lokasi_nama' => $validated['address'] ?? 'Lokasi terpilih',
            'radius_km' => 1,
            'preferensi' => [
                'modal' => $validated['estimatedCapital'] ? 'sedang' : 'sedang',
                'tipe_bisnis_diutamakan' => array_values(array_filter(array_map('trim', array_filter([$validated['businessType'] ?? ''])))),
            ],
            'batasan' => ['maks_ide' => 3, 'output_bahasa' => 'id'],
        ];

        // Skema JSON (disarikan dari lba.js)
        $schema = [
            'type' => 'object',
            'additionalProperties' => false,
            'required' => ['lokasi', 'ringkasan', 'ide_bisnis', 'rekomendasi_final'],
            'properties' => [
                'lokasi' => [
                    'type' => 'object',
                    'additionalProperties' => false,
                    'required' => ['nama', 'lat', 'lng', 'radius_km'],
                    'properties' => [
                        'nama' => ['type' => 'string'],
                        'lat' => ['type' => 'number'],
                        'lng' => ['type' => 'number'],
                        'radius_km' => ['type' => 'number'],
                    ],
                ],
                'ringkasan' => [
                    'type' => 'object',
                    'additionalProperties' => false,
                    'required' => ['karakter', 'temuan_kunci'],
                    'properties' => [
                        'karakter' => ['type' => 'string', 'maxLength' => 200],
                        'temuan_kunci' => ['type' => 'array', 'minItems' => 5, 'maxItems' => 5, 'items' => ['type' => 'string', 'maxLength' => 200]],

                    ],
                ],
                'ide_bisnis' => [
                    'type' => 'array',
                    'minItems' => 1,
                    'maxItems' => 1,
                    'items' => [
                        'type' => 'object',
                        'additionalProperties' => false,
                        'required' => ['nama', 'skor_total', 'skor_komponen', 'alasan', 'risiko', 'langkah_awal', 'estimasi', 'strategi_akselerasi_profit', 'evidence'],
                        'properties' => [
                            'nama' => ['type' => 'string', 'maxLength' => 80],
                            'skor_total' => ['type' => 'integer', 'minimum' => 0, 'maximum' => 100],
                            'skor_komponen' => [
                                'type' => 'object',
                                'additionalProperties' => false,
                                'required' => ['permintaan_lokal', 'aksesibilitas', 'daya_beli', 'kecocokan_lingkungan'],
                                'properties' => [
                                    'permintaan_lokal' => ['type' => 'integer', 'minimum' => 0, 'maximum' => 100],
                                    'aksesibilitas' => ['type' => 'integer', 'minimum' => 0, 'maximum' => 100],
                                    'daya_beli' => ['type' => 'integer', 'minimum' => 0, 'maximum' => 100],
                                    'kecocokan_lingkungan' => ['type' => 'integer', 'minimum' => 0, 'maximum' => 100],
                                ],
                            ],
                            'alasan' => ['type' => 'array', 'maxItems' => 4, 'items' => ['type' => 'string', 'maxLength' => 160]],
                            'risiko' => ['type' => 'array', 'maxItems' => 4, 'items' => ['type' => 'string', 'maxLength' => 160]],
                            'langkah_awal' => ['type' => 'array', 'maxItems' => 4, 'items' => ['type' => 'string', 'maxLength' => 160]],
                            'estimasi' => [
                                'type' => 'object',
                                'additionalProperties' => false,
                                'required' => [
                                    'biaya_awal_idr',
                                    'balik_modal_bulan',
                                    'rincian_biaya_awal',
                                    'total_biaya_awal_idr',
                                    'rincian_biaya_operasional_bulanan',
                                    'total_biaya_operasional_bulanan_idr',
                                    'proyeksi_pendapatan',
                                    'bep_detail'
                                ],
                                'properties' => [
                                    'biaya_awal_idr' => [
                                        'type' => 'object',
                                        'additionalProperties' => false,
                                        'required' => ['min', 'max'],
                                        'properties' => [
                                            'min' => ['type' => 'integer', 'minimum' => 0],
                                            'max' => ['type' => 'integer', 'minimum' => 0],
                                        ],
                                    ],
                                    'balik_modal_bulan' => [
                                        'type' => 'object',
                                        'additionalProperties' => false,
                                        'required' => ['min', 'max'],
                                        'properties' => [
                                            'min' => ['type' => 'integer', 'minimum' => 0],
                                            'max' => ['type' => 'integer', 'minimum' => 0],
                                        ],
                                    ],
                                    // rincian biaya awal (opsional terperinci)
                                    'rincian_biaya_awal' => [
                                        'type' => 'array',
                                        'maxItems' => 6,
                                        'items' => [
                                            'type' => 'object',
                                            'additionalProperties' => false,
                                            'required' => ['nama', 'qty', 'unit', 'harga_satuan_idr', 'total_idr', 'keterangan'],
                                            'properties' => [
                                                'nama' => ['type' => 'string'],
                                                'qty' => ['type' => ['number', 'null']],
                                                'unit' => ['type' => ['string', 'null']],
                                                'harga_satuan_idr' => ['type' => ['integer', 'null'], 'minimum' => 0],
                                                'total_idr' => ['type' => 'integer', 'minimum' => 0],
                                                'keterangan' => ['type' => ['string', 'null'], 'maxLength' => 120],
                                            ],
                                        ],
                                    ],
                                    'total_biaya_awal_idr' => ['type' => ['integer', 'null'], 'minimum' => 0],

                                    // rincian biaya operasional bulanan
                                    'rincian_biaya_operasional_bulanan' => [
                                        'type' => 'array',
                                        'maxItems' => 6,
                                        'items' => [
                                            'type' => 'object',
                                            'additionalProperties' => false,
                                            'required' => ['nama', 'biaya_idr', 'keterangan'],
                                            'properties' => [
                                                'nama' => ['type' => 'string'],
                                                'biaya_idr' => ['type' => 'integer', 'minimum' => 0],
                                                'keterangan' => ['type' => ['string', 'null'], 'maxLength' => 120],
                                            ],
                                        ],
                                    ],
                                    'total_biaya_operasional_bulanan_idr' => ['type' => ['integer', 'null'], 'minimum' => 0],

                                    // proyeksi pendapatan terperinci
                                    'proyeksi_pendapatan' => [
                                        'type' => 'object',
                                        'additionalProperties' => false,
                                        'required' => ['harian_idr', 'bulanan_idr', 'tahunan_idr', 'keterangan'],
                                        'properties' => [
                                            'harian_idr' => ['type' => ['integer', 'null'], 'minimum' => 0],
                                            'bulanan_idr' => ['type' => ['integer', 'null'], 'minimum' => 0],
                                            'tahunan_idr' => ['type' => ['integer', 'null'], 'minimum' => 0],
                                            'keterangan' => ['type' => ['string', 'null'], 'maxLength' => 160],
                                        ],
                                    ],

                                    // BEP terperinci
                                    'bep_detail' => [
                                        'type' => 'object',
                                        'additionalProperties' => false,
                                        'required' => ['asumsi_transaksi_harian', 'penjualan_bep_bulanan', 'pendapatan_bep_bulanan_idr', 'estimasi_waktu_bep_bulan', 'keterangan'],
                                        'properties' => [
                                            'asumsi_transaksi_harian' => ['type' => ['number', 'null']],
                                            'penjualan_bep_bulanan' => ['type' => ['integer', 'null'], 'minimum' => 0],
                                            'pendapatan_bep_bulanan_idr' => ['type' => ['integer', 'null'], 'minimum' => 0],
                                            'estimasi_waktu_bep_bulan' => ['type' => ['number', 'null'], 'minimum' => 0],
                                            'keterangan' => ['type' => ['string', 'null'], 'maxLength' => 160],
                                        ],
                                    ],
                                ],
                            ],
                            'strategi_akselerasi_profit' => [
                                'type' => ['array', 'null'],
                                'maxItems' => 4,
                                'items' => [
                                    'type' => 'object',
                                    'additionalProperties' => false,
                                    'required' => ['kategori', 'judul', 'keterangan'],
                                    'properties' => [
                                        'kategori' => ['type' => 'string'],
                                        'judul' => ['type' => 'string', 'maxLength' => 80],
                                        'keterangan' => ['type' => 'string', 'maxLength' => 160],
                                    ],
                                ],
                            ],

                            'evidence' => ['type' => 'array', 'minItems' => 4, 'maxItems' => 4, 'items' => ['type' => 'string', 'maxLength' => 160]],
                        ],
                    ],
                ],
                'rekomendasi_final' => [
                    'type' => 'object',
                    'additionalProperties' => false,
                    'required' => ['pilihan', 'alasan', 'metrik_keberhasilan_90_hari'],
                    'properties' => [
                        'pilihan' => ['type' => 'string'],
                        'alasan' => ['type' => 'array', 'items' => ['type' => 'string']],
                        'metrik_keberhasilan_90_hari' => ['type' => 'array', 'items' => ['type' => 'string']],
                    ],
                ],
            ],
        ];

        try {
            $response = Http::withToken($apiKey)
                ->timeout(45)
                ->connectTimeout(12)
                ->post('https://api.openai.com/v1/responses', [
                    'model' => $model,
                    'max_output_tokens' => 1300,
                    'input' => [
                        ['role' => 'system', 'content' => $systemPrompt],
                        ['role' => 'user', 'content' => json_encode($userPayload, JSON_UNESCAPED_UNICODE)],
                    ],
                    'text' => [
                        'format' => [
                            'type' => 'json_schema',
                            'name' => 'lba_schema',
                            'strict' => true,
                            'schema' => $schema,
                        ],
                    ],
                ]);

            if (!$response->ok()) {
                $fallback = $this->fallbackFromFast($validated, $apiKey);
                if ($fallback) {
                    return response()->json($fallback);
                }
                return response()->json([
                    'error' => 'OpenAI request failed',
                    'details' => $response->json(),
                ], 502);
            }

            $respJson = $response->json();
            $content = data_get($respJson, 'output_text');
            if (!$content) {
                $content = data_get($respJson, 'output.0.content.0.text');
            }
            if (!$content) {
                $fallback = $this->fallbackFromFast($validated, $apiKey);
                if ($fallback) {
                    return response()->json($fallback);
                }
                return response()->json(['error' => 'Empty content from OpenAI'], 502);
            }

            $json = json_decode($content, true);
            if (!is_array($json)) {
                $fallback = $this->fallbackFromFast($validated, $apiKey);
                if ($fallback) {
                    return response()->json($fallback);
                }
                return response()->json(['error' => 'Invalid JSON returned by OpenAI', 'raw' => $content], 502);
            }

            return response()->json($json);
        } catch (\Throwable $e) {
            $fallback = $this->fallbackFromFast($validated, $apiKey);
            if ($fallback) {
                return response()->json($fallback);
            }
            return response()->json([
                'error' => 'Exception while calling OpenAI',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function suggestFast(Request $request)
    {
        $validated = $request->validate([
            'lat' => 'required|numeric',
            'lng' => 'required|numeric',
            'address' => 'nullable|string',
            'businessType' => 'nullable|string',
        ]);

        $apiKey = env('OPENAI_API_KEY');
        if (!$apiKey) {
            return response()->json([
                'error' => 'OPENAI_API_KEY is not set in environment.'
            ], 500);
        }

        $userPayload = [
            'koordinat' => [
                'lat' => (float)$validated['lat'],
                'lng' => (float)$validated['lng'],
            ],
            'lokasi_nama' => $validated['address'] ?? 'Lokasi terpilih',
            'radius_km' => 1,
            'preferensi' => [
                'tipe_bisnis_diutamakan' => array_values(array_filter(array_map('trim', array_filter([$validated['businessType'] ?? ''])))),
            ],
            'batasan' => ['maks_ide' => 3, 'output_bahasa' => 'id'],
        ];

        $system = <<<SYS
Anda analis bisnis berbasis lokasi. Balas HANYA dalam JSON (tanpa teks lain) dengan format:
{
  "ide_bisnis": [
    { "nama": string, "keterangan": string }
  ]
}
Ketentuan:
- Maksimal 3 ide, urutkan dari yang paling menjanjikan.
- Bahasa Indonesia, singkat dan informatif.
- Jangan sertakan skor, angka, atau bidang lain.
- "keterangan" berisi ringkasan alasan (1–2 kalimat) mengapa ide cocok untuk lokasi tersebut.
Jangan keluar dari JSON.
SYS;

        try {
            $response = Http::withToken($apiKey)
                ->timeout(45)
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => 'gpt-4o-mini',
                    'messages' => [
                        ['role' => 'system', 'content' => $system],
                        ['role' => 'user', 'content' => json_encode($userPayload, JSON_UNESCAPED_UNICODE)],
                    ],
                    'temperature' => 0.3,
                    'max_tokens' => 900,
                    'response_format' => ['type' => 'json_object'],
                ]);

            if (!$response->ok()) {
                return response()->json([
                    'error' => 'OpenAI request failed',
                    'details' => $response->json(),
                ], 502);
            }

            $content = data_get($response->json(), 'choices.0.message.content');
            if (!$content) {
                return response()->json(['error' => 'Empty content from OpenAI'], 502);
            }

            $json = json_decode($content, true);
            if (!is_array($json)) {
                return response()->json(['error' => 'Invalid JSON returned by OpenAI', 'raw' => $content], 502);
            }

            return response()->json($json);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Exception while calling OpenAI',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    private function fallbackFromFast(array $validated, string $apiKey)
    {
        try {
            $userPayload = [
                'koordinat' => [
                    'lat' => (float)$validated['lat'],
                    'lng' => (float)$validated['lng'],
                ],
                'lokasi_nama' => $validated['address'] ?? 'Lokasi terpilih',
                'radius_km' => 1,
                'preferensi' => [
                    'tipe_bisnis_diutamakan' => array_values(array_filter(array_map('trim', array_filter([$validated['businessType'] ?? ''])))),
                ],
                'batasan' => ['maks_ide' => 3, 'output_bahasa' => 'id'],
            ];

            $system = <<<SYS
Anda analis bisnis berbasis lokasi. Balas HANYA dalam JSON (tanpa teks lain) dengan format:
{
  "ide_bisnis": [
    { "nama": string, "keterangan": string }
  ]
}
Ketentuan:
- Maksimal 3 ide, urutkan dari yang paling menjanjikan.
- Bahasa Indonesia, sangat singkat (maks 1–2 kalimat di keterangan).
- Jangan sertakan bidang lain.
Jangan keluar dari JSON.
SYS;

            $resp = \Illuminate\Support\Facades\Http::withToken($apiKey)
                ->timeout(20)
                ->post('https://api.openai.com/v1/chat/completions', [
                    'model' => 'gpt-4o-mini',
                    'messages' => [
                        ['role' => 'system', 'content' => $system],
                        ['role' => 'user', 'content' => json_encode($userPayload, JSON_UNESCAPED_UNICODE)],
                    ],
                    'temperature' => 0.2,
                    'max_tokens' => 600,
                    'response_format' => ['type' => 'json_object'],
                ]);

            if (!$resp->ok()) {
                return null;
            }
            $content = data_get($resp->json(), 'choices.0.message.content');
            if (!$content) {
                return null;
            }
            $fast = json_decode($content, true);
            if (!is_array($fast)) {
                return null;
            }
            $first = $fast['ide_bisnis'][0] ?? null;
            $nama = is_array($first) ? ($first['nama'] ?? 'Ide Terbaik') : 'Ide Terbaik';
            $ket = is_array($first) ? ($first['keterangan'] ?? '') : '';

            // buat array alasan singkat dari keterangan
            $alasanArr = array_values(array_filter(array_map('trim', array_slice(preg_split('/[.!?]/', (string)$ket), 0, 2))));
            if (empty($alasanArr)) {
                $alasanArr = ['Cocok dengan karakter lokasi.'];
            }

            $out = [
                'lokasi' => [
                    'nama' => $validated['address'] ?? 'Lokasi terpilih',
                    'lat' => (float)$validated['lat'],
                    'lng' => (float)$validated['lng'],
                    'radius_km' => 1,
                ],
                'ringkasan' => [
                    'karakter' => 'Ringkasan cepat (fallback).',
                    'temuan_kunci' => [
                        'Jam sibuk: 06:00-09:00 (sedang)',
                        'Rentang usia: 18-45',
                        'Pekerjaan umum: karyawan/mahasiswa',
                        'Gaya hidup: mobilitas tinggi',
                        'Daya beli: menengah',
                    ],
                ],
                'ide_bisnis' => [[
                    'nama' => $nama,
                    'skor_total' => 72,
                    'skor_komponen' => [
                        'permintaan_lokal' => 75,
                        'aksesibilitas' => 70,
                        'daya_beli' => 70,
                        'kecocokan_lingkungan' => 72,
                    ],
                    'alasan' => array_slice($alasanArr, 0, 4),
                    'risiko' => [],
                    'langkah_awal' => [],
                    'estimasi' => [
                        'biaya_awal_idr' => ['min' => 3000000, 'max' => 8000000],
                        'balik_modal_bulan' => ['min' => 3, 'max' => 6],
                        'rincian_biaya_awal' => [
                            ['nama' => 'Peralatan dasar', 'qty' => 1, 'unit' => null, 'harga_satuan_idr' => 3000000, 'total_idr' => 3000000, 'keterangan' => null],
                            ['nama' => 'Renovasi ringan', 'qty' => 1, 'unit' => null, 'harga_satuan_idr' => 2000000, 'total_idr' => 2000000, 'keterangan' => null],
                            ['nama' => 'Perizinan & lain-lain', 'qty' => 1, 'unit' => null, 'harga_satuan_idr' => 1000000, 'total_idr' => 1000000, 'keterangan' => null],
                        ],
                        'total_biaya_awal_idr' => 6000000,
                        'rincian_biaya_operasional_bulanan' => [
                            ['nama' => 'Sewa tempat', 'biaya_idr' => 1500000, 'keterangan' => null],
                            ['nama' => 'Utilitas', 'biaya_idr' => 500000, 'keterangan' => null],
                            ['nama' => 'Bahan habis pakai', 'biaya_idr' => 1000000, 'keterangan' => null],
                        ],
                        'total_biaya_operasional_bulanan_idr' => 3000000,
                        'proyeksi_pendapatan' => [
                            'harian_idr' => 300000,
                            'bulanan_idr' => 9000000,
                            'tahunan_idr' => 108000000,
                            'keterangan' => 'Perkiraan awal konservatif.',
                        ],
                        'bep_detail' => [
                            'asumsi_transaksi_harian' => 20,
                            'penjualan_bep_bulanan' => 300,
                            'pendapatan_bep_bulanan_idr' => 9000000,
                            'estimasi_waktu_bep_bulan' => 4,
                            'keterangan' => 'Dengan margin positif setelah biaya operasional.',
                        ],
                    ],
                    'strategi_akselerasi_profit' => [],
                    'evidence' => [],
                ]],
                'rekomendasi_final' => [
                    'pilihan' => $nama,
                    'alasan' => ['Rekomendasi cepat berdasarkan hasil fast.'],
                    'metrik_keberhasilan_90_hari' => ['Transaksi harian', 'Omzet bulanan', 'Kepuasan pelanggan'],
                ],
            ];

            return $out;
        } catch (\Throwable) {
            return null;
        }
    }

    public function competitors(Request $request)
    {
        $validated = $request->validate([
            'lat' => 'required|numeric',
            'lng' => 'required|numeric',
            'radius_km' => 'nullable|numeric',
            'query' => 'required|string',
        ]);

        $apiKey = env('GOOGLE_MAPS_API_KEY');
        if (!$apiKey) {
            return response()->json([
                'error' => 'GOOGLE_MAPS_API_KEY is not set in environment.'
            ], 501);
        }

        $lat = (float)$validated['lat'];
        $lng = (float)$validated['lng'];
        $radiusKm = isset($validated['radius_km']) ? (float)$validated['radius_km'] : 1.0;
        $radiusM = (int)max(100, min(50000, $radiusKm * 1000));
        $query = $validated['query'];

        try {
            $resp = Http::get('https://maps.googleapis.com/maps/api/place/nearbysearch/json', [
                'location' => $lat . ',' . $lng,
                'radius' => $radiusM,
                'keyword' => $query,
                'key' => $apiKey,
                'language' => 'id',
            ]);

            if (!$resp->ok()) {
                return response()->json([
                    'error' => 'Failed to query Google Places',
                    'details' => $resp->json(),
                ], 502);
            }
            $data = $resp->json();
            $results = $data['results'] ?? [];
            $items = array_map(function ($r) {
                return [
                    'name' => $r['name'] ?? null,
                    'address' => $r['vicinity'] ?? ($r['formatted_address'] ?? null),
                    'rating' => $r['rating'] ?? null,
                    'user_ratings_total' => $r['user_ratings_total'] ?? null,
                    'place_id' => $r['place_id'] ?? null,
                    'location' => [
                        'lat' => $r['geometry']['location']['lat'] ?? null,
                        'lng' => $r['geometry']['location']['lng'] ?? null,
                    ],
                    'open_now' => $r['opening_hours']['open_now'] ?? null,
                    'types' => $r['types'] ?? [],
                ];
            }, $results);

            return response()->json(['items' => $items, 'source' => 'google_places']);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Exception while calling Google Places',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
