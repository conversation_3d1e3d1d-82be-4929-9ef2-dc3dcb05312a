<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class VoiceGeneratorController extends Controller
{
    /**
     * Generate voice over (script + audio)
     */
    public function generateVoice(Request $request)
    {
        try {
            $validated = $request->validate([
                'product' => 'required|string|max:1000',
                'purpose' => 'nullable|string',
                'tone' => 'nullable|string',
                'language' => 'nullable|string',
                'gender' => 'nullable|string',
                'url' => 'nullable|url|max:500',
            ]);

            $product = $validated['product'];
            $purpose = $validated['purpose'] ?? 'promo';
            $tone = $validated['tone'] ?? 'friendly';
            $language = $validated['language'] ?? 'id-ID';
            $gender = $validated['gender'] ?? 'female';
            $url = $validated['url'] ?? null;

            // Step 1: Generate script using Vertex AI Gemini
            $script = $this->generateScript($product, $purpose, $tone, $url);

            if (!$script) {
                throw new \Exception('Failed to generate script');
            }

            // Step 2: Convert script to audio using Google Text-to-Speech
            $audioUrl = $this->textToSpeech($script, $language, $gender);

            if (!$audioUrl) {
                throw new \Exception('Failed to generate audio');
            }

            return response()->json([
                'script' => $script,
                'audioUrl' => $audioUrl,
            ]);
        } catch (\Exception $e) {
            Log::error('Voice generation error: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to generate voice over',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate script using Vertex AI Gemini
     */
    private function generateScript($product, $purpose, $tone, $url = null)
    {
        try {
            // Get Vertex AI credentials
            $projectId = env('VERTEX_AI_PROJECT_ID');
            $location = env('VERTEX_AI_LOCATION', 'us-central1');
            $model = env('VERTEX_AI_MODEL', 'gemini-1.5-flash');

            if (!$projectId) {
                throw new \Exception('VERTEX_AI_PROJECT_ID not configured');
            }

            // Build prompt
            $urlInstruction = '';
            if ($url) {
                $urlInstruction = "\n\nPENTING: Kunjungi dan pelajari website berikut untuk memahami brand voice, tone, dan style mereka:\nWebsite: {$url}\n\nGunakan insight dari website tersebut untuk membuat narasi yang lebih relevan dan kontekstual.";
            }

            $prompt = "Buatkan narasi voice over untuk {$purpose} dengan tone {$tone}.

Produk/Layanan: {$product}
{$urlInstruction}

Buatlah narasi yang:
- Durasi sekitar 30-60 detik (150-250 kata)
- Opening yang menarik perhatian dengan pertanyaan atau statement kuat
- Gunakan kalimat pendek dan jelas (mudah dibacakan)
- Variasi panjang kalimat untuk ritme yang natural
- Gunakan tanda tanya (?) untuk pertanyaan retoris
- Gunakan tanda seru (!) untuk emphasis dan excitement
- Menjelaskan value proposition dengan jelas
- Bahasa conversational seperti berbicara langsung ke audience
- Call-to-action yang compelling di akhir
- Tone {$tone}
- Cocok untuk dibacakan dengan intonasi yang ekspresif

TIPS untuk intonasi natural:
- Mulai dengan pertanyaan atau greeting yang engaging
- Gunakan kata-kata emosional yang memicu intonasi
- Variasi struktur kalimat (pendek-panjang-pendek)
- Akhiri dengan CTA yang energik

PENTING: Buat narasi yang LENGKAP dan siap dibacakan. Jangan include stage directions atau notes.

Format output dalam JSON:
{
  \"script\": \"narasi voice over lengkap yang siap dibacakan\"
}";

            // Get access token
            $accessToken = $this->getAccessToken();

            if (!$accessToken) {
                throw new \Exception('Failed to get Vertex AI access token');
            }

            // Call Vertex AI API
            $endpoint = "https://{$location}-aiplatform.googleapis.com/v1/projects/{$projectId}/locations/{$location}/publishers/google/models/{$model}:generateContent";

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ])->post($endpoint, [
                'contents' => [
                    [
                        'role' => 'user',
                        'parts' => [
                            ['text' => $prompt]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.7,
                    'topP' => 0.95,
                    'topK' => 40,
                    'maxOutputTokens' => 8192,
                ],
            ]);

            if (!$response->successful()) {
                Log::error('Vertex AI API request failed: ' . $response->body());
                throw new \Exception('Vertex AI API request failed: ' . $response->body());
            }

            $data = $response->json();

            // Extract text from response
            $text = $data['candidates'][0]['content']['parts'][0]['text'] ?? '';

            if (empty($text)) {
                throw new \Exception('Empty response from Vertex AI');
            }

            // Parse JSON response
            $result = $this->parseScriptResponse($text);

            return $result;
        } catch (\Exception $e) {
            Log::error('Script generation error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Convert text to speech using Gemini 2.5 Pro Preview TTS
     */
    private function textToSpeech($text, $language, $gender)
    {
        try {
            // Get API key
            $apiKey = env('GEMINI_API_KEY');
            if (!$apiKey) {
                throw new \Exception('GEMINI_API_KEY not configured');
            }

            // Determine voice name based on language and gender
            $voiceName = $this->getVoiceName($language, $gender);

            // Call Gemini 2.5 Pro Preview TTS API
            // Using generativelanguage.googleapis.com endpoint (AI Studio API)
            $endpoint = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro-preview-tts:generateContent?key={$apiKey}";

            $response = Http::timeout(120)->withOptions([
                'stream' => false,
                'verify' => false,
            ])->withHeaders([
                'Content-Type' => 'application/json',
            ])->post($endpoint, [
                'contents' => [
                    [
                        'role' => 'user',
                        'parts' => [
                            [
                                'text' => $text
                            ]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 1,
                    'responseModalities' => ['audio'],
                    'speechConfig' => [
                        'voiceConfig' => [
                            'prebuiltVoiceConfig' => [
                                'voiceName' => $voiceName
                            ]
                        ]
                    ]
                ]
            ]);

            if (!$response->successful()) {
                $errorBody = $response->body();
                $statusCode = $response->status();
                Log::error('Gemini TTS API request failed', [
                    'status' => $statusCode,
                    'body' => $errorBody,
                    'endpoint' => $endpoint,
                ]);
                throw new \Exception("Gemini TTS API request failed (HTTP {$statusCode}): {$errorBody}");
            }

            $data = $response->json();
            Log::info('Gemini TTS API response received', [
                'has_candidates' => isset($data['candidates']),
                'response_keys' => array_keys($data),
            ]);

            // Extract audio data from response
            $audioData = null;
            $mimeType = null;

            if (isset($data['candidates'][0]['content']['parts'])) {
                foreach ($data['candidates'][0]['content']['parts'] as $part) {
                    if (isset($part['inlineData']['data'])) {
                        $audioData = $part['inlineData']['data'];
                        $mimeType = $part['inlineData']['mimeType'] ?? 'audio/wav';
                        break;
                    }
                }
            }

            if (!$audioData) {
                throw new \Exception('No audio data in response');
            }

            // Decode base64 audio data
            $decodedAudio = base64_decode($audioData);

            // Convert to WAV if needed
            if (str_contains($mimeType, 'L16') || str_contains($mimeType, 'pcm')) {
                $decodedAudio = $this->convertToWav($decodedAudio, $mimeType);
                $extension = 'wav';
            } else {
                $extension = 'mp3';
            }

            // Save audio file
            $filename = 'voice-over-' . time() . '.' . $extension;
            $path = 'voice-overs/' . $filename;

            Storage::disk('public')->put($path, $decodedAudio);

            // Return public URL
            $audioUrl = url('storage/' . $path);

            Log::info("Voice over generated successfully with Gemini TTS: {$audioUrl}");

            return $audioUrl;
        } catch (\Exception $e) {
            Log::error('Gemini TTS error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get voice name based on language and gender
     * Using Gemini 2.5 Pro Preview TTS prebuilt voices
     */
    private function getVoiceName($language, $gender)
    {
        // Gemini TTS prebuilt voices
        // Available voices: Puck, Charon, Kore, Fenrir, Aoede, Zephyr
        $voices = [
            'id-ID' => [
                'female' => 'Aoede',  // Warm, friendly female voice
                'male' => 'Fenrir',   // Strong, confident male voice
            ],
            'en-US' => [
                'female' => 'Aoede',  // Warm, friendly female voice
                'male' => 'Puck',     // Energetic male voice
            ],
        ];

        return $voices[$language][$gender] ?? 'Aoede';
    }

    /**
     * Convert raw PCM audio to WAV format
     */
    private function convertToWav($audioData, $mimeType)
    {
        // Parse audio parameters from mime type
        $params = $this->parseAudioMimeType($mimeType);
        $bitsPerSample = $params['bits_per_sample'];
        $sampleRate = $params['rate'];
        $numChannels = 1;
        $dataSize = strlen($audioData);
        $bytesPerSample = $bitsPerSample / 8;
        $blockAlign = $numChannels * $bytesPerSample;
        $byteRate = $sampleRate * $blockAlign;
        $chunkSize = 36 + $dataSize;

        // Build WAV header
        $header = pack(
            'a4Va4a4VvvVVvva4V',
            'RIFF',           // ChunkID
            $chunkSize,       // ChunkSize
            'WAVE',           // Format
            'fmt ',           // Subchunk1ID
            16,               // Subchunk1Size (16 for PCM)
            1,                // AudioFormat (1 for PCM)
            $numChannels,     // NumChannels
            $sampleRate,      // SampleRate
            $byteRate,        // ByteRate
            $blockAlign,      // BlockAlign
            $bitsPerSample,   // BitsPerSample
            'data',           // Subchunk2ID
            $dataSize         // Subchunk2Size
        );

        return $header . $audioData;
    }

    /**
     * Parse audio MIME type to extract parameters
     */
    private function parseAudioMimeType($mimeType)
    {
        $bitsPerSample = 16;
        $rate = 24000;

        // Extract rate from parameters
        $parts = explode(';', $mimeType);
        foreach ($parts as $param) {
            $param = trim($param);
            if (stripos($param, 'rate=') === 0) {
                $rateStr = substr($param, 5);
                $rate = intval($rateStr);
            } elseif (stripos($param, 'audio/L') === 0) {
                $bitsStr = substr($param, 7);
                $bitsPerSample = intval($bitsStr);
            }
        }

        return [
            'bits_per_sample' => $bitsPerSample,
            'rate' => $rate
        ];
    }



    /**
     * Get access token for Vertex AI
     */
    private function getAccessToken()
    {
        // Try to get from environment first (for manual token)
        $token = env('VERTEX_AI_ACCESS_TOKEN');

        if ($token) {
            return $token;
        }

        // Try using service account key file (RECOMMENDED)
        $keyFile = env('GOOGLE_APPLICATION_CREDENTIALS');
        if ($keyFile && file_exists($keyFile)) {
            try {
                $client = new \Google\Client();
                $client->setAuthConfig($keyFile);
                $client->addScope('https://www.googleapis.com/auth/cloud-platform');
                $client->useApplicationDefaultCredentials();

                $token = $client->fetchAccessTokenWithAssertion();

                if (isset($token['access_token'])) {
                    return $token['access_token'];
                }
            } catch (\Exception $e) {
                Log::error('Failed to get token from service account: ' . $e->getMessage());
            }
        }

        // Fallback to gcloud command (for development)
        try {
            $output = shell_exec('gcloud auth print-access-token 2>&1');
            $token = trim($output);

            if (!empty($token) && !str_contains($token, 'ERROR')) {
                return $token;
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get access token via gcloud: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Parse script response from AI
     */
    private function parseScriptResponse($rawResponse)
    {
        // Clean response - remove markdown code blocks if present
        $cleanResponse = preg_replace('/```json\s*|\s*```/', '', $rawResponse);
        $cleanResponse = trim($cleanResponse);

        // Try to extract JSON from response
        $jsonMatch = [];
        if (preg_match('/\{[\s\S]*\}/', $cleanResponse, $jsonMatch)) {
            $jsonStr = $jsonMatch[0];

            $decoded = json_decode($jsonStr, true);

            if (json_last_error() === JSON_ERROR_NONE && isset($decoded['script'])) {
                return $decoded['script'];
            }
        }

        // Fallback: return raw response
        return $cleanResponse;
    }
}
