<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionPackage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class SubscriptionPackageController extends Controller
{
    /**
     * Display a listing of subscription packages
     */
    public function index(Request $request)
    {
        try {
            // Check if user is admin
            if (!Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }

            $query = SubscriptionPackage::query();

            // Search by name
            if ($request->has('search') && $request->search !== '') {
                $search = $request->search;
                $query->where('name', 'like', "%{$search}%");
            }

            // Filter by status if specified
            if ($request->has('is_active') && $request->is_active !== '') {
                $query->where('is_active', $request->is_active === 'true');
            }

            $packages = $query->ordered()->get();

            return response()->json([
                'success' => true,
                'packages' => $packages
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch subscription packages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created subscription package
     */
    public function store(Request $request)
    {
        try {
            // Check if user is admin
            if (!Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:subscription_packages,name',
                'slug' => 'nullable|string|max:255|unique:subscription_packages,slug',
                'description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'duration_months' => 'required|integer|min:1',
                'duration_label' => 'required|string|max:255',
                'features' => 'nullable|array',
                'badge' => 'nullable|string|max:255',
                'color' => 'required|string|max:255',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $package = SubscriptionPackage::create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Subscription package created successfully',
                'package' => $package
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create subscription package',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified subscription package
     */
    public function show($id)
    {
        try {
            // Check if user is admin
            if (!Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }

            $package = SubscriptionPackage::find($id);

            if (!$package) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription package not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'package' => $package
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch subscription package',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified subscription package
     */
    public function update(Request $request, $id)
    {
        try {
            // Check if user is admin
            if (!Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }

            $package = SubscriptionPackage::find($id);

            if (!$package) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription package not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:subscription_packages,name,' . $id,
                'slug' => 'nullable|string|max:255|unique:subscription_packages,slug,' . $id,
                'description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'duration_months' => 'required|integer|min:1',
                'duration_label' => 'required|string|max:255',
                'features' => 'nullable|array',
                'badge' => 'nullable|string|max:255',
                'color' => 'required|string|max:255',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $package->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Subscription package updated successfully',
                'package' => $package->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update subscription package',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified subscription package
     */
    public function destroy($id)
    {
        try {
            // Check if user is admin
            if (!Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }

            $package = SubscriptionPackage::find($id);

            if (!$package) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription package not found'
                ], 404);
            }

            $package->delete();

            return response()->json([
                'success' => true,
                'message' => 'Subscription package deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete subscription package',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle subscription package status (active/inactive)
     */
    public function toggleStatus($id)
    {
        try {
            // Check if user is admin
            if (!Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }

            $package = SubscriptionPackage::find($id);

            if (!$package) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription package not found'
                ], 404);
            }

            $package->is_active = !$package->is_active;
            $package->save();

            return response()->json([
                'success' => true,
                'message' => 'Subscription package status updated successfully',
                'package' => $package
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update subscription package status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get active subscription packages for public use
     */
    public function getActive()
    {
        try {
            $packages = SubscriptionPackage::active()->ordered()->get();

            return response()->json([
                'success' => true,
                'packages' => $packages
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch active subscription packages',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
