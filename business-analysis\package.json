{"name": "lba-analyzer", "version": "1.0.0", "description": "Location-Based Business Analysis with OpenAI Structured Outputs (Node.js)", "main": "lba.js", "type": "module", "scripts": {"start": "node lba.js --lat -7.9543379 --lng 112.5779014 --name \"Karangwidoro, Dau, Kab. <PERSON>\" --radius 1 --modal sedang --prefer ritel,kuliner,jasa", "dev": "node lba.js"}, "keywords": ["openai", "business-analysis", "location-based", "json", "malang"], "author": "Your Name", "license": "MIT", "dependencies": {"ajv": "^8.17.1", "dotenv": "^16.4.5", "formdata-node": "^6.0.3", "openai": "^5.22.0", "sharp": "^0.34.4"}}