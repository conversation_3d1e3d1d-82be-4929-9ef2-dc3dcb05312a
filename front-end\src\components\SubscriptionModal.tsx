import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  X,
  Check,
  Star,
  Package,
  CreditCard,
  Clock,
  DollarSign,
  Loader2,
  AlertCircle,
  FileText
} from 'lucide-react';

const API_BASE = import.meta.env.VITE_API_BASE || 'http://127.0.0.1:8000';

interface SubscriptionPackage {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  price: number;
  duration_months: number;
  duration_label: string;
  features: string[] | null;
  badge: string | null;
  color: string;
  is_active: boolean;
  sort_order: number;
}

interface PaymentMethod {
  id: number;
  name: string;
  type: string;
  description: string | null;
  icon: string | null;
  is_active: boolean;
}

interface SubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  lockedFeature?: string;
  onInvoiceCreated?: (invoiceId: number) => void;
}

const SubscriptionModal: React.FC<SubscriptionModalProps> = ({ isOpen, onClose, lockedFeature, onInvoiceCreated }) => {
  const { user } = useAuth();
  const [packages, setPackages] = useState<SubscriptionPackage[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPackage, setSelectedPackage] = useState<SubscriptionPackage | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [step, setStep] = useState<'packages' | 'payment' | 'confirmation'>('packages');
  const [error, setError] = useState('');
  const [createdInvoice, setCreatedInvoice] = useState<any>(null);

  // Helper function to get auth headers
  const getAuthHeaders = () => {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      throw new Error('No authentication token found');
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    };
  };

  const fetchData = async () => {
    try {
      setLoading(true);

      const [packagesResponse, paymentMethodsResponse] = await Promise.all([
        fetch(`${API_BASE}/api/subscription-packages/active`),
        fetch(`${API_BASE}/api/payment-methods/active`)
      ]);

      const [packagesData, paymentMethodsData] = await Promise.all([
        packagesResponse.json(),
        paymentMethodsResponse.json()
      ]);

      if (packagesData.success) {
        setPackages(packagesData.packages);
      }

      if (paymentMethodsData.success) {
        setPaymentMethods(paymentMethodsData.payment_methods);
      }

    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to load subscription data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateInvoice = async () => {
    if (!selectedPackage || !selectedPaymentMethod) return;

    try {
      setProcessing(true);
      setError('');

      const response = await fetch(`${API_BASE}/api/invoices/subscription`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          subscription_package_id: selectedPackage.id,
          payment_method_id: selectedPaymentMethod.id,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setCreatedInvoice(data.invoice);
        setStep('confirmation');
      } else {
        setError(data.message || 'Failed to create invoice');
      }

    } catch (error) {
      console.error('Error creating invoice:', error);
      setError('Failed to create invoice');
    } finally {
      setProcessing(false);
    }
  };

  const getColorBadgeClass = (color: string) => {
    switch (color) {
      case 'blue': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'emerald': return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'purple': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'red': return 'bg-red-100 text-red-800 border-red-200';
      case 'yellow': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getFeatureName = (featureId: string) => {
    const featureNames: { [key: string]: string } = {
      'wa-testimonial-generator': 'WA Testimonial Generator',
      'ads-image-generator': 'Ads Image Generator',
      'marketing-content-generator': 'Marketing Content Generator',
      'peta-cuan': 'Peta Cuan Lokasi',
      'kalkulator-hpp': 'Kalkulator HPP Otomatis'
    };
    return featureNames[featureId] || featureId;
  };

  useEffect(() => {
    if (isOpen) {
      fetchData();
      setStep('packages');
      setSelectedPackage(null);
      setSelectedPaymentMethod(null);
      setCreatedInvoice(null);
      setError('');
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Berlangganan SantuyGrow</h2>
              {lockedFeature && (
                <p className="text-gray-600 mt-1">
                  Untuk mengakses <span className="font-medium">{getFeatureName(lockedFeature)}</span>, silakan pilih paket berlangganan
                </p>
              )}
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
                <span className="text-red-700">{error}</span>
              </div>
            </div>
          )}

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-6 h-6 animate-spin text-emerald-600" />
              <span className="ml-2 text-gray-600">Loading packages...</span>
            </div>
          ) : step === 'packages' ? (
            <>
              {/* Package Selection */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Pilih Paket Berlangganan</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {packages.map((pkg) => (
                    <div
                      key={pkg.id}
                      onClick={() => setSelectedPackage(pkg)}
                      className={`relative border-2 rounded-lg p-6 cursor-pointer transition-all ${selectedPackage?.id === pkg.id
                        ? 'border-emerald-500 bg-emerald-50'
                        : 'border-gray-200 hover:border-gray-300'
                        }`}
                    >
                      {pkg.badge && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <span className="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-medium flex items-center">
                            <Star className="w-3 h-3 mr-1" />
                            {pkg.badge}
                          </span>
                        </div>
                      )}

                      <div className="text-center">
                        <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 ${getColorBadgeClass(pkg.color)}`}>
                          <Package className="w-6 h-6" />
                        </div>

                        <h4 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h4>
                        <p className="text-gray-600 text-sm mb-4">{pkg.description}</p>

                        <div className="mb-4">
                          <div className="text-3xl font-bold text-gray-900 flex items-center justify-center">
                            <DollarSign className="w-6 h-6 text-gray-400 mr-1" />
                            {formatPrice(pkg.price)}
                          </div>
                          <div className="text-gray-500 text-sm flex items-center justify-center mt-1">
                            <Clock className="w-4 h-4 mr-1" />
                            {pkg.duration_label}
                          </div>
                        </div>

                        {pkg.features && pkg.features.length > 0 && (
                          <div className="text-left">
                            <h5 className="font-medium text-gray-900 mb-2">Fitur yang didapat:</h5>
                            <ul className="space-y-1">
                              {pkg.features.map((feature, index) => (
                                <li key={index} className="flex items-start text-sm text-gray-600">
                                  <Check className="w-4 h-4 text-emerald-500 mr-2 mt-0.5 flex-shrink-0" />
                                  {feature}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>

                      {selectedPackage?.id === pkg.id && (
                        <div className="absolute top-4 right-4">
                          <div className="bg-emerald-500 text-white rounded-full p-1">
                            <Check className="w-4 h-4" />
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Next Button */}
              <div className="flex justify-end">
                <button
                  onClick={() => setStep('payment')}
                  disabled={!selectedPackage}
                  className="px-6 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  Lanjutkan ke Pembayaran
                </button>
              </div>
            </>
          ) : step === 'payment' ? (
            <>
              {/* Payment Method Selection */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Pilih Metode Pembayaran</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {paymentMethods.map((method) => (
                    <div
                      key={method.id}
                      onClick={() => setSelectedPaymentMethod(method)}
                      className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${selectedPaymentMethod?.id === method.id
                        ? 'border-emerald-500 bg-emerald-50'
                        : 'border-gray-200 hover:border-gray-300'
                        }`}
                    >
                      <div className="flex items-center">
                        <div className="bg-gray-100 rounded-lg p-3 mr-4">
                          <CreditCard className="w-6 h-6 text-gray-600" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{method.name}</h4>
                          <p className="text-gray-600 text-sm">{method.description}</p>
                        </div>
                        {selectedPaymentMethod?.id === method.id && (
                          <div className="bg-emerald-500 text-white rounded-full p-1">
                            <Check className="w-4 h-4" />
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Order Summary */}
              {selectedPackage && (
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <h4 className="font-medium text-gray-900 mb-2">Ringkasan Pesanan</h4>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">{selectedPackage.name} - {selectedPackage.duration_label}</span>
                    <span className="font-medium text-gray-900">{formatPrice(selectedPackage.price)}</span>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-between">
                <button
                  onClick={() => setStep('packages')}
                  className="px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Kembali
                </button>
                <button
                  onClick={handleCreateInvoice}
                  disabled={!selectedPaymentMethod || processing}
                  className="px-6 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center"
                >
                  {processing ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      Memproses...
                    </>
                  ) : (
                    'Lakukan Pembayaran'
                  )}
                </button>
              </div>
            </>
          ) : (
            <>
              {/* Confirmation */}
              <div className="text-center py-8">
                <div className="bg-emerald-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Check className="w-8 h-8 text-emerald-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Invoice Berhasil Dibuat!</h3>
                <p className="text-gray-600 mb-6">
                  Invoice untuk paket <span className="font-medium">{selectedPackage?.name}</span> telah dibuat.
                  Silakan lakukan pembayaran untuk mengaktifkan langganan Anda.
                </p>

                {createdInvoice && (
                  <div className="bg-gray-50 rounded-lg p-4 mb-6">
                    <div className="text-sm text-gray-600 mb-2">
                      <strong>Nomor Invoice:</strong> {createdInvoice.invoice_number}
                    </div>
                    <div className="text-sm text-gray-600 mb-2">
                      <strong>Jumlah:</strong> {formatPrice(createdInvoice.amount)}
                    </div>
                    <div className="text-sm text-gray-600">
                      <strong>Jatuh Tempo:</strong> {formatDate(createdInvoice.due_date)}
                    </div>
                  </div>
                )}

                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  {createdInvoice && onInvoiceCreated && (
                    <button
                      onClick={() => {
                        onInvoiceCreated(createdInvoice.id);
                        onClose();
                      }}
                      className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      Lihat Invoice
                    </button>
                  )}
                  <button
                    onClick={onClose}
                    className="px-6 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
                  >
                    Tutup
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubscriptionModal;
