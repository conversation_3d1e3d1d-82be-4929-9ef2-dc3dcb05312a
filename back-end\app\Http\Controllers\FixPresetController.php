<?php

namespace App\Http\Controllers;

use App\Models\FixPreset;
use Illuminate\Http\Request;

class FixPresetController extends Controller
{
    public function index()
    {
        return response()->json(FixPreset::orderBy('created_at', 'desc')->get());
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'fix' => 'required|array',
        ]);

        $preset = FixPreset::create($validated);
        return response()->json($preset, 201);
    }

    public function destroy(FixPreset $preset)
    {
        $preset->delete();
        return response()->json([ 'deleted' => true ]);
    }
}

