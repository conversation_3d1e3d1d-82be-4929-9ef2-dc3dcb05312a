<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Invoice;
use App\Models\SubscriptionPackage;
use App\Models\PaymentMethod;
use Illuminate\Foundation\Testing\RefreshDatabase;

class InvoiceVerificationTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user for authentication
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);
    }

    public function test_admin_can_approve_invoice_and_activate_subscription()
    {
        // Create subscription package
        $package = SubscriptionPackage::create([
            'name' => 'Test Package',
            'slug' => 'test-package',
            'description' => 'Test package description',
            'price' => 100000,
            'duration_months' => 3,
            'duration_label' => 'per 3 bulan',
            'features' => ['Feature 1', 'Feature 2'],
            'color' => 'blue',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Create payment method
        $paymentMethod = PaymentMethod::create([
            'name' => 'Bank Transfer BCA',
            'type' => 'bank',
            'description' => 'Transfer melalui Bank Central Asia',
            'details' => [
                'bank_name' => 'Bank Central Asia (BCA)',
                'account_number' => '**********',
                'account_name' => 'PT Santuy Grow',
                'bank_code' => 'BCA'
            ],
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Create user
        $user = User::factory()->create();

        // Create invoice
        $invoice = Invoice::create([
            'user_id' => $user->id,
            'subscription_package_id' => $package->id,
            'payment_method_id' => $paymentMethod->id,
            'amount' => $package->price,
            'status' => 'pending_verification',
            'due_date' => now()->addDays(7),
            'invoice_number' => 'INV-' . time(),
        ]);

        // Create token for admin
        $token = $this->admin->createToken('test-token')->plainTextToken;

        // Approve the invoice
        $response = $this->postJson("/api/admin/invoices/{$invoice->id}/verify", [
            'action' => 'approve'
        ], [
            'Authorization' => 'Bearer ' . $token
        ]);

        // Assert response
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Payment approved and subscription activated'
            ]);

        // Refresh models
        $invoice->refresh();
        $user->refresh();

        // Assert invoice is marked as paid
        $this->assertEquals('paid', $invoice->status);
        $this->assertNotNull($invoice->paid_at);

        // Assert user subscription is activated
        $this->assertTrue($user->has_subscription);
        $this->assertEquals($package->id, $user->subscription_package_id);
        $this->assertEquals('active', $user->subscription_status);
        $this->assertNotNull($user->subscription_start);
        $this->assertNotNull($user->subscription_end);

        // Assert subscription duration is correct
        $expectedEndDate = $user->subscription_start->addMonths(3);
        $this->assertEquals(
            $expectedEndDate->format('Y-m-d'),
            $user->subscription_end->format('Y-m-d')
        );
    }

    public function test_admin_can_reject_invoice()
    {
        // Create subscription package
        $package = SubscriptionPackage::create([
            'name' => 'Test Package',
            'slug' => 'test-package',
            'description' => 'Test package description',
            'price' => 100000,
            'duration_months' => 6,
            'duration_label' => 'per 6 bulan',
            'features' => ['Feature 1', 'Feature 2'],
            'color' => 'blue',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Create payment method
        $paymentMethod = PaymentMethod::create([
            'name' => 'Bank Transfer BCA',
            'type' => 'bank',
            'description' => 'Transfer melalui Bank Central Asia',
            'details' => [],
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Create user
        $user = User::factory()->create();

        // Create invoice
        $invoice = Invoice::create([
            'user_id' => $user->id,
            'subscription_package_id' => $package->id,
            'payment_method_id' => $paymentMethod->id,
            'amount' => $package->price,
            'status' => 'pending_verification',
            'due_date' => now()->addDays(7),
            'invoice_number' => 'INV-' . time(),
        ]);

        // Create token for admin
        $token = $this->admin->createToken('test-token')->plainTextToken;

        // Reject the invoice
        $response = $this->postJson("/api/admin/invoices/{$invoice->id}/verify", [
            'action' => 'reject'
        ], [
            'Authorization' => 'Bearer ' . $token
        ]);

        // Assert response
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Payment rejected'
            ]);

        // Refresh models
        $invoice->refresh();
        $user->refresh();

        // Assert invoice is marked as failed
        $this->assertEquals('failed', $invoice->status);

        // Assert user subscription is NOT activated
        $this->assertFalse($user->has_subscription);
        $this->assertNull($user->subscription_package_id);
        $this->assertNull($user->subscription_status);
    }
}
