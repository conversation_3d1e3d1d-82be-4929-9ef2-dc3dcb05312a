<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Update status enum to include pending_verification
            $table->enum('status', ['pending', 'pending_verification', 'paid', 'failed', 'cancelled'])
                ->default('pending')
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Revert back to original enum values
            $table->enum('status', ['pending', 'paid', 'failed', 'cancelled'])
                ->default('pending')
                ->change();
        });
    }
};
