import React, { useEffect, useState } from 'react';
import { MapPin, Users, ShoppingBag, Clock, Car, Building2, Activity, Loader2 } from 'lucide-react';

type AnalysisData = {
  lokasi: { nama: string; lat: number; lng: number; radius_km: number };
  ringkasan: { karakter: string; temuan_kunci: string[]; asumsi: string[] };
  ide_bisnis: Array<any>;
  rekomendasi_final: { pilihan: string; alasan: string[]; metrik_keberhasilan_90_hari: string[] };
} | null;

type Props = {
  analysis: AnalysisData;
  analysisLoading: boolean;
  analysisError: string | null;
  activeTab: 'ringkasan' | 'data' | 'kompetitor' | 'swot' | 'modal' | 'rekomendasi';
  onTabChange: (tab: Props['activeTab']) => void;
};

const RightInfoPanel: React.FC<Props> = ({ analysis, analysisLoading, analysisError, activeTab, onTabChange }) => {
  // Kompetitor fetching state
  const [compLoading, setCompLoading] = useState(false);
  const [compError, setCompError] = useState<string | null>(null);
  const [competitors, setCompetitors] = useState<Array<any>>([]);

  const API_BASE = (import.meta as any).env?.VITE_BACKEND_URL || 'http://localhost:8000';


  const normalizeDash = (s: string) => s?.replace(/\\u2014/g, '—').replace(/\\u2013/g, '–') ?? '';

  useEffect(() => {
    const fetchComps = async () => {
      if (!analysis || activeTab !== 'kompetitor') return;
      try {
        setCompError(null);
        setCompLoading(true);
        const topName = (analysis.ide_bisnis?.[0]?.nama || '').toString();
        const q = topName && topName.length > 1 ? topName : 'usaha';
        const params = new URLSearchParams({
          lat: String(analysis.lokasi?.lat ?? ''),
          lng: String(analysis.lokasi?.lng ?? ''),
          radius_km: String(analysis.lokasi?.radius_km ?? 1),
          query: q,
        });
        const res = await fetch(`${API_BASE}/api/competitors?${params.toString()}`);
        const data = await res.json();
        if (!res.ok) throw new Error(data?.error || 'Gagal mengambil kompetitor');
        setCompetitors(Array.isArray(data.items) ? data.items : []);
      } catch (e: any) {
        setCompError(e?.message || 'Terjadi kesalahan saat mengambil kompetitor');
        setCompetitors([]);
      } finally {
        setCompLoading(false);
      }
    };
    fetchComps();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, analysis?.lokasi?.lat, analysis?.lokasi?.lng, analysis?.ide_bisnis?.[0]?.nama]);

  if (analysisLoading) {
    return (
      <div className="rounded-lg border border-gray-200 bg-white p-5">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-slate-200 rounded w-1/3"></div>
          <div className="h-3 bg-slate-200 rounded w-2/3"></div>
          <div className="grid grid-cols-3 gap-3">
            <div className="h-24 bg-slate-200 rounded"></div>
            <div className="h-24 bg-slate-200 rounded"></div>
            <div className="h-24 bg-slate-200 rounded"></div>
          </div>
          <div className="h-3 bg-slate-200 rounded w-full"></div>
          <div className="h-3 bg-slate-200 rounded w-5/6"></div>
        </div>
      </div>
    );
  }

  if (!analysis) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="font-medium text-gray-800 mb-3 flex items-center gap-2">
          <MapPin className="w-4 h-4 text-gray-500" />
          <span>Siap Menganalisis Lokasi?</span>
        </div>
        <div className="h-48 border border-dashed border-gray-300 rounded-md flex items-center justify-center text-sm text-gray-500 text-center px-4">
          Isi detail di samping untuk mendapatkan analisis potensi traffic, demografi, dan kompetitor dari AI.
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-5">
      {analysisError && (
        <div className="mb-3 text-xs text-red-700 bg-red-50 border border-red-200 rounded p-3">{analysisError}</div>
      )}

      <div className="flex items-center justify-between flex-wrap gap-4">
        <div>
          <div className="text-sm text-gray-500">Lokasi</div>
          <div className="font-semibold text-gray-800">{analysis.lokasi?.nama}</div>
        </div>
        {(() => {
          const top: any = analysis.ide_bisnis?.[0];
          const score = typeof top?.skor_total === 'number' ? Math.max(0, Math.min(100, top.skor_total)) : null;
          if (score == null) return null;
          return (
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">Potensi (skor tertinggi)</div>
              <div className="relative w-16 h-16">
                <div
                  className="w-16 h-16 rounded-full"
                  style={{ background: `conic-gradient(#16a34a ${score * 3.6}deg, #e5e7eb 0)` }}
                />
                <div className="absolute inset-0 flex items-center justify-center text-sm font-semibold text-gray-800">
                  {score}%
                </div>
              </div>
            </div>
          );
        })()}
      </div>

      <div className="mt-5 border-b border-gray-200">
        <nav className="flex gap-4 text-sm">
          {(['ringkasan', 'data', 'kompetitor', 'swot', 'modal', 'rekomendasi'] as Props['activeTab'][]).map((k) => (
            <button
              key={k}
              onClick={() => onTabChange(k)}
              className={`pb-2 -mb-px border-b-2 ${activeTab === k ? 'border-green-600 text-green-700' : 'border-transparent text-gray-500 hover:text-gray-700'}`}
            >
              {k === 'ringkasan' ? 'Ringkasan' : k === 'data' ? 'Data Lapangan' : k === 'kompetitor' ? 'Kompetitor' : k === 'swot' ? 'Analisis SWOT' : k === 'modal' ? 'Modal & Omset' : 'Rekomendasi'}
            </button>
          ))}
        </nav>
      </div>

      <div className="mt-4 text-sm">
        {activeTab === 'ringkasan' && (
          <div className="space-y-3">
            {/* Aksesibilitas */}
            {(() => {
              const top: any = analysis.ide_bisnis?.[0];
              const sc: number | null = typeof top?.skor_komponen?.aksesibilitas === 'number' ? top.skor_komponen.aksesibilitas : null;
              const status = (v: number) => v >= 81 ? 'sangat baik' : v >= 61 ? 'baik' : v >= 41 ? 'cukup' : v >= 21 ? 'buruk' : 'sangat buruk';
              const evidences: string[] = (analysis.ide_bisnis || []).flatMap((it: any) => Array.isArray(it.evidence) ? it.evidence : []);
              const keys = ['akses', 'jalan', 'transport', 'parkir', 'angkot', 'halte', 'tol'];
              const ket = evidences.find(e => keys.some(k => e.toLowerCase().includes(k))) || analysis.ringkasan?.karakter;
              return (
                <div className="rounded-md border border-gray-200 p-3">
                  <div className="font-medium text-gray-800 mb-1">Aksesibilitas</div>
                  <div className="text-sm text-gray-600">Status: <span className="font-semibold text-gray-800">{sc != null ? status(sc) : '—'}</span>{sc != null ? ` (${sc}/100)` : ''}</div>
                  <div className="text-sm text-gray-700 mt-1">{ket || '—'}</div>
                </div>
              );
            })()}

            {/* Visibilitas */}
            {(() => {
              const top: any = analysis.ide_bisnis?.[0];
              const proxy: number | null = typeof top?.skor_komponen?.kecocokan_lingkungan === 'number' ? top.skor_komponen.kecocokan_lingkungan : (typeof top?.skor_komponen?.permintaan_lokal === 'number' ? top.skor_komponen.permintaan_lokal : null);
              const status = (v: number) => v >= 81 ? 'sangat baik' : v >= 61 ? 'baik' : v >= 41 ? 'cukup' : v >= 21 ? 'buruk' : 'sangat buruk';
              const evidences: string[] = (analysis.ide_bisnis || []).flatMap((it: any) => Array.isArray(it.evidence) ? it.evidence : []);
              const keys = ['visibilitas', 'terlihat', 'exposure', 'papan nama', 'jalan utama', 'traffic', 'lalu lintas'];
              const ket = evidences.find(e => keys.some(k => e.toLowerCase().includes(k))) || analysis.ringkasan?.karakter;
              return (
                <div className="rounded-md border border-gray-200 p-3">
                  <div className="font-medium text-gray-800 mb-1">Visibilitas</div>
                  <div className="text-sm text-gray-600">Status: <span className="font-semibold text-gray-800">{proxy != null ? status(proxy) : '—'}</span>{proxy != null ? ` (${proxy}/100)` : ''}</div>
                  <div className="text-sm text-gray-700 mt-1">{ket || '—'}</div>
                </div>
              );
            })()}

            {/* Analisis Jam Sibuk */}
            {(() => {
              const temuan: string[] = (analysis.ringkasan?.temuan_kunci || []).filter(Boolean) as string[];
              const jamLineRaw = temuan.find((s) => /^\s*jam sibuk\s*:/i.test(s || '')) || '';
              const jamLine = normalizeDash(jamLineRaw);
              if (jamLine) {
                const listStr = jamLine.split(':').slice(1).join(':');
                const parts = listStr.split(/\s*,\s*/).map((p) => normalizeDash(p).trim()).filter(Boolean);
                const items = parts.map((p) => {
                  const m = p.match(/(\d{1,2}(?::\d{2})?)\s*[-\u2013\u2014]\s*(\d{1,2}(?::\d{2})?)/);
                  const label = m ? `${m[1]}\u2013${m[2]}` : p.replace(/^\s*jam sibuk\s*[:\-]\s*/i, '').trim();
                  const status = (/sibuk/i.test(p) ? 'sibuk' : /sedang/i.test(p) ? 'sedang' : /rendah/i.test(p) ? 'rendah' : '\u2014');
                  const alasanM = p.match(/alasan\s*:\s*([^\)]*)/i);
                  const alasan = alasanM ? alasanM[1].replace(/\)*\s*$/, '').trim() : '';
                  return { label, status, alasan };
                });
                return (
                  <div className="rounded-md border border-gray-200 p-3">
                    <div className="font-medium text-gray-800 mb-1">Analisis Jam Sibuk</div>
                    <ul className="text-sm text-gray-700 list-disc pl-5">
                      {items.map((it, i) => (
                        <li key={i}>{it.label}{it.status ? <> <span className="font-semibold text-gray-800 capitalize">{it.status}</span></> : null}{it.alasan ? <> (<span className="text-gray-700">{it.alasan}</span>)</> : null}</li>
                      ))}
                    </ul>
                  </div>
                );
              }
              // tidak ada data jam sibuk terstruktur → jangan fallback
              return (
                <div className="rounded-md border border-gray-200 p-3">
                  <div className="font-medium text-gray-800 mb-1">Analisis Jam Sibuk</div>
                  <div className="text-sm text-gray-500">Belum ada data jam sibuk pada respons.</div>
                </div>
              );
            })()}

            {/* Profil Demografis */}
            {(() => {
              const top: any = analysis.ide_bisnis?.[0];
              const dayaScore: number | null = typeof top?.skor_komponen?.daya_beli === 'number' ? top.skor_komponen.daya_beli : null;
              const toStatus = (v: number) => v >= 81 ? 'sangat tinggi' : v >= 61 ? 'tinggi' : v >= 41 ? 'menengah' : v >= 21 ? 'rendah' : 'sangat rendah';
              const temuan: string[] = (analysis.ringkasan?.temuan_kunci || []).filter(Boolean) as string[];
              const getAfter = (key: string) => (temuan.find((s) => new RegExp('^\\s*' + key + '\\s*:', 'i').test(s || '')) || '').split(':').slice(1).join(':').trim();
              const usiaLine = getAfter('rentang usia');
              const pekerjaanLine = getAfter('pekerjaan umum');
              const gayaLine = getAfter('gaya hidup');
              const dayaBeliCat = getAfter('daya beli');

              // Fallback heuristik bila kosong
              const evidences: string[] = (analysis.ide_bisnis || []).flatMap((it: any) => Array.isArray(it.evidence) ? it.evidence : []);
              const pool: string[] = [analysis.ringkasan?.karakter, ...temuan, ...evidences].filter(Boolean) as string[];
              const joined = pool.join('. ').toLowerCase();
              const usiaMatch = (!usiaLine ? joined.match(/\b(\d{2})\s*[\-–]\s*(\d{2})\b/) : null);
              const usia = usiaLine || (usiaMatch ? `${usiaMatch[1]}-${usiaMatch[2]} tahun` : '—');
              const pekerjaanKeys = ['mahasiswa', 'pelajar', 'karyawan', 'pns', 'asn', 'pedagang', 'pekerja', 'buruh', 'wirausaha', 'ibu rumah tangga', 'keluarga muda'];
              const pekerjaan = pekerjaanLine || (pekerjaanKeys.filter(k => joined.includes(k)).slice(0, 3).join(', ') || '—');
              const gayaKeys = ['kepraktisan', 'efisiensi waktu', 'hemat', 'keluarga', 'premium', 'nongkrong', 'olahraga', 'sehat', 'kafe', 'kuliner'];
              const gaya = gayaLine || (gayaKeys.filter(k => joined.includes(k)).slice(0, 3).join(', ') || '—');
              const daya = dayaBeliCat || (dayaScore != null ? toStatus(dayaScore) : '—');

              return (
                <div className="rounded-md border border-gray-200 p-3">
                  <div className="font-medium text-gray-800 mb-1">Profil Demografis</div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                    <div><span className="text-gray-600">Rentang usia:</span> <span className="font-semibold text-gray-800">{usia}</span></div>
                    <div><span className="text-gray-600">Pekerjaan umum:</span> <span className="font-semibold text-gray-800">{pekerjaan}</span></div>
                    <div><span className="text-gray-600">Daya beli:</span> <span className="font-semibold text-gray-800 capitalize">{daya}</span>{(!dayaBeliCat && dayaScore != null) ? ` (${dayaScore}/100)` : ''}</div>
                    <div><span className="text-gray-600">Gaya hidup:</span> <span className="font-semibold text-gray-800">{gaya}</span></div>
                  </div>
                </div>
              );
            })()}
          </div>
        )}

        {activeTab === 'data' && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {(() => {
              const evidences: string[] = (analysis.ide_bisnis || []).flatMap((it: any) => Array.isArray(it.evidence) ? it.evidence : []);
              if (!evidences.length) return <div className="text-gray-500">Belum ada data lapangan pada respons.</div>;
              return evidences.slice(0, 12).map((e: any, idx: number) => {
                const L = String(e || '').toLowerCase();
                let CatIcon: any = Activity; let title = 'Data Lapangan';
                if (/(akses|jalan|parkir|angkot|halte|tol|macet)/.test(L)) { CatIcon = Car; title = 'Aksesibilitas'; }
                else if (/(visibilitas|exposure|papan nama|jalan utama|traffic|lalu lintas)/.test(L)) { CatIcon = Activity; title = 'Visibilitas'; }
                else if (/(demograf|usia|pelajar|mahasiswa|karyawan|keluarga)/.test(L)) { CatIcon = Users; title = 'Demografi'; }
                else if (/(kompetitor|toko|warung|rumah makan|minimarket|ritel|kafe|resto)/.test(L)) { CatIcon = ShoppingBag; title = 'Kompetitor'; }
                else if (/(jam|pagi|siang|sore|malam|\b\d{1,2}:?\d{0,2}\s*[-–—]\s*\d{1,2}:?\d{0,2}\b)/.test(L)) { CatIcon = Clock; title = 'Jam Sibuk'; }
                else if (/(lingkungan|perumahan|kantor|kampus|sekolah)/.test(L)) { CatIcon = Building2; title = 'Lingkungan'; }
                return (
                  <div key={idx} className="rounded-md border border-gray-200 bg-white p-3 text-sm text-gray-700">
                    <div className="flex items-center gap-2 mb-1 text-gray-800"><CatIcon className="w-4 h-4 text-gray-500" /><span className="font-medium">{title}</span></div>
                    <div>{normalizeDash(String(e || ''))}</div>
                  </div>
                );
              });
            })()}
          </div>
        )}

        {activeTab === 'kompetitor' && (
          <div className="rounded-md border border-gray-200 bg-white p-4">
            <div className="flex items-center gap-2 mb-3">
              <ShoppingBag className="w-4 h-4 text-gray-500" />
              <div className="font-medium text-gray-800">Kompetitor Terdekat</div>
            </div>
            {compLoading ? (
              <div className="flex items-center gap-2 text-sm text-gray-600"><Loader2 className="w-4 h-4 animate-spin" /> Mengambil kompetitor terdekat...</div>
            ) : compError ? (
              <div className="text-sm text-red-700 bg-red-50 border border-red-200 rounded p-2">{compError}</div>
            ) : competitors.length === 0 ? (
              <div className="text-sm text-gray-600">Tidak ditemukan usaha serupa dalam radius {analysis.lokasi?.radius_km ?? 1} km.</div>
            ) : (
              <div className="space-y-2">
                {competitors.slice(0, 10).map((c, i) => (
                  <div key={c.place_id || i} className="rounded border border-gray-200 p-3">
                    <div className="font-medium text-gray-800">{c.name || '—'}</div>
                    <div className="text-xs text-gray-600">{c.address || 'Alamat tidak tersedia'}</div>
                    <div className="text-xs text-gray-600 mt-1">
                      {typeof c.rating === 'number' ? `Rating ${c.rating} (${c.user_ratings_total || 0})` : 'Rating tidak tersedia'}
                      {c.open_now != null ? (c.open_now ? ' · Buka sekarang' : ' · Tutup saat ini') : ''}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'swot' && (
          <div className="grid sm:grid-cols-2 gap-4">
            {(() => {
              const top: any = analysis.ide_bisnis?.[0];
              const strengths: string[] = Array.isArray(top?.alasan) ? top.alasan : [];
              const weaknesses: string[] = Array.isArray(top?.risiko) ? top.risiko : [];
              return (
                <>
                  <div className="rounded-md border border-green-200 bg-green-50 p-3">
                    <div className="font-medium text-green-800 mb-1">Kelebihan Lokasi</div>
                    {strengths.length ? (
                      <ul className="list-disc pl-5 text-green-800">{strengths.map((s: any, i: number) => (<li key={i}>{s}</li>))}</ul>
                    ) : <div className="text-green-700 text-sm">Tidak ada data.</div>}
                  </div>
                  <div className="rounded-md border border-red-200 bg-red-50 p-3">
                    <div className="font-medium text-red-800 mb-1">Kekurangan Lokasi</div>
                    {weaknesses.length ? (
                      <ul className="list-disc pl-5 text-red-800">{weaknesses.map((s: any, i: number) => (<li key={i}>{s}</li>))}</ul>
                    ) : <div className="text-red-700 text-sm">Tidak ada data.</div>}
                  </div>
                </>
              );
            })()}
          </div>
        )}

        {activeTab === 'modal' && (
          <div className="grid grid-cols-1 gap-3">
            {(() => {
              const top: any = analysis.ide_bisnis?.[0];
              const est = top?.estimasi;
              if (!est) return <div className="text-gray-500">Belum ada estimasi biaya pada respons.</div>;
              const fmt = (n?: number) => (typeof n === 'number' ? n.toLocaleString('id-ID') : '—');
              return (
                <>
                  <div className="rounded-md border border-gray-200 p-3">
                    <div className="font-medium text-gray-800 mb-1">Estimasi Kebutuhan Modal</div>
                    {Array.isArray(est.rincian_biaya_awal) && est.rincian_biaya_awal.length ? (
                      <>
                        <ul className="text-sm text-gray-700 space-y-1">
                          {est.rincian_biaya_awal.map((it: any, i: number) => (
                            <li key={i} className="flex items-start justify-between gap-3">
                              <span>
                                <span className="text-gray-800">{it.nama}</span>
                                {it.qty ? <span className="text-gray-600"> ({it.qty}{it.unit ? ` ${it.unit}` : ''}{typeof it.harga_satuan_idr === 'number' ? ` x Rp ${fmt(it.harga_satuan_idr)}` : ''})</span> : null}
                                {it.keterangan ? <span className="text-gray-600"> — {it.keterangan}</span> : null}
                              </span>
                              <span className="font-semibold text-gray-800 whitespace-nowrap">Rp {fmt(it.total_idr)}</span>
                            </li>
                          ))}
                        </ul>
                        {typeof est.total_biaya_awal_idr === 'number' ? (
                          <div className="text-sm text-gray-800 font-semibold mt-2">Total biaya awal: Rp {fmt(est.total_biaya_awal_idr)}</div>
                        ) : (est.biaya_awal_idr ? (
                          <div className="text-sm text-gray-700 mt-2">Perkiraan rentang: Rp {fmt(est.biaya_awal_idr.min)} – {fmt(est.biaya_awal_idr.max)}</div>
                        ) : null)}
                      </>
                    ) : (
                      <div className="text-sm text-gray-700">IDR {fmt(est.biaya_awal_idr?.min)} – {fmt(est.biaya_awal_idr?.max)}</div>
                    )}
                  </div>

                  <div className="rounded-md border border-gray-200 p-3">
                    <div className="font-medium text-gray-800 mb-1">Biaya Operasional (Bulanan)</div>
                    {Array.isArray(est.rincian_biaya_operasional_bulanan) && est.rincian_biaya_operasional_bulanan.length ? (
                      <>
                        <ul className="text-sm text-gray-700 space-y-1">
                          {est.rincian_biaya_operasional_bulanan.map((it: any, i: number) => (
                            <li key={i} className="flex items-start justify-between gap-3">
                              <span>
                                <span className="text-gray-800">{it.nama}</span>
                                {it.keterangan ? <span className="text-gray-600"> — {it.keterangan}</span> : null}
                              </span>
                              <span className="font-semibold text-gray-800 whitespace-nowrap">Rp {fmt(it.biaya_idr)}</span>
                            </li>
                          ))}
                        </ul>
                        {typeof est.total_biaya_operasional_bulanan_idr === 'number' ? (
                          <div className="text-sm text-gray-800 font-semibold mt-2">Total operasional/bulan: Rp {fmt(est.total_biaya_operasional_bulanan_idr)}</div>
                        ) : (est.biaya_operasional_bulanan_idr ? (
                          <div className="text-sm text-gray-700 mt-2">Perkiraan rentang: Rp {fmt(est.biaya_operasional_bulanan_idr.min)} – {fmt(est.biaya_operasional_bulanan_idr.max)} /bulan</div>
                        ) : null)}
                      </>
                    ) : (
                      est.biaya_operasional_bulanan_idr ? (
                        <div className="text-sm text-gray-700">Rp {fmt(est.biaya_operasional_bulanan_idr.min)} – {fmt(est.biaya_operasional_bulanan_idr.max)} /bulan</div>
                      ) : <div className="text-sm text-gray-700">—</div>
                    )}
                  </div>

                  <div className="rounded-md border border-gray-200 p-3">
                    <div className="font-medium text-gray-800 mb-1">Proyeksi Pendapatan</div>
                    {est.proyeksi_pendapatan ? (
                      <>
                        <div className="text-sm text-gray-700">Harian: Rp {fmt(est.proyeksi_pendapatan.harian_idr)}</div>
                        <div className="text-sm text-gray-700">Bulanan: Rp {fmt(est.proyeksi_pendapatan.bulanan_idr)}</div>
                        <div className="text-sm text-gray-700">Tahunan: Rp {fmt(est.proyeksi_pendapatan.tahunan_idr)}</div>
                        {est.proyeksi_pendapatan.keterangan ? <div className="text-xs text-gray-500 mt-1">{est.proyeksi_pendapatan.keterangan}</div> : null}
                      </>
                    ) : (
                      <div className="text-sm text-gray-700">
                        {est.proyeksi_pendapatan_bulanan_idr ? (
                          <>IDR {fmt(est.proyeksi_pendapatan_bulanan_idr.min)} – {fmt(est.proyeksi_pendapatan_bulanan_idr.max)} /bulan</>
                        ) : '—'}
                      </div>
                    )}
                  </div>

                  <div className="rounded-md border border-gray-200 p-3">
                    <div className="font-medium text-gray-800 mb-1">Analisis Titik Impas (BEP)</div>
                    {est.bep_detail ? (
                      <>
                        {typeof est.bep_detail.asumsi_transaksi_harian !== 'undefined' ? (
                          <div className="text-sm text-gray-700">Asumsi transaksi rata-rata (harian): {est.bep_detail.asumsi_transaksi_harian}</div>
                        ) : null}
                        {typeof est.bep_detail.penjualan_bep_bulanan !== 'undefined' ? (
                          <div className="text-sm text-gray-700">Penjualan BEP / bulan: {fmt(est.bep_detail.penjualan_bep_bulanan)}</div>
                        ) : null}
                        {typeof est.bep_detail.pendapatan_bep_bulanan_idr !== 'undefined' ? (
                          <div className="text-sm text-gray-700">Pendapatan BEP / bulan: Rp {fmt(est.bep_detail.pendapatan_bep_bulanan_idr)}</div>
                        ) : null}
                        {typeof est.bep_detail.estimasi_waktu_bep_bulan !== 'undefined' ? (
                          <div className="text-sm text-gray-700">Estimasi waktu BEP: {est.bep_detail.estimasi_waktu_bep_bulan} bulan</div>
                        ) : null}
                        {est.bep_detail.keterangan ? <div className="text-xs text-gray-500 mt-1">{est.bep_detail.keterangan}</div> : null}
                      </>
                    ) : (
                      <>
                        <div className="text-sm text-gray-700">
                          {est.bep_omzet_bulanan_idr ? (
                            <>Butuh omzet: IDR {fmt(est.bep_omzet_bulanan_idr.min)} – {fmt(est.bep_omzet_bulanan_idr.max)} /bulan</>
                          ) : est.balik_modal_bulan ? (
                            <>Balik modal: {fmt(est.balik_modal_bulan.min)} – {fmt(est.balik_modal_bulan.max)} bulan</>
                          ) : '—'}
                        </div>
                        {est.biaya_operasional_bulanan_idr && (
                          <div className="text-xs text-gray-500 mt-1">Biaya operasional: IDR {fmt(est.biaya_operasional_bulanan_idr.min)} – {fmt(est.biaya_operasional_bulanan_idr.max)} /bulan</div>
                        )}
                      </>
                    )}
                  </div>

                  <div className="rounded-md border border-gray-200 p-3">
                    <div className="font-medium text-gray-800 mb-1">Strategi Akselerasi Profit</div>
                    {Array.isArray(top?.strategi_akselerasi_profit) && top.strategi_akselerasi_profit.length ? (
                      <ul className="space-y-2">
                        {top.strategi_akselerasi_profit.map((s: any, i: number) => (
                          <li key={i} className="text-sm text-gray-700">
                            <div className="flex items-start justify-between gap-3">
                              <div>
                                <div className="text-xs inline-flex px-2 py-0.5 rounded bg-green-50 text-green-700 border border-green-200 mr-2">{s.kategori}</div>
                                <span className="font-semibold text-gray-800">{s.judul}</span>
                              </div>
                            </div>
                            {s.keterangan ? <div className="text-xs text-gray-600 mt-1">{s.keterangan}</div> : null}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      Array.isArray(top?.langkah_awal) && top.langkah_awal.length ? (
                        <ul className="list-disc pl-5 text-gray-700 text-sm">
                          {top.langkah_awal.map((s: string, i: number) => (<li key={i}>{s}</li>))}
                        </ul>
                      ) : (
                        <div className="text-sm text-gray-700">—</div>
                      )
                    )}
                  </div>
                </>
              );
            })()}
          </div>
        )}

        {activeTab === 'rekomendasi' && (
          <div className="space-y-2">
            <div className="font-medium text-gray-800">Pilihan</div>
            <div className="text-gray-700">{analysis.rekomendasi_final?.pilihan}</div>
            {analysis.rekomendasi_final?.alasan?.length ? (
              <div>
                <div className="font-medium text-gray-800 mt-2">Alasan</div>
                <ul className="list-disc pl-5 text-gray-700">
                  {analysis.rekomendasi_final.alasan.map((s: any, i: number) => (<li key={i}>{s}</li>))}
                </ul>
              </div>
            ) : null}
            {analysis.rekomendasi_final?.metrik_keberhasilan_90_hari?.length ? (
              <div>
                <div className="font-medium text-gray-800 mt-2">Metrik 90 Hari</div>
                <ul className="list-disc pl-5 text-gray-700">
                  {analysis.rekomendasi_final.metrik_keberhasilan_90_hari.map((s: any, i: number) => (<li key={i}>{s}</li>))}
                </ul>
              </div>
            ) : null}
          </div>
        )}
      </div>
    </div>
  );
};

export default RightInfoPanel;
