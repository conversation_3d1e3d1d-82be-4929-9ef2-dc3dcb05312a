<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class ImageBlenderController extends Controller
{
    public function generate(Request $request)
    {
        // Allow long-running
        if (function_exists('set_time_limit')) {
            @set_time_limit(0);
        }
        @ini_set('max_execution_time', '0');

        $validated = $request->validate([
            'model' => 'required|file|image|max:8192',
            'product' => 'required|file|image|max:8192',
            'integrasi' => 'nullable|string',
            'gayaPose' => 'nullable|array',
            'gayaPose.*' => 'string',
            'interaksi' => 'nullable|array',
            'interaksi.*' => 'string',
            'camera' => 'nullable|string',
            'background' => 'nullable|string',
            'art' => 'nullable|string',
            'manual' => 'nullable|string',
            'aspect' => 'required|string|in:1:1,4:5,3:4,16:9,9:16',
            'count' => 'required|integer|min:1|max:4',
        ]);

        $apiKey = config('services.openai.key') ?: env('OPENAI_API_KEY');
        if (!$apiKey) {
            return response()->json(['error' => 'OPENAI_API_KEY is not configured in environment.'], 500);
        }

        $size = match ($validated['aspect']) {
            '1:1' => '1024x1024',
            '16:9' => '1792x1024',
            '9:16' => '1024x1792',
            '4:5' => '1024x1024',
            '3:4' => '1024x1024',
            default => '1024x1024',
        };

        $integrasi = (string) ($validated['integrasi'] ?? '');
        $gayaPose = (array) ($validated['gayaPose'] ?? []);
        $interaksi = (array) ($validated['interaksi'] ?? []);
        $camera = (string) ($validated['camera'] ?? '');
        $background = (string) ($validated['background'] ?? '');
        $art = (string) ($validated['art'] ?? '');
        $manual = (string) ($validated['manual'] ?? '');
        $count = (int) $validated['count'];

        $prompt = "\ud83e\udde9 Image Blender: Integrasikan model + produk agar menyatu natural.\n\n";
        if ($integrasi) $prompt .= "Integrasi Produk: {$integrasi}\n";
        if (!empty($gayaPose)) {
            $prompt .= "Gaya Model & Pose:\n";
            foreach ($gayaPose as $p) {
                $prompt .= "- {$p}\n";
            }
        }
        if (!empty($interaksi)) {
            $prompt .= "Interaksi dengan Produk:\n";
            foreach ($interaksi as $p) {
                $prompt .= "- {$p}\n";
            }
        }
        if ($camera) $prompt .= "Sudut Kamera: {$camera}\n";
        if ($background) $prompt .= "Latar Belakang & Lingkungan: {$background}\n";
        if ($art) $prompt .= "Gaya Artistik: {$art}\n";
        if ($manual) {
            $prompt .= "\nInstruksi Tambahan:\n{$manual}\n";
        }
        $prompt .= "\nSamakan pencahayaan, tone warna, perspektif, tambahkan bayangan/refleksi realistis agar produk menyatu dengan tangan/tubuh model. Hasilkan komposisi editorial yang rapi.\n";

        try {
            $modelFile = $request->file('model');
            $productFile = $request->file('product');

            $http = Http::withToken($apiKey)->timeout(0)->connectTimeout(0)->asMultipart();
            // Attach both images using array syntax 'image[]' per OpenAI API requirement
            $http = $http->attach('image[]', file_get_contents($modelFile->getRealPath()), $modelFile->getClientOriginalName() ?: 'model.' . $modelFile->extension());
            $http = $http->attach('image[]', file_get_contents($productFile->getRealPath()), $productFile->getClientOriginalName() ?: 'product.' . $productFile->extension());

            $response = $http->post('https://api.openai.com/v1/images/edits', [
                ['name' => 'model', 'contents' => 'gpt-image-1'],
                ['name' => 'prompt', 'contents' => $prompt],
                ['name' => 'n', 'contents' => (string) $count],
                ['name' => 'size', 'contents' => $size],
            ]);

            if (!$response->successful()) {
                return response()->json([
                    'error' => 'OpenAI request failed',
                    'details' => $response->json(),
                ], $response->status());
            }

            $json = $response->json();
            $images = [];
            if (!empty($json['data']) && is_array($json['data'])) {
                foreach ($json['data'] as $item) {
                    if (!empty($item['b64_json'])) {
                        $images[] = 'data:image/png;base64,' . $item['b64_json'];
                    } elseif (!empty($item['url'])) {
                        $images[] = $item['url'];
                    }
                }
            }

            return response()->json(['images' => $images, 'size' => $size]);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Server error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
