import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

const API_BASE = (import.meta as any).env?.VITE_BACKEND_URL || 'http://localhost:8000';

interface User {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'user';
  is_active: boolean;
  created_at: string;
  has_subscription?: boolean;
  subscription_start?: string;
  subscription_end?: string;
  subscription_package_id?: number;
  subscription_status?: 'active' | 'expired' | 'cancelled';
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; message?: string }>;
  register: (name: string, email: string, password: string, passwordConfirmation: string) => Promise<{ success: boolean; message?: string }>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  refreshUser: () => Promise<void>;
  setUser: (user: User | null) => void;
  startSubscriptionPolling: () => void;
  stopSubscriptionPolling: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [subscriptionPolling, setSubscriptionPolling] = useState(false);

  const isAuthenticated = !!user;

  // Check authentication status on app load
  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('auth_token');

      if (!token || token === 'null') {
        setUser(null);
        setIsLoading(false);
        return;
      }

      const response = await fetch(`${API_BASE}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.success && data.user) {
        setUser(data.user);
      } else {
        // Token is invalid, remove it
        localStorage.removeItem('auth_token');
        setUser(null);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      localStorage.removeItem('auth_token');
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh user data (useful after subscription changes)
  const refreshUser = async () => {
    try {
      const token = localStorage.getItem('auth_token');

      if (!token || token === 'null') {
        return;
      }

      const response = await fetch(`${API_BASE}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.success && data.user) {
        const previousSubscriptionStatus = user?.has_subscription;
        setUser(data.user);

        // If subscription was just activated, show notification
        if (!previousSubscriptionStatus && data.user.has_subscription) {
          // This will be handled by components that need to show notifications
          window.dispatchEvent(new CustomEvent('subscriptionActivated'));
        }
      }
    } catch (error) {
      console.error('User refresh failed:', error);
    }
  };

  // Start polling for subscription status changes
  const startSubscriptionPolling = () => {
    if (subscriptionPolling) return;

    setSubscriptionPolling(true);
    const interval = setInterval(async () => {
      await refreshUser();
    }, 10000); // Poll every 10 seconds

    // Store interval ID for cleanup
    (window as any).subscriptionPollingInterval = interval;

    // Auto-stop after 5 minutes
    setTimeout(() => {
      stopSubscriptionPolling();
    }, 5 * 60 * 1000);
  };

  // Stop polling for subscription status changes
  const stopSubscriptionPolling = () => {
    setSubscriptionPolling(false);
    if ((window as any).subscriptionPollingInterval) {
      clearInterval((window as any).subscriptionPollingInterval);
      (window as any).subscriptionPollingInterval = null;
    }
  };

  // Login function
  const login = async (email: string, password: string) => {
    try {
      const response = await fetch(`${API_BASE}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (data.success) {
        // Store token in localStorage
        localStorage.setItem('auth_token', data.token);
        setUser(data.user);
        return { success: true };
      } else {
        return { success: false, message: data.message || 'Login failed' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: 'Network error. Please try again.' };
    }
  };

  // Register function
  const register = async (name: string, email: string, password: string, passwordConfirmation: string) => {
    try {
      const response = await fetch(`${API_BASE}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          name,
          email,
          password,
          password_confirmation: passwordConfirmation,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Store token in localStorage
        if (data.token) {
          localStorage.setItem('auth_token', data.token);
        }
        setUser(data.user);
        return { success: true };
      } else {
        return { success: false, message: data.message || 'Registration failed' };
      }
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, message: 'Network error. Please try again.' };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      const token = localStorage.getItem('auth_token');

      if (token) {
        await fetch(`${API_BASE}/api/auth/logout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always remove token and clear user
      localStorage.removeItem('auth_token');
      setUser(null);
    }
  };

  // Check auth on component mount
  useEffect(() => {
    checkAuth();
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    checkAuth,
    refreshUser,
    setUser,
    startSubscriptionPolling,
    stopSubscriptionPolling,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
