[2025-09-24 08:00:01] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.worksheets' doesn't exist (Connection: mysql, SQL: select * from `worksheets` order by `created_at` desc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.worksheets' doesn't exist (Connection: mysql, SQL: select * from `worksheets` order by `created_at` desc) at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\WorksheetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\WorksheetController->index()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\WorksheetController), 'index')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#47 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.worksheets' doesn't exist at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:406)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(406): PDO->prepare('select * from `...')
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\WorksheetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\WorksheetController->index()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\WorksheetController), 'index')
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#48 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#49 {main}
"} 
[2025-09-24 08:00:09] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.worksheets' doesn't exist (Connection: mysql, SQL: select * from `worksheets` order by `created_at` desc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.worksheets' doesn't exist (Connection: mysql, SQL: select * from `worksheets` order by `created_at` desc) at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\WorksheetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\WorksheetController->index()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\WorksheetController), 'index')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#47 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.worksheets' doesn't exist at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:406)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(406): PDO->prepare('select * from `...')
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\WorksheetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\WorksheetController->index()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\WorksheetController), 'index')
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#48 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#49 {main}
"} 
[2025-09-24 08:00:27] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.worksheets' doesn't exist (Connection: mysql, SQL: select * from `worksheets` order by `created_at` desc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.worksheets' doesn't exist (Connection: mysql, SQL: select * from `worksheets` order by `created_at` desc) at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\WorksheetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\WorksheetController->index()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\WorksheetController), 'index')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#47 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.worksheets' doesn't exist at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:406)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(406): PDO->prepare('select * from `...')
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\WorksheetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\WorksheetController->index()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\WorksheetController), 'index')
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#48 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#49 {main}
"} 
[2025-09-24 13:08:08] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php:45)
[stacktrace]
#0 {main}
"} 
[2025-09-24 13:09:50] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php:45)
[stacktrace]
#0 {main}
"} 
[2025-09-24 14:07:25] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php:45)
[stacktrace]
#0 {main}
"} 
[2025-09-25 06:49:23] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.fix_presets' doesn't exist (Connection: mysql, SQL: select * from `fix_presets` order by `created_at` desc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.fix_presets' doesn't exist (Connection: mysql, SQL: select * from `fix_presets` order by `created_at` desc) at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\FixPresetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\FixPresetController->index()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\FixPresetController), 'index')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#47 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.fix_presets' doesn't exist at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:406)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(406): PDO->prepare('select * from `...')
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\FixPresetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\FixPresetController->index()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\FixPresetController), 'index')
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#48 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#49 {main}
"} 
[2025-09-25 06:49:26] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.fix_presets' doesn't exist (Connection: mysql, SQL: select * from `fix_presets` order by `created_at` desc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.fix_presets' doesn't exist (Connection: mysql, SQL: select * from `fix_presets` order by `created_at` desc) at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\FixPresetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\FixPresetController->index()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\FixPresetController), 'index')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#47 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.fix_presets' doesn't exist at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:406)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(406): PDO->prepare('select * from `...')
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\FixPresetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\FixPresetController->index()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\FixPresetController), 'index')
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#48 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#49 {main}
"} 
[2025-09-25 06:49:39] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.fix_presets' doesn't exist (Connection: mysql, SQL: select * from `fix_presets` order by `created_at` desc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.fix_presets' doesn't exist (Connection: mysql, SQL: select * from `fix_presets` order by `created_at` desc) at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\FixPresetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\FixPresetController->index()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\FixPresetController), 'index')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#47 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.fix_presets' doesn't exist at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:406)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(406): PDO->prepare('select * from `...')
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\FixPresetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\FixPresetController->index()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\FixPresetController), 'index')
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#48 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#49 {main}
"} 
[2025-09-25 06:50:03] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.fix_presets' doesn't exist (Connection: mysql, SQL: select * from `fix_presets` order by `created_at` desc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.fix_presets' doesn't exist (Connection: mysql, SQL: select * from `fix_presets` order by `created_at` desc) at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\FixPresetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\FixPresetController->index()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\FixPresetController), 'index')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#47 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.fix_presets' doesn't exist at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:406)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(406): PDO->prepare('select * from `...')
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\FixPresetController.php(12): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\FixPresetController->index()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\FixPresetController), 'index')
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#48 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#49 {main}
"} 
[2025-09-25 07:34:08] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.hpp_records' doesn't exist (Connection: mysql, SQL: select * from `hpp_records` where `model` = ritel-fnb order by `created_at` desc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.hpp_records' doesn't exist (Connection: mysql, SQL: select * from `hpp_records` where `model` = ritel-fnb order by `created_at` desc) at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\HppRecordController.php(15): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\HppRecordController->index(Object(Illuminate\\Http\\Request))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HppRecordController), 'index')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#47 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.hpp_records' doesn't exist at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:406)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(406): PDO->prepare('select * from `...')
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\HppRecordController.php(15): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\HppRecordController->index(Object(Illuminate\\Http\\Request))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HppRecordController), 'index')
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#48 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#49 {main}
"} 
[2025-09-25 07:34:10] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.hpp_records' doesn't exist (Connection: mysql, SQL: select * from `hpp_records` where `model` = ritel-fnb order by `created_at` desc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.hpp_records' doesn't exist (Connection: mysql, SQL: select * from `hpp_records` where `model` = ritel-fnb order by `created_at` desc) at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\HppRecordController.php(15): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\HppRecordController->index(Object(Illuminate\\Http\\Request))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HppRecordController), 'index')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#47 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.hpp_records' doesn't exist at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:406)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(406): PDO->prepare('select * from `...')
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(397): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3188): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3173): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3763): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3172): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(902): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(884): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\HppRecordController.php(15): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\HppRecordController->index(Object(Illuminate\\Http\\Request))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HppRecordController), 'index')
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#48 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#49 {main}
"} 
[2025-09-25 07:34:25] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.hpp_records' doesn't exist (Connection: mysql, SQL: insert into `hpp_records` (`model`, `title`, `data`, `updated_at`, `created_at`) values (ritel-fnb, Bumbu Soto Madura, {"inputs":{"vfNama":"Bumbu Soto Madura","vfKategori":"makanan","vfTarget":"100","vfChosenPrice":"72000","vfTargetProfit":"5000000"},"vfVarCosts":[{"id":"5f7c39ef-ec72-48bd-b92e-8aac09c516a1","bahan":"Ayam","pakaiQty":"1000","pakaiUnit":"g","totalHarga":"30000","beliQty":"1000","beliUnit":"g"},{"id":"07758ccd-62ac-4ee3-ad89-bb4d119a223c","bahan":"Beras","pakaiQty":"500","pakaiUnit":"g","totalHarga":"8000","beliQty":"1000","beliUnit":"g"},{"id":"7a9db29b-eb70-433b-aa6f-50c0e045c171","bahan":"Bumbu Soto (Bawang, Jahe, Kunyit)","pakaiQty":"200","pakaiUnit":"g","totalHarga":"15000","beliQty":"500","beliUnit":"g"},{"id":"c4189306-d663-4964-aa42-a9e15bbc70f3","bahan":"Daun Seledri","pakaiQty":"50","pakaiUnit":"g","totalHarga":"5000","beliQty":"100","beliUnit":"g"},{"id":"16c12a46-90ae-4a8a-845f-96755ec2ad52","bahan":"Kemasan (Botol)","pakaiQty":"10","pakaiUnit":"pcs","totalHarga":"20000","beliQty":"10","beliUnit":"pcs"},{"id":"ceabfc92-cd4b-4d44-ba64-f0ab3f34cba8","bahan":"Minyak Goreng","pakaiQty":"200","pakaiUnit":"ml","totalHarga":"6000","beliQty":"1000","beliUnit":"ml"},{"id":"75ad781b-1bef-425f-8df0-2ea192028f58","bahan":"Garam","pakaiQty":"100","pakaiUnit":"g","totalHarga":"2000","beliQty":"500","beliUnit":"g"},{"id":"7010744b-f362-4621-be0a-ad87973c3e4f","bahan":"Merica","pakaiQty":"50","pakaiUnit":"g","totalHarga":"3000","beliQty":"100","beliUnit":"g"}],"vfFixCosts":[{"id":"3900e7aa-901c-4fa6-ba38-dc7ea87ccae1","nama":"Sewa Tempat","totalBulan":"2000000","alokasi":null},{"id":"c9c1b8f0-35d6-45aa-a5b2-81f80e0ebd40","nama":"Gaji Karyawan","totalBulan":"3000000","alokasi":null},{"id":"9ada1945-ef92-4e47-b748-27f86b143464","nama":"Utilitas (Listrik, Air)","totalBulan":"1000000","alokasi":null},{"id":"9ee855bd-15d7-4882-87e4-8575975e4b73","nama":"Biaya Pemasaran","totalBulan":"500000","alokasi":null},{"id":"acabd208-f83a-4b88-91a5-534653526dcc","nama":"Biaya Transportasi","totalBulan":"700000","alokasi":null}],"summary":{"totalVar":65600,"allocFix":0,"totalFix":7200000,"hpp":65600},"vfPriceSuggests":[{"level":"kompetitif","harga":72000,"profit_per_unit":6400,"margin_pct":10.3,"catatan":"Harga ini sedikit di atas HPP, memberikan margin yang kompetitif untuk menarik pelanggan."},{"level":"standar","harga":85000,"profit_per_unit":19400,"margin_pct":22.4,"catatan":"Harga ini memberikan margin yang lebih baik, cocok untuk pasar menengah."},{"level":"premium","harga":105000,"profit_per_unit":39400,"margin_pct":37.1,"catatan":"Harga premium ini menawarkan margin tinggi, namun perlu dipastikan kualitas produk sesuai harapan konsumen."}]}, 2025-09-25 07:34:25, 2025-09-25 07:34:25)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.hpp_records' doesn't exist (Connection: mysql, SQL: insert into `hpp_records` (`model`, `title`, `data`, `updated_at`, `created_at`) values (ritel-fnb, Bumbu Soto Madura, {\"inputs\":{\"vfNama\":\"Bumbu Soto Madura\",\"vfKategori\":\"makanan\",\"vfTarget\":\"100\",\"vfChosenPrice\":\"72000\",\"vfTargetProfit\":\"5000000\"},\"vfVarCosts\":[{\"id\":\"5f7c39ef-ec72-48bd-b92e-8aac09c516a1\",\"bahan\":\"Ayam\",\"pakaiQty\":\"1000\",\"pakaiUnit\":\"g\",\"totalHarga\":\"30000\",\"beliQty\":\"1000\",\"beliUnit\":\"g\"},{\"id\":\"07758ccd-62ac-4ee3-ad89-bb4d119a223c\",\"bahan\":\"Beras\",\"pakaiQty\":\"500\",\"pakaiUnit\":\"g\",\"totalHarga\":\"8000\",\"beliQty\":\"1000\",\"beliUnit\":\"g\"},{\"id\":\"7a9db29b-eb70-433b-aa6f-50c0e045c171\",\"bahan\":\"Bumbu Soto (Bawang, Jahe, Kunyit)\",\"pakaiQty\":\"200\",\"pakaiUnit\":\"g\",\"totalHarga\":\"15000\",\"beliQty\":\"500\",\"beliUnit\":\"g\"},{\"id\":\"c4189306-d663-4964-aa42-a9e15bbc70f3\",\"bahan\":\"Daun Seledri\",\"pakaiQty\":\"50\",\"pakaiUnit\":\"g\",\"totalHarga\":\"5000\",\"beliQty\":\"100\",\"beliUnit\":\"g\"},{\"id\":\"16c12a46-90ae-4a8a-845f-96755ec2ad52\",\"bahan\":\"Kemasan (Botol)\",\"pakaiQty\":\"10\",\"pakaiUnit\":\"pcs\",\"totalHarga\":\"20000\",\"beliQty\":\"10\",\"beliUnit\":\"pcs\"},{\"id\":\"ceabfc92-cd4b-4d44-ba64-f0ab3f34cba8\",\"bahan\":\"Minyak Goreng\",\"pakaiQty\":\"200\",\"pakaiUnit\":\"ml\",\"totalHarga\":\"6000\",\"beliQty\":\"1000\",\"beliUnit\":\"ml\"},{\"id\":\"75ad781b-1bef-425f-8df0-2ea192028f58\",\"bahan\":\"Garam\",\"pakaiQty\":\"100\",\"pakaiUnit\":\"g\",\"totalHarga\":\"2000\",\"beliQty\":\"500\",\"beliUnit\":\"g\"},{\"id\":\"7010744b-f362-4621-be0a-ad87973c3e4f\",\"bahan\":\"Merica\",\"pakaiQty\":\"50\",\"pakaiUnit\":\"g\",\"totalHarga\":\"3000\",\"beliQty\":\"100\",\"beliUnit\":\"g\"}],\"vfFixCosts\":[{\"id\":\"3900e7aa-901c-4fa6-ba38-dc7ea87ccae1\",\"nama\":\"Sewa Tempat\",\"totalBulan\":\"2000000\",\"alokasi\":null},{\"id\":\"c9c1b8f0-35d6-45aa-a5b2-81f80e0ebd40\",\"nama\":\"Gaji Karyawan\",\"totalBulan\":\"3000000\",\"alokasi\":null},{\"id\":\"9ada1945-ef92-4e47-b748-27f86b143464\",\"nama\":\"Utilitas (Listrik, Air)\",\"totalBulan\":\"1000000\",\"alokasi\":null},{\"id\":\"9ee855bd-15d7-4882-87e4-8575975e4b73\",\"nama\":\"Biaya Pemasaran\",\"totalBulan\":\"500000\",\"alokasi\":null},{\"id\":\"acabd208-f83a-4b88-91a5-534653526dcc\",\"nama\":\"Biaya Transportasi\",\"totalBulan\":\"700000\",\"alokasi\":null}],\"summary\":{\"totalVar\":65600,\"allocFix\":0,\"totalFix\":7200000,\"hpp\":65600},\"vfPriceSuggests\":[{\"level\":\"kompetitif\",\"harga\":72000,\"profit_per_unit\":6400,\"margin_pct\":10.3,\"catatan\":\"Harga ini sedikit di atas HPP, memberikan margin yang kompetitif untuk menarik pelanggan.\"},{\"level\":\"standar\",\"harga\":85000,\"profit_per_unit\":19400,\"margin_pct\":22.4,\"catatan\":\"Harga ini memberikan margin yang lebih baik, cocok untuk pasar menengah.\"},{\"level\":\"premium\",\"harga\":105000,\"profit_per_unit\":39400,\"margin_pct\":37.1,\"catatan\":\"Harga premium ini menawarkan margin tinggi, namun perlu dipastikan kualitas produk sesuai harapan konsumen.\"}]}, 2025-09-25 07:34:25, 2025-09-25 07:34:25)) at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('insert into `hp...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `hp...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `hp...', Array, 'id')
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `hp...', Array, 'id')
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2235): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1436): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1401): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1240): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1219): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(390): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\HppRecord))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1218): tap(Object(App\\Models\\HppRecord), Object(Closure))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2540): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2556): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\HppRecordController.php(28): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\HppRecordController->store(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HppRecordController), 'store')
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#53 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'back_end.hpp_records' doesn't exist at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:47)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(47): PDO->prepare('insert into `hp...')
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `hp...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('insert into `hp...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `hp...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `hp...', Array, 'id')
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `hp...', Array, 'id')
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2235): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1436): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1401): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1240): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1219): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(390): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\HppRecord))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1218): tap(Object(App\\Models\\HppRecord), Object(Closure))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2540): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2556): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\HppRecordController.php(28): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\HppRecordController->store(Object(Illuminate\\Http\\Request))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HppRecordController), 'store')
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#55 {main}
"} 
[2025-09-25 07:44:37] local.ERROR: The "--fresh" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--fresh\" option does not exist. at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Input\\ArgvInput.php(155): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('fresh', NULL)
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--fresh')
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--fresh', true)
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Command\\Command.php(276): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-29 06:55:43] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php:45)
[stacktrace]
#0 {main}
"} 
[2025-10-02 02:08:15] local.INFO: Starting Step 1: Generate narasi + konsep {"product":"Kopi Susu Aren","purpose":"promosi produk","duration":30,"tone":"friendly"} 
[2025-10-02 02:08:37] local.INFO: Step 1 completed successfully {"concept_length":618,"script_length":428,"scenes_count":4} 
[2025-10-02 02:08:49] local.INFO: Starting Step 1: Generate narasi + konsep {"product":"Kopi Susu Aren","purpose":"promosi produk","duration":30,"tone":"friendly"} 
[2025-10-02 02:09:05] local.INFO: Step 1 completed successfully {"concept_length":463,"script_length":295,"scenes_count":4} 
[2025-10-02 02:22:40] local.INFO: Starting Step 1: Generate narasi + konsep {"product":"Kopi Susu Aren","purpose":"promosi produk","duration":30,"tone":"friendly"} 
[2025-10-02 02:22:54] local.INFO: Step 1 completed successfully {"concept_length":789,"narration_length":411,"duration":30} 
[2025-10-02 02:26:58] local.INFO: Starting Step 2: Generate voice over {"narration_length":336,"language":"id-ID","gender":"female","tone":"friendly"} 
[2025-10-02 02:27:23] local.INFO: Gemini TTS API response received {"has_candidates":true,"response_keys":["candidates","usageMetadata","modelVersion","responseId"]} 
[2025-10-02 02:27:23] local.INFO: Video voice over generated successfully: http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372043.wav  
[2025-10-02 02:27:23] local.INFO: Step 2 completed successfully {"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372043.wav"} 
[2025-10-02 02:27:30] local.INFO: Starting Step 2: Generate voice over {"narration_length":68,"language":"id-ID","gender":"male","tone":"friendly"} 
[2025-10-02 02:27:37] local.INFO: Gemini TTS API response received {"has_candidates":true,"response_keys":["candidates","usageMetadata","modelVersion","responseId"]} 
[2025-10-02 02:27:37] local.INFO: Video voice over generated successfully: http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372057.wav  
[2025-10-02 02:27:37] local.INFO: Step 2 completed successfully {"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372057.wav"} 
[2025-10-02 02:37:33] local.INFO: Starting Step 3: Generate silent video {"concept_length":303,"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372057.wav","duration":30,"style":"modern"} 
[2025-10-02 02:37:33] local.INFO: Estimated audio duration from file size {"file_size":209850,"estimated_duration":5} 
[2025-10-02 02:37:33] local.INFO: Using actual audio duration {"duration":5} 
[2025-10-02 02:37:33] local.INFO: Built video prompt {"prompt_length":482,"style":"modern","duration":5} 
[2025-10-02 02:37:33] local.INFO: Generating silent video with Veo {"prompt_length":482,"duration":5} 
[2025-10-02 02:37:34] local.ERROR: Veo API request failed {"status":400,"body":"{
  \"error\": {
    \"code\": 400,
    \"message\": \"`duration` isn't supported by this model. Please remove it or refer to the Gemini API documentation for supported usage.\",
    \"status\": \"INVALID_ARGUMENT\"
  }
}
"} 
[2025-10-02 02:37:34] local.ERROR: Veo video generation error: Veo API request failed: {
  "error": {
    "code": 400,
    "message": "`duration` isn't supported by this model. Please remove it or refer to the Gemini API documentation for supported usage.",
    "status": "INVALID_ARGUMENT"
  }
}
  
[2025-10-02 02:37:34] local.ERROR: Step 3 error: Veo API request failed: {
  "error": {
    "code": 400,
    "message": "`duration` isn't supported by this model. Please remove it or refer to the Gemini API documentation for supported usage.",
    "status": "INVALID_ARGUMENT"
  }
}
  
[2025-10-02 02:37:36] local.INFO: Starting Step 3: Generate silent video {"concept_length":19,"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372057.wav","duration":8,"style":"modern"} 
[2025-10-02 02:37:36] local.INFO: Estimated audio duration from file size {"file_size":209850,"estimated_duration":5} 
[2025-10-02 02:37:36] local.INFO: Using actual audio duration {"duration":5} 
[2025-10-02 02:37:36] local.INFO: Built video prompt {"prompt_length":463,"style":"modern","duration":5} 
[2025-10-02 02:37:36] local.INFO: Generating silent video with Veo {"prompt_length":463,"duration":5} 
[2025-10-02 02:37:36] local.ERROR: Veo API request failed {"status":400,"body":"{
  \"error\": {
    \"code\": 400,
    \"message\": \"`duration` isn't supported by this model. Please remove it or refer to the Gemini API documentation for supported usage.\",
    \"status\": \"INVALID_ARGUMENT\"
  }
}
"} 
[2025-10-02 02:37:36] local.ERROR: Veo video generation error: Veo API request failed: {
  "error": {
    "code": 400,
    "message": "`duration` isn't supported by this model. Please remove it or refer to the Gemini API documentation for supported usage.",
    "status": "INVALID_ARGUMENT"
  }
}
  
[2025-10-02 02:37:36] local.ERROR: Step 3 error: Veo API request failed: {
  "error": {
    "code": 400,
    "message": "`duration` isn't supported by this model. Please remove it or refer to the Gemini API documentation for supported usage.",
    "status": "INVALID_ARGUMENT"
  }
}
  
[2025-10-02 02:37:58] local.INFO: Starting Step 3: Generate silent video {"concept_length":19,"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372057.wav","duration":8,"style":"modern"} 
[2025-10-02 02:37:58] local.INFO: Estimated audio duration from file size {"file_size":209850,"estimated_duration":5} 
[2025-10-02 02:37:58] local.INFO: Using actual audio duration {"duration":5} 
[2025-10-02 02:37:58] local.INFO: Built video prompt {"prompt_length":463,"style":"modern","duration":5} 
[2025-10-02 02:37:58] local.INFO: Generating silent video with Veo {"prompt_length":463,"duration":5} 
[2025-10-02 02:38:01] local.INFO: Veo operation started {"operation":"models/veo-3.0-generate-001/operations/hd2tr42ud8ys"} 
[2025-10-02 02:38:11] local.INFO: Veo operation still processing {"attempt":1,"max":60} 
[2025-10-02 02:38:22] local.INFO: Veo operation still processing {"attempt":2,"max":60} 
[2025-10-02 02:38:32] local.INFO: Veo operation still processing {"attempt":3,"max":60} 
[2025-10-02 02:38:43] local.INFO: Veo operation still processing {"attempt":4,"max":60} 
[2025-10-02 02:38:53] local.INFO: Veo operation still processing {"attempt":5,"max":60} 
[2025-10-02 02:39:03] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Controllers\\VideoGeneratorController.php:877)
[stacktrace]
#0 {main}
"} 
[2025-10-02 02:40:04] local.INFO: Starting Step 3: Generate silent video {"concept_length":71,"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372057.wav","duration":8,"style":"modern","async":true} 
[2025-10-02 02:40:04] local.INFO: Estimated audio duration from file size {"file_size":209850,"estimated_duration":5} 
[2025-10-02 02:40:04] local.INFO: Using actual audio duration {"duration":5} 
[2025-10-02 02:40:04] local.INFO: Built video prompt {"prompt_length":473,"style":"modern","duration":5} 
[2025-10-02 02:40:04] local.INFO: Starting async video generation with Veo {"prompt_length":473,"duration":5} 
[2025-10-02 02:40:06] local.INFO: Async Veo operation started {"operation":"models/veo-3.0-generate-001/operations/ckiuutrcqugx"} 
[2025-10-02 02:40:14] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/ckiuutrcqugx"} 
[2025-10-02 02:40:48] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/ckiuutrcqugx"} 
[2025-10-02 02:45:16] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/ckiuutrcqugx"} 
[2025-10-02 02:45:16] local.INFO: Video operation completed {"operation_id":"models/veo-3.0-generate-001/operations/ckiuutrcqugx"} 
[2025-10-02 02:45:16] local.INFO: Downloading video from Veo {"uri":"https://generativelanguage.googleapis.com/v1beta/files/zn0d6twjymc8:download?alt=media"} 
[2025-10-02 02:45:19] local.INFO: Silent video saved successfully {"path":"silent-videos/silent-video-1759373118-6169.mp4","url":"http://127.0.0.1:8000/storage/silent-videos/silent-video-1759373118-6169.mp4","size":2172933} 
[2025-10-02 02:47:58] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/ckiuutrcqugx"} 
[2025-10-02 02:47:58] local.INFO: Video operation completed {"operation_id":"models/veo-3.0-generate-001/operations/ckiuutrcqugx"} 
[2025-10-02 02:47:58] local.INFO: Downloading video from Veo {"uri":"https://generativelanguage.googleapis.com/v1beta/files/zn0d6twjymc8:download?alt=media"} 
[2025-10-02 02:48:00] local.INFO: Silent video saved successfully {"path":"silent-videos/silent-video-1759373280-9407.mp4","url":"http://127.0.0.1:8000/storage/silent-videos/silent-video-1759373280-9407.mp4","size":2172933} 
[2025-10-02 02:48:00] local.WARNING: FFmpeg not installed, returning video with audio  
[2025-10-02 02:49:47] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/ckiuutrcqugx"} 
[2025-10-02 02:49:48] local.INFO: Video operation completed {"operation_id":"models/veo-3.0-generate-001/operations/ckiuutrcqugx"} 
[2025-10-02 02:49:48] local.INFO: Downloading video from Veo {"uri":"https://generativelanguage.googleapis.com/v1beta/files/zn0d6twjymc8:download?alt=media"} 
[2025-10-02 02:49:50] local.INFO: Silent video saved successfully {"path":"silent-videos/silent-video-1759373390-9312.mp4","url":"http://127.0.0.1:8000/storage/silent-videos/silent-video-1759373390-9312.mp4","size":2172933} 
[2025-10-02 02:49:50] local.WARNING: FFmpeg not installed, returning video with audio  
[2025-10-02 02:51:52] local.INFO: Starting Step 3: Generate silent video {"concept_length":30,"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372057.wav","duration":8,"style":"modern","async":true} 
[2025-10-02 02:51:52] local.INFO: Estimated audio duration from file size {"file_size":209850,"estimated_duration":5} 
[2025-10-02 02:51:52] local.INFO: Using actual audio duration {"duration":5} 
[2025-10-02 02:51:52] local.INFO: Built video prompt {"prompt_length":465,"style":"modern","duration":5} 
[2025-10-02 02:51:52] local.INFO: Starting async video generation with Veo {"prompt_length":465,"duration":5} 
[2025-10-02 02:51:54] local.INFO: Async Veo operation started {"operation":"models/veo-3.0-generate-001/operations/9mrxi6x56y82"} 
[2025-10-02 02:52:00] local.INFO: Starting Step 3: Generate silent video {"concept_length":30,"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372057.wav","duration":8,"style":"modern","async":true} 
[2025-10-02 02:52:00] local.INFO: Estimated audio duration from file size {"file_size":209850,"estimated_duration":5} 
[2025-10-02 02:52:00] local.INFO: Using actual audio duration {"duration":5} 
[2025-10-02 02:52:00] local.INFO: Built video prompt {"prompt_length":465,"style":"modern","duration":5} 
[2025-10-02 02:52:00] local.INFO: Starting async video generation with Veo {"prompt_length":465,"duration":5} 
[2025-10-02 02:52:01] local.INFO: Async Veo operation started {"operation":"models/veo-3.0-generate-001/operations/xwdiruhduwak"} 
[2025-10-02 02:56:40] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/xwdiruhduwak"} 
[2025-10-02 02:56:40] local.INFO: Video operation completed {"operation_id":"models/veo-3.0-generate-001/operations/xwdiruhduwak"} 
[2025-10-02 02:56:40] local.INFO: Downloading video from Veo {"uri":"https://generativelanguage.googleapis.com/v1beta/files/7gk0ecmbbucp:download?alt=media"} 
[2025-10-02 02:56:42] local.INFO: Silent video saved successfully {"path":"silent-videos/silent-video-1759373802-1387.mp4","url":"http://127.0.0.1:8000/storage/silent-videos/silent-video-1759373802-1387.mp4","size":1654936} 
[2025-10-02 02:56:42] local.WARNING: FFmpeg not installed, returning video with audio  
[2025-10-02 02:56:46] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/xwdiruhduwak"} 
[2025-10-02 02:56:47] local.INFO: Video operation completed {"operation_id":"models/veo-3.0-generate-001/operations/xwdiruhduwak"} 
[2025-10-02 02:56:47] local.INFO: Downloading video from Veo {"uri":"https://generativelanguage.googleapis.com/v1beta/files/7gk0ecmbbucp:download?alt=media"} 
[2025-10-02 02:56:48] local.INFO: Silent video saved successfully {"path":"silent-videos/silent-video-1759373808-5742.mp4","url":"http://127.0.0.1:8000/storage/silent-videos/silent-video-1759373808-5742.mp4","size":1654936} 
[2025-10-02 02:56:48] local.WARNING: FFmpeg not installed, returning video with audio  
[2025-10-02 02:57:41] local.INFO: Starting Step 3: Generate silent video {"concept_length":43,"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372057.wav","duration":8,"style":"cinematic","async":true} 
[2025-10-02 02:57:41] local.INFO: Estimated audio duration from file size {"file_size":209850,"estimated_duration":5} 
[2025-10-02 02:57:41] local.INFO: Using actual audio duration {"duration":5} 
[2025-10-02 02:57:41] local.INFO: Built video prompt {"prompt_length":476,"style":"cinematic","duration":5} 
[2025-10-02 02:57:41] local.INFO: Starting async video generation with Veo {"prompt_length":476,"duration":5} 
[2025-10-02 02:57:43] local.INFO: Async Veo operation started {"operation":"models/veo-3.0-generate-001/operations/vzujxmrset3u"} 
[2025-10-02 02:58:39] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/vzujxmrset3u"} 
[2025-10-02 02:58:40] local.INFO: Video operation completed {"operation_id":"models/veo-3.0-generate-001/operations/vzujxmrset3u"} 
[2025-10-02 02:58:40] local.ERROR: No video URI found in response {"response":{"@type":"type.googleapis.com/google.ai.generativelanguage.v1beta.PredictLongRunningResponse","generateVideoResponse":{"raiMediaFilteredCount":1,"raiMediaFilteredReasons":["We encountered an issue with the audio for your prompt, which means we could not create your video. This can sometimes happen due to our safety filters or other processing issues. Please modify your request and try again. You have not been charged for this attempt."]}}} 
[2025-10-02 02:58:40] local.ERROR: Video status check error: No video URI in completed operation  
[2025-10-02 02:58:56] local.INFO: Starting Step 3: Generate silent video {"concept_length":67,"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372057.wav","duration":8,"style":"clean","async":true} 
[2025-10-02 02:58:56] local.INFO: Estimated audio duration from file size {"file_size":209850,"estimated_duration":5} 
[2025-10-02 02:58:56] local.INFO: Using actual audio duration {"duration":5} 
[2025-10-02 02:58:56] local.INFO: Built video prompt {"prompt_length":464,"style":"clean","duration":5} 
[2025-10-02 02:58:56] local.INFO: Starting async video generation with Veo {"prompt_length":464,"duration":5} 
[2025-10-02 02:58:57] local.INFO: Async Veo operation started {"operation":"models/veo-3.0-generate-001/operations/abhyzs7bcsqt"} 
[2025-10-02 02:59:34] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/abhyzs7bcsqt"} 
[2025-10-02 02:59:40] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/abhyzs7bcsqt"} 
[2025-10-02 03:01:15] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/abhyzs7bcsqt"} 
[2025-10-02 03:01:16] local.INFO: Video operation completed {"operation_id":"models/veo-3.0-generate-001/operations/abhyzs7bcsqt"} 
[2025-10-02 03:01:16] local.INFO: Downloading video from Veo {"uri":"https://generativelanguage.googleapis.com/v1beta/files/1r1otpw1bi28:download?alt=media"} 
[2025-10-02 03:01:18] local.INFO: Silent video saved successfully {"path":"silent-videos/silent-video-1759374078-4941.mp4","url":"http://127.0.0.1:8000/storage/silent-videos/silent-video-1759374078-4941.mp4","size":1731598} 
[2025-10-02 03:01:18] local.INFO: FFmpeg found {"path":"C:\\ffmpeg\\bin\\ffmpeg.exe"} 
[2025-10-02 03:01:18] local.INFO: Removing audio from video {"command":"C:\\ffmpeg\\bin\\ffmpeg.exe -i \"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/silent-videos/silent-video-1759374078-4941.mp4\" -c:v copy -an \"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/silent-videos/silent-1759374078-2810.mp4\" 2>&1","original_file":"silent-video-1759374078-4941.mp4","silent_file":"silent-1759374078-2810.mp4"} 
[2025-10-02 03:01:18] local.INFO: Original video with audio deleted {"file":"silent-video-1759374078-4941.mp4"} 
[2025-10-02 03:01:18] local.INFO: Audio removed successfully {"silent_video_url":"http://127.0.0.1:8000/storage/silent-videos/silent-1759374078-2810.mp4","file_size":1581231} 
[2025-10-02 03:02:06] local.INFO: Starting Step 4: Combine video with audio {"video_url":"http://127.0.0.1:8000/storage/silent-videos/silent-1759374078-2810.mp4","audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759372057.wav","duration":8} 
[2025-10-02 03:02:06] local.WARNING: Could not parse media duration {"file":"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/silent-videos/silent-1759374078-2810.mp4","output":null} 
[2025-10-02 03:02:06] local.WARNING: Could not parse media duration {"file":"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/video-voice-overs/video-voice-over-1759372057.wav","output":null} 
[2025-10-02 03:02:06] local.INFO: Media durations {"video_duration":8,"audio_duration":8} 
[2025-10-02 03:02:06] local.INFO: Combining video and audio {"command":"C:\\ffmpeg\\bin\\ffmpeg.exe -i \"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/silent-videos/silent-1759374078-2810.mp4\" -i \"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/video-voice-overs/video-voice-over-1759372057.wav\" -t 8 -c:v copy -c:a aac -b:a 128k \"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/final-videos/final-video-1759374126-5654.mp4\" -y 2>&1","output_file":"final-video-1759374126-5654.mp4"} 
[2025-10-02 03:02:07] local.INFO: Video and audio combined successfully {"final_video_url":"http://127.0.0.1:8000/storage/final-videos/final-video-1759374126-5654.mp4","file_size":1646952,"output_path":"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/final-videos/final-video-1759374126-5654.mp4"} 
[2025-10-02 03:02:07] local.INFO: Step 4 completed successfully {"final_video_url":"http://127.0.0.1:8000/storage/final-videos/final-video-1759374126-5654.mp4","duration":8} 
[2025-10-02 03:02:43] local.INFO: Starting Step 1: Generate narasi + konsep {"product":"Kopi Arabica Premium","purpose":"marketing","duration":10,"tone":"friendly"} 
[2025-10-02 03:02:53] local.INFO: Step 1 completed successfully {"concept_length":567,"narration_length":229,"duration":10} 
[2025-10-02 03:03:00] local.INFO: Starting Step 2: Generate voice over {"narration_length":154,"language":"id-ID","gender":"female","tone":"friendly"} 
[2025-10-02 03:03:13] local.INFO: Gemini TTS API response received {"has_candidates":true,"response_keys":["candidates","usageMetadata","modelVersion","responseId"]} 
[2025-10-02 03:03:13] local.INFO: Video voice over generated successfully: http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759374193.wav  
[2025-10-02 03:03:13] local.INFO: Step 2 completed successfully {"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759374193.wav"} 
[2025-10-02 03:03:22] local.INFO: Starting Step 3: Generate silent video {"concept_length":165,"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759374193.wav","duration":10,"style":"modern","async":true} 
[2025-10-02 03:03:22] local.INFO: Estimated audio duration from file size {"file_size":499770,"estimated_duration":9.9954} 
[2025-10-02 03:03:22] local.INFO: Using actual audio duration {"duration":10} 
[2025-10-02 03:03:22] local.INFO: Built video prompt {"prompt_length":480,"style":"modern","duration":10} 
[2025-10-02 03:03:22] local.INFO: Starting async video generation with Veo {"prompt_length":480,"duration":10} 
[2025-10-02 03:03:24] local.INFO: Async Veo operation started {"operation":"models/veo-3.0-generate-001/operations/uyhop36jz939"} 
[2025-10-02 03:18:54] local.INFO: Starting Step 1: Generate narasi + konsep {"product":"Aplikasi Kasir Digital Usaria","purpose":"promo","duration":30,"tone":"friendly"} 
[2025-10-02 03:19:03] local.INFO: Step 1 completed successfully {"concept_length":749,"narration_length":511,"duration":30} 
[2025-10-02 03:19:28] local.INFO: Starting Step 2: Generate voice over {"narration_length":511,"language":"id-ID","gender":"female","tone":"friendly"} 
[2025-10-02 03:20:01] local.INFO: Gemini TTS API response received {"has_candidates":true,"response_keys":["candidates","usageMetadata","modelVersion","responseId"]} 
[2025-10-02 03:20:01] local.INFO: Video voice over generated successfully: http://localhost:8000/storage/video-voice-overs/video-voice-over-1759375201.wav  
[2025-10-02 03:20:01] local.INFO: Step 2 completed successfully {"audio_url":"http://localhost:8000/storage/video-voice-overs/video-voice-over-1759375201.wav"} 
[2025-10-02 03:23:29] local.INFO: Starting Step 1: Generate narasi + konsep {"product":"Kopi Arabica Premium","purpose":"marketing","duration":10,"tone":"friendly"} 
[2025-10-02 03:23:41] local.INFO: Step 1 completed successfully {"concept_length":738,"narration_length":221,"duration":10} 
[2025-10-02 03:23:41] local.INFO: Starting Step 1: Generate narasi + konsep {"product":"Kopi Arabica Premium","purpose":"marketing","duration":10,"tone":"friendly"} 
[2025-10-02 03:23:51] local.INFO: Step 1 completed successfully {"concept_length":609,"narration_length":192,"duration":10} 
[2025-10-02 03:23:51] local.INFO: Starting Step 1: Generate narasi + konsep {"product":"Aplikasi Kasir Digital Usaria","purpose":"marketing","duration":30,"tone":"friendly"} 
[2025-10-02 03:24:02] local.INFO: Step 1 completed successfully {"concept_length":680,"narration_length":416,"duration":30} 
[2025-10-02 03:24:02] local.INFO: Starting Step 2: Generate voice over {"narration_length":117,"language":"id-ID","gender":"female","tone":"friendly"} 
[2025-10-02 03:24:15] local.INFO: Gemini TTS API response received {"has_candidates":true,"response_keys":["candidates","usageMetadata","modelVersion","responseId"]} 
[2025-10-02 03:24:15] local.INFO: Video voice over generated successfully: http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759375455.wav  
[2025-10-02 03:24:15] local.INFO: Step 2 completed successfully {"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759375455.wav"} 
[2025-10-02 03:24:15] local.INFO: Starting Step 2: Generate voice over {"narration_length":117,"language":"id-ID","gender":"female","tone":"friendly"} 
[2025-10-02 03:24:26] local.INFO: Gemini TTS API response received {"has_candidates":true,"response_keys":["candidates","usageMetadata","modelVersion","responseId"]} 
[2025-10-02 03:24:26] local.INFO: Video voice over generated successfully: http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759375466.wav  
[2025-10-02 03:24:26] local.INFO: Step 2 completed successfully {"audio_url":"http://127.0.0.1:8000/storage/video-voice-overs/video-voice-over-1759375466.wav"} 
[2025-10-02 03:24:27] local.INFO: Starting Step 2: Generate voice over {"narration_length":416,"language":"id-ID","gender":"female","tone":"friendly"} 
[2025-10-02 03:24:52] local.INFO: Gemini TTS API response received {"has_candidates":true,"response_keys":["candidates","usageMetadata","modelVersion","responseId"]} 
[2025-10-02 03:24:52] local.INFO: Video voice over generated successfully: http://localhost:8000/storage/video-voice-overs/video-voice-over-1759375492.wav  
[2025-10-02 03:24:52] local.INFO: Step 2 completed successfully {"audio_url":"http://localhost:8000/storage/video-voice-overs/video-voice-over-1759375492.wav"} 
[2025-10-02 03:25:32] local.INFO: Starting Step 2: Generate voice over {"narration_length":117,"language":"id-ID","gender":"female","tone":"friendly"} 
[2025-10-02 03:26:42] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:695)
[stacktrace]
#0 {main}
"} 
[2025-10-02 03:26:45] local.INFO: Starting Step 3: Generate silent video {"concept_length":680,"audio_url":"http://localhost:8000/storage/video-voice-overs/video-voice-over-1759375492.wav","duration":30,"style":"modern","async":true} 
[2025-10-02 03:26:46] local.INFO: Estimated audio duration from file size {"file_size":1173690,"estimated_duration":23.4738} 
[2025-10-02 03:26:46] local.INFO: Using actual audio duration {"duration":24} 
[2025-10-02 03:26:46] local.INFO: Built video prompt {"prompt_length":489,"style":"modern","duration":24} 
[2025-10-02 03:26:46] local.INFO: Starting async video generation with Veo {"prompt_length":489,"duration":24} 
[2025-10-02 03:26:47] local.INFO: Async Veo operation started {"operation":"models/veo-3.0-generate-001/operations/3sny4ebm8txd"} 
[2025-10-02 03:26:48] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/3sny4ebm8txd"} 
[2025-10-02 03:26:59] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/3sny4ebm8txd"} 
[2025-10-02 03:27:10] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/3sny4ebm8txd"} 
[2025-10-02 03:27:21] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/3sny4ebm8txd"} 
[2025-10-02 03:27:32] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/3sny4ebm8txd"} 
[2025-10-02 03:27:43] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/3sny4ebm8txd"} 
[2025-10-02 03:27:54] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/3sny4ebm8txd"} 
[2025-10-02 03:27:54] local.INFO: Video operation completed {"operation_id":"models/veo-3.0-generate-001/operations/3sny4ebm8txd"} 
[2025-10-02 03:27:54] local.ERROR: No video URI found in response {"response":{"@type":"type.googleapis.com/google.ai.generativelanguage.v1beta.PredictLongRunningResponse","generateVideoResponse":{"raiMediaFilteredCount":1,"raiMediaFilteredReasons":["We encountered an issue with the audio for your prompt, which means we could not create your video. This can sometimes happen due to our safety filters or other processing issues. Please modify your request and try again. You have not been charged for this attempt."]}}} 
[2025-10-02 03:27:54] local.ERROR: Video status check error: No video URI in completed operation  
[2025-10-02 03:28:05] local.INFO: Starting Step 3: Generate silent video {"concept_length":680,"audio_url":"http://localhost:8000/storage/video-voice-overs/video-voice-over-1759375492.wav","duration":30,"style":"modern","async":true} 
[2025-10-02 03:28:05] local.INFO: Estimated audio duration from file size {"file_size":1173690,"estimated_duration":23.4738} 
[2025-10-02 03:28:05] local.INFO: Using actual audio duration {"duration":24} 
[2025-10-02 03:28:05] local.INFO: Built video prompt {"prompt_length":489,"style":"modern","duration":24} 
[2025-10-02 03:28:05] local.INFO: Starting async video generation with Veo {"prompt_length":489,"duration":24} 
[2025-10-02 03:28:07] local.INFO: Async Veo operation started {"operation":"models/veo-3.0-generate-001/operations/bj40iqyurb2s"} 
[2025-10-02 03:28:07] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/bj40iqyurb2s"} 
[2025-10-02 03:28:18] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/bj40iqyurb2s"} 
[2025-10-02 03:28:29] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/bj40iqyurb2s"} 
[2025-10-02 03:28:40] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/bj40iqyurb2s"} 
[2025-10-02 03:28:51] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/bj40iqyurb2s"} 
[2025-10-02 03:29:02] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/bj40iqyurb2s"} 
[2025-10-02 03:29:13] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/bj40iqyurb2s"} 
[2025-10-02 03:29:24] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/bj40iqyurb2s"} 
[2025-10-02 03:29:25] local.INFO: Video operation completed {"operation_id":"models/veo-3.0-generate-001/operations/bj40iqyurb2s"} 
[2025-10-02 03:29:25] local.ERROR: No video URI found in response {"response":{"@type":"type.googleapis.com/google.ai.generativelanguage.v1beta.PredictLongRunningResponse","generateVideoResponse":{"raiMediaFilteredCount":1,"raiMediaFilteredReasons":["We encountered an issue with the audio for your prompt, which means we could not create your video. This can sometimes happen due to our safety filters or other processing issues. Please modify your request and try again. You have not been charged for this attempt."]}}} 
[2025-10-02 03:29:25] local.ERROR: Video status check error: No video URI in completed operation  
[2025-10-02 03:30:31] local.INFO: Starting Step 2: Generate voice over {"narration_length":117,"language":"id-ID","gender":"female","tone":"friendly"} 
[2025-10-02 03:30:42] local.INFO: Gemini TTS API response received {"has_candidates":true,"response_keys":["candidates","usageMetadata","modelVersion","responseId"]} 
[2025-10-02 03:30:42] local.INFO: Video voice over generated successfully: http://127.0.0.1:8000/api/storage/video-voice-overs/video-voice-over-1759375842.wav  
[2025-10-02 03:30:42] local.INFO: Step 2 completed successfully {"audio_url":"http://127.0.0.1:8000/api/storage/video-voice-overs/video-voice-over-1759375842.wav"} 
[2025-10-02 03:31:44] local.INFO: Starting Step 3: Generate silent video {"concept_length":680,"audio_url":"http://localhost:8000/storage/video-voice-overs/video-voice-over-1759375492.wav","duration":30,"style":"modern","async":true} 
[2025-10-02 03:31:44] local.INFO: Estimated audio duration from file size {"file_size":1173690,"estimated_duration":23.4738} 
[2025-10-02 03:31:44] local.INFO: Using actual audio duration {"duration":24} 
[2025-10-02 03:31:44] local.INFO: Built video prompt {"prompt_length":489,"style":"modern","duration":24} 
[2025-10-02 03:31:44] local.INFO: Starting async video generation with Veo {"prompt_length":489,"duration":24} 
[2025-10-02 03:31:46] local.INFO: Async Veo operation started {"operation":"models/veo-3.0-generate-001/operations/u9tewsts8n6h"} 
[2025-10-02 03:31:47] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/u9tewsts8n6h"} 
[2025-10-02 03:31:58] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/u9tewsts8n6h"} 
[2025-10-02 03:32:09] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/u9tewsts8n6h"} 
[2025-10-02 03:32:21] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/u9tewsts8n6h"} 
[2025-10-02 03:32:32] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/u9tewsts8n6h"} 
[2025-10-02 03:32:44] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/u9tewsts8n6h"} 
[2025-10-02 03:32:55] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/u9tewsts8n6h"} 
[2025-10-02 03:32:56] local.INFO: Video operation completed {"operation_id":"models/veo-3.0-generate-001/operations/u9tewsts8n6h"} 
[2025-10-02 03:32:56] local.ERROR: No video URI found in response {"response":{"@type":"type.googleapis.com/google.ai.generativelanguage.v1beta.PredictLongRunningResponse","generateVideoResponse":{"raiMediaFilteredCount":1,"raiMediaFilteredReasons":["We encountered an issue with the audio for your prompt, which means we could not create your video. This can sometimes happen due to our safety filters or other processing issues. Please modify your request and try again. You have not been charged for this attempt."]}}} 
[2025-10-02 03:32:56] local.ERROR: Video status check error: No video URI in completed operation  
[2025-10-02 03:36:38] local.INFO: Starting Step 1: Generate narasi + konsep {"product":"Kopi","purpose":"marketing","duration":10,"tone":"friendly"} 
[2025-10-02 03:36:48] local.INFO: Step 1 completed successfully {"concept_length":695,"narration_length":205,"duration":10} 
[2025-10-02 03:36:59] local.INFO: Starting Step 2: Generate voice over {"narration_length":130,"language":"id-ID","gender":"female","tone":"friendly"} 
[2025-10-02 03:37:12] local.INFO: Gemini TTS API response received {"has_candidates":true,"response_keys":["candidates","usageMetadata","modelVersion","responseId"]} 
[2025-10-02 03:37:12] local.INFO: Video voice over generated successfully: http://127.0.0.1:8000/api/storage/video-voice-overs/video-voice-over-1759376232.wav  
[2025-10-02 03:37:12] local.INFO: Step 2 completed successfully {"audio_url":"http://127.0.0.1:8000/api/storage/video-voice-overs/video-voice-over-1759376232.wav"} 
[2025-10-02 03:37:23] local.INFO: Starting Step 3: Generate silent video {"concept_length":188,"audio_url":"http://127.0.0.1:8000/api/storage/video-voice-overs/video-voice-over-1759376232.wav","duration":10,"style":"modern","async":true} 
[2025-10-02 03:37:23] local.INFO: Estimated audio duration from file size {"file_size":451770,"estimated_duration":9.0354} 
[2025-10-02 03:37:23] local.INFO: Using actual audio duration {"duration":10} 
[2025-10-02 03:37:23] local.INFO: Built video prompt {"prompt_length":367,"style":"modern","duration":10,"safe_product":"kopi"} 
[2025-10-02 03:37:23] local.INFO: Starting async video generation with Veo {"prompt_length":367,"duration":10} 
[2025-10-02 03:37:25] local.INFO: Async Veo operation started {"operation":"models/veo-3.0-generate-001/operations/pxx6pzd1r9vt"} 
[2025-10-02 03:38:45] local.INFO: Checking video operation status {"operation_id":"models/veo-3.0-generate-001/operations/pxx6pzd1r9vt"} 
[2025-10-02 03:38:46] local.INFO: Video operation completed {"operation_id":"models/veo-3.0-generate-001/operations/pxx6pzd1r9vt"} 
[2025-10-02 03:38:46] local.INFO: Downloading video from Veo {"uri":"https://generativelanguage.googleapis.com/v1beta/files/sr85818035q9:download?alt=media"} 
[2025-10-02 03:38:49] local.INFO: Silent video saved successfully {"path":"silent-videos/silent-video-1759376329-9763.mp4","url":"http://127.0.0.1:8000/api/storage/silent-videos/silent-video-1759376329-9763.mp4","size":1622276} 
[2025-10-02 03:38:49] local.INFO: FFmpeg found {"path":"C:\\ffmpeg\\bin\\ffmpeg.exe"} 
[2025-10-02 03:38:49] local.INFO: Removing audio from video {"command":"C:\\ffmpeg\\bin\\ffmpeg.exe -i \"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/silent-videos/silent-video-1759376329-9763.mp4\" -c:v copy -an \"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/silent-videos/silent-1759376329-6230.mp4\" 2>&1","original_file":"silent-video-1759376329-9763.mp4","silent_file":"silent-1759376329-6230.mp4"} 
[2025-10-02 03:38:49] local.INFO: Original video with audio deleted {"file":"silent-video-1759376329-9763.mp4"} 
[2025-10-02 03:38:49] local.INFO: Audio removed successfully {"silent_video_url":"http://127.0.0.1:8000/api/storage/silent-videos/silent-1759376329-6230.mp4","file_size":1471736} 
[2025-10-02 03:38:57] local.INFO: Starting Step 4: Combine video with audio {"video_url":"http://127.0.0.1:8000/api/storage/silent-videos/silent-1759376329-6230.mp4","audio_url":"http://127.0.0.1:8000/api/storage/video-voice-overs/video-voice-over-1759376232.wav","duration":10} 
[2025-10-02 03:38:57] local.WARNING: Could not parse media duration {"file":"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/silent-videos/silent-1759376329-6230.mp4","output":null} 
[2025-10-02 03:38:57] local.WARNING: Could not parse media duration {"file":"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/video-voice-overs/video-voice-over-1759376232.wav","output":null} 
[2025-10-02 03:38:57] local.INFO: Media durations {"video_duration":8,"audio_duration":8} 
[2025-10-02 03:38:57] local.INFO: Combining video and audio {"command":"C:\\ffmpeg\\bin\\ffmpeg.exe -i \"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/silent-videos/silent-1759376329-6230.mp4\" -i \"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/video-voice-overs/video-voice-over-1759376232.wav\" -t 8 -c:v copy -c:a aac -b:a 128k \"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/final-videos/final-video-1759376337-8212.mp4\" -y 2>&1","output_file":"final-video-1759376337-8212.mp4"} 
[2025-10-02 03:38:57] local.INFO: Video and audio combined successfully {"final_video_url":"http://127.0.0.1:8000/api/storage/final-videos/final-video-1759376337-8212.mp4","file_size":1583231,"output_path":"C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\storage\\app/public/final-videos/final-video-1759376337-8212.mp4"} 
[2025-10-02 03:38:57] local.INFO: Step 4 completed successfully {"final_video_url":"http://127.0.0.1:8000/api/storage/final-videos/final-video-1759376337-8212.mp4","duration":10} 
[2025-10-02 03:39:21] local.INFO: Starting Step 3: Generate silent video {"concept_length":680,"audio_url":"http://localhost:8000/storage/video-voice-overs/video-voice-over-1759375492.wav","duration":30,"style":"modern","async":true} 
[2025-10-02 03:39:21] local.INFO: Estimated audio duration from file size {"file_size":1173690,"estimated_duration":23.4738} 
[2025-10-02 03:39:21] local.INFO: Using actual audio duration {"duration":24} 
[2025-10-02 03:39:21] local.INFO: Built video prompt {"prompt_length":392,"style":"modern","duration":24,"safe_product":"aplikasi kasir digital usaria"} 
[2025-10-02 03:39:21] local.INFO: Starting async video generation with Veo {"prompt_length":392,"duration":24} 
[2025-10-02 03:39:22] local.ERROR: Veo API request failed {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\"
  }
}
"} 
[2025-10-02 03:39:22] local.ERROR: Async video generation error: Veo API request failed: {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED"
  }
}
  
[2025-10-02 03:39:22] local.ERROR: Step 3 error: Veo API request failed: {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED"
  }
}
  
[2025-10-02 03:39:59] local.INFO: Starting Step 1: Generate narasi + konsep {"product":"Aplikasi Kasir Digital Usaria","purpose":"marketing","duration":30,"tone":"friendly"} 
[2025-10-02 03:40:07] local.INFO: Step 1 completed successfully {"concept_length":733,"narration_length":506,"duration":30} 
[2025-10-02 03:40:15] local.INFO: Starting Step 2: Generate voice over {"narration_length":506,"language":"id-ID","gender":"female","tone":"friendly"} 
[2025-10-02 03:40:42] local.INFO: Gemini TTS API response received {"has_candidates":true,"response_keys":["candidates","usageMetadata","modelVersion","responseId"]} 
[2025-10-02 03:40:42] local.INFO: Video voice over generated successfully: http://localhost:8000/api/storage/video-voice-overs/video-voice-over-1759376442.wav  
[2025-10-02 03:40:42] local.INFO: Step 2 completed successfully {"audio_url":"http://localhost:8000/api/storage/video-voice-overs/video-voice-over-1759376442.wav"} 
[2025-10-02 03:41:13] local.INFO: Starting Step 3: Generate silent video {"concept_length":733,"audio_url":"http://localhost:8000/api/storage/video-voice-overs/video-voice-over-1759376442.wav","duration":30,"style":"modern","async":true} 
[2025-10-02 03:41:13] local.INFO: Estimated audio duration from file size {"file_size":1371450,"estimated_duration":27.429} 
[2025-10-02 03:41:13] local.INFO: Using actual audio duration {"duration":28} 
[2025-10-02 03:41:13] local.INFO: Built video prompt {"prompt_length":392,"style":"modern","duration":28,"safe_product":"aplikasi kasir digital usaria"} 
[2025-10-02 03:41:13] local.INFO: Starting async video generation with Veo {"prompt_length":392,"duration":28} 
[2025-10-02 03:41:14] local.ERROR: Veo API request failed {"status":429,"body":"{
  \"error\": {
    \"code\": 429,
    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",
    \"status\": \"RESOURCE_EXHAUSTED\"
  }
}
"} 
[2025-10-02 03:41:14] local.ERROR: Async video generation error: Veo API request failed: {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED"
  }
}
  
[2025-10-02 03:41:14] local.ERROR: Step 3 error: Veo API request failed: {
  "error": {
    "code": 429,
    "message": "You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.",
    "status": "RESOURCE_EXHAUSTED"
  }
}
  
[2025-10-02 04:10:22] local.INFO: Starting Step 1: Generate narasi + konsep {"product":"Aplikasi Kasir Digital Usaria","purpose":"marketing","duration":30,"tone":"friendly"} 
[2025-10-02 04:10:34] local.INFO: Step 1 completed successfully {"concept_length":746,"narration_length":466,"duration":30} 
[2025-10-02 04:39:21] local.INFO: Starting Step 1: Generate narasi + konsep {"product":"Aplikasi Kasir Digital Usaria","purpose":"marketing","duration":30,"tone":"friendly"} 
[2025-10-02 04:39:31] local.INFO: Step 1 completed successfully {"concept_length":727,"narration_length":515,"duration":30} 
[2025-10-02 04:39:33] local.INFO: Starting Step 2: Generate voice over {"narration_length":515,"language":"id-ID","gender":"female","tone":"friendly"} 
[2025-10-02 04:40:04] local.INFO: Gemini TTS API response received {"has_candidates":true,"response_keys":["candidates","usageMetadata","modelVersion","responseId"]} 
[2025-10-02 04:40:04] local.INFO: Video voice over generated successfully: http://localhost:8000/api/storage/video-voice-overs/video-voice-over-1759380004.wav  
[2025-10-02 04:40:04] local.INFO: Step 2 completed successfully {"audio_url":"http://localhost:8000/api/storage/video-voice-overs/video-voice-over-1759380004.wav"} 
[2025-10-02 04:45:49] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sessions' already exists (Connection: mysql, SQL: create table `sessions` (`id` varchar(255) not null, `user_id` bigint unsigned null, `ip_address` varchar(45) null, `user_agent` text null, `payload` longtext not null, `last_activity` int not null, primary key (`id`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sessions' already exists (Connection: mysql, SQL: create table `sessions` (`id` varchar(255) not null, `user_id` bigint unsigned null, `ip_address` varchar(45) null, `user_agent` text null, `payload` longtext not null, `last_activity` int not null, primary key (`id`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('create table `s...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run('create table `s...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(121): Illuminate\\Database\\Connection->statement('create table `s...')
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(363): Illuminate\\Database\\Schema\\Builder->create('sessions', Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\database\\migrations\\2025_10_02_044526_create_sessions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(809): Illuminate\\Console\\View\\Components\\Task->render('2025_10_02_0445...', Object(Closure))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_10_02_0445...', Object(Closure))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp-8.2-ne...', 2, false)
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(666): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sessions' already exists at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:570)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(570): PDOStatement->execute()
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `s...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('create table `s...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run('create table `s...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(121): Illuminate\\Database\\Connection->statement('create table `s...')
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(363): Illuminate\\Database\\Schema\\Builder->create('sessions', Object(Closure))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\database\\migrations\\2025_10_02_044526_create_sessions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(809): Illuminate\\Console\\View\\Components\\Task->render('2025_10_02_0445...', Object(Closure))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_10_02_0445...', Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp-8.2-ne...', 2, false)
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(666): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-10-02 05:56:35] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sessions' already exists (Connection: mysql, SQL: create table `sessions` (`id` varchar(255) not null, `user_id` bigint unsigned null, `ip_address` varchar(45) null, `user_agent` text null, `payload` longtext not null, `last_activity` int not null, primary key (`id`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sessions' already exists (Connection: mysql, SQL: create table `sessions` (`id` varchar(255) not null, `user_id` bigint unsigned null, `ip_address` varchar(45) null, `user_agent` text null, `payload` longtext not null, `last_activity` int not null, primary key (`id`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('create table `s...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run('create table `s...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(121): Illuminate\\Database\\Connection->statement('create table `s...')
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(363): Illuminate\\Database\\Schema\\Builder->create('sessions', Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\database\\migrations\\2025_10_02_044526_create_sessions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(809): Illuminate\\Console\\View\\Components\\Task->render('2025_10_02_0445...', Object(Closure))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_10_02_0445...', Object(Closure))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp-8.2-ne...', 2, false)
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(666): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sessions' already exists at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:570)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(570): PDOStatement->execute()
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `s...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('create table `s...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run('create table `s...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(121): Illuminate\\Database\\Connection->statement('create table `s...')
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(363): Illuminate\\Database\\Schema\\Builder->create('sessions', Object(Closure))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\database\\migrations\\2025_10_02_044526_create_sessions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(809): Illuminate\\Console\\View\\Components\\Task->render('2025_10_02_0445...', Object(Closure))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_10_02_0445...', Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp-8.2-ne...', 2, false)
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(666): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-10-02 05:56:56] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sessions' already exists (Connection: mysql, SQL: create table `sessions` (`id` varchar(255) not null, `user_id` bigint unsigned null, `ip_address` varchar(45) null, `user_agent` text null, `payload` longtext not null, `last_activity` int not null, primary key (`id`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sessions' already exists (Connection: mysql, SQL: create table `sessions` (`id` varchar(255) not null, `user_id` bigint unsigned null, `ip_address` varchar(45) null, `user_agent` text null, `payload` longtext not null, `last_activity` int not null, primary key (`id`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('create table `s...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run('create table `s...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(121): Illuminate\\Database\\Connection->statement('create table `s...')
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(363): Illuminate\\Database\\Schema\\Builder->create('sessions', Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\database\\migrations\\2025_10_02_044526_create_sessions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(809): Illuminate\\Console\\View\\Components\\Task->render('2025_10_02_0445...', Object(Closure))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_10_02_0445...', Object(Closure))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp-8.2-ne...', 1, false)
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(666): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#45 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sessions' already exists at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:570)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(570): PDOStatement->execute()
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `s...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('create table `s...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run('create table `s...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(121): Illuminate\\Database\\Connection->statement('create table `s...')
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(363): Illuminate\\Database\\Schema\\Builder->create('sessions', Object(Closure))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\database\\migrations\\2025_10_02_044526_create_sessions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(809): Illuminate\\Console\\View\\Components\\Task->render('2025_10_02_0445...', Object(Closure))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_10_02_0445...', Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp-8.2-ne...', 1, false)
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(666): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#47 {main}
"} 
[2025-10-02 05:58:41] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'users_email_unique' (Connection: mysql, SQL: insert into `users` (`name`, `email`, `password`, `email_verified_at`, `updated_at`, `created_at`) values (Test User, <EMAIL>, $2y$12$Wqw.Rkzqg3YzGLnYiycHOu0GviULSxpOVpa89gDc3vYdI9IC0WdHm, 2025-10-02 05:58:41, 2025-10-02 05:58:41, 2025-10-02 05:58:41)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'users_email_unique' (Connection: mysql, SQL: insert into `users` (`name`, `email`, `password`, `email_verified_at`, `updated_at`, `created_at`) values (Test User, <EMAIL>, $2y$12$Wqw.Rkzqg3YzGLnYiycHOu0GviULSxpOVpa89gDc3vYdI9IC0WdHm, 2025-10-02 05:58:41, 2025-10-02 05:58:41, 2025-10-02 05:58:41)) at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:819)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('insert into `us...', Array, Object(Closure))
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `us...', Array, Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `us...', Array, 'id')
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `us...', Array, 'id')
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2235): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1436): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1401): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1240): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1219): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(390): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1218): tap(Object(App\\Models\\User), Object(Closure))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2540): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2556): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\database\\seeders\\UserSeeder.php(26): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\UserSeeder->run()
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(59): Illuminate\\Database\\Seeder->__invoke(Array)
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#48 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'users_email_unique' at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `us...', Array)
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('insert into `us...', Array, Object(Closure))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `us...', Array, Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `us...', Array, 'id')
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `us...', Array, 'id')
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2235): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1436): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1401): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1240): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1219): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(390): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1218): tap(Object(App\\Models\\User), Object(Closure))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2540): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2556): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\database\\seeders\\UserSeeder.php(26): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\UserSeeder->run()
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(59): Illuminate\\Database\\Seeder->__invoke(Array)
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#50 {main}
"} 
[2025-10-02 07:13:07] local.ERROR: Session store not set on request. {"exception":"[object] (RuntimeException(code: 0): Session store not set on request. at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php:584)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Middleware\\HandleCorsSession.php(40): Illuminate\\Http\\Request->session()
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\HandleCorsSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#2 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Middleware\\StorageCors.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\StorageCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#37 {main}
"} 
[2025-10-03 02:38:35] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(871): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(278): route('login')
#2 [internal function]: Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(117): call_user_func(Object(Closure), Object(Illuminate\\Http\\Request))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(104): Illuminate\\Auth\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(87): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(61): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Middleware\\StorageCors.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\StorageCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#41 {main}
"} 
[2025-10-03 02:38:52] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(871): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(278): route('login')
#2 [internal function]: Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(117): call_user_func(Object(Closure), Object(Illuminate\\Http\\Request))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(104): Illuminate\\Auth\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(87): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(61): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Middleware\\StorageCors.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\StorageCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#41 {main}
"} 
[2025-10-03 02:38:56] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(871): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(278): route('login')
#2 [internal function]: Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(117): call_user_func(Object(Closure), Object(Illuminate\\Http\\Request))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(104): Illuminate\\Auth\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(87): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(61): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Middleware\\StorageCors.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\StorageCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#41 {main}
"} 
[2025-10-03 02:39:00] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(871): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(278): route('login')
#2 [internal function]: Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(117): call_user_func(Object(Closure), Object(Illuminate\\Http\\Request))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(104): Illuminate\\Auth\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(87): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(61): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Middleware\\StorageCors.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\StorageCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#41 {main}
"} 
[2025-10-03 02:39:07] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(871): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(278): route('login')
#2 [internal function]: Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(117): call_user_func(Object(Closure), Object(Illuminate\\Http\\Request))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(104): Illuminate\\Auth\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(87): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(61): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Middleware\\StorageCors.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\StorageCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#41 {main}
"} 
[2025-10-03 02:39:10] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(871): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(278): route('login')
#2 [internal function]: Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(117): call_user_func(Object(Closure), Object(Illuminate\\Http\\Request))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(104): Illuminate\\Auth\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(87): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(61): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Middleware\\StorageCors.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\StorageCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#41 {main}
"} 
[2025-10-03 02:39:41] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(871): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(278): route('login')
#2 [internal function]: Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(117): call_user_func(Object(Closure), Object(Illuminate\\Http\\Request))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(104): Illuminate\\Auth\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(87): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(61): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Middleware\\StorageCors.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\StorageCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#41 {main}
"} 
[2025-10-03 02:39:48] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(871): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(278): route('login')
#2 [internal function]: Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(117): call_user_func(Object(Closure), Object(Illuminate\\Http\\Request))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(104): Illuminate\\Auth\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(87): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(61): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Middleware\\StorageCors.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\StorageCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#41 {main}
"} 
[2025-10-03 03:52:45] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(871): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(278): route('login')
#2 [internal function]: Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(117): call_user_func(Object(Closure), Object(Illuminate\\Http\\Request))
#4 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(104): Illuminate\\Auth\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#5 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(87): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#6 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(61): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#7 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#8 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\app\\Http\\Middleware\\StorageCors.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\StorageCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#40 C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp-8.2-ne...')
#41 {main}
"} 
[2025-10-09 08:32:47] local.ERROR: Gemini API request failed: {
  "error": {
    "code": 404,
    "message": "models/gemini-1.5-flash is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}
  
[2025-10-09 08:32:47] local.ERROR: Script generation error: Gemini API request failed: {
  "error": {
    "code": 404,
    "message": "models/gemini-1.5-flash is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}
  
[2025-10-09 08:32:47] local.ERROR: Voice generation error: Gemini API request failed: {
  "error": {
    "code": 404,
    "message": "models/gemini-1.5-flash is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}
  
[2025-10-09 08:33:06] local.ERROR: Gemini API request failed: {
  "error": {
    "code": 404,
    "message": "models/gemini-1.5-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}
  
[2025-10-09 08:33:06] local.ERROR: Script generation error: Gemini API request failed: {
  "error": {
    "code": 404,
    "message": "models/gemini-1.5-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}
  
[2025-10-09 08:33:06] local.ERROR: Voice generation error: Gemini API request failed: {
  "error": {
    "code": 404,
    "message": "models/gemini-1.5-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}
  
[2025-10-09 08:34:42] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\xampp-8.2-new\\htdocs\\santuygrow\\back-end\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:695)
[stacktrace]
#0 {main}
"} 
