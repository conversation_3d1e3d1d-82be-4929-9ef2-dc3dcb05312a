<?php

namespace App\Http\Controllers;

use App\Models\Worksheet;
use Illuminate\Http\Request;

class WorksheetController extends Controller
{
    public function index()
    {
        return response()->json(Worksheet::orderBy('created_at', 'desc')->get());
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $ws = Worksheet::create($validated);
        return response()->json($ws, 201);
    }

    public function update(Request $request, Worksheet $worksheet)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $worksheet->update($validated);
        return response()->json($worksheet);
    }

    public function destroy(Worksheet $worksheet)
    {
        $worksheet->delete();
        return response()->json([ 'deleted' => true ]);
    }
}

