<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TestimonialController extends Controller
{
    public function generateTestimonial(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'productName' => 'required|string|max:255',
                'description' => 'required|string|max:1000'
            ]);

            $productName = $request->input('productName');
            $description = $request->input('description');

            // OpenAI API configuration
            $openaiApiKey = env('OPENAI_API_KEY');

            if (!$openaiApiKey) {
                throw new \Exception('OpenAI API key not configured');
            }

            // Create prompt for OpenAI
            $prompt = $this->createTestimonialPrompt($productName, $description);

            // Call OpenAI API
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $openaiApiKey,
                'Content-Type' => 'application/json',
            ])->timeout(30)->post('https://api.openai.com/v1/chat/completions', [
                'model' => 'gpt-3.5-turbo',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'Anda adalah asisten yang ahli dalam membuat testimoni WhatsApp yang natural dan meyakinkan dalam bahasa Indonesia. Buatlah percakapan testimoni yang terlihat autentik antara pelanggan dan bisnis.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => 1000,
                'temperature' => 0.7,
            ]);

            if (!$response->successful()) {
                throw new \Exception('OpenAI API request failed: ' . $response->body());
            }

            $aiResponse = $response->json();
            $generatedText = $aiResponse['choices'][0]['message']['content'] ?? '';

            // Parse the AI response into structured messages
            $messages = $this->parseAiResponse($generatedText);

            return response()->json([
                'success' => true,
                'messages' => $messages
            ]);
        } catch (\Exception $e) {
            Log::error('Testimonial generation failed: ' . $e->getMessage());

            // Return fallback messages if API fails
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'messages' => $this->getFallbackMessages($productName ?? '', $description ?? '')
            ], 500);
        }
    }

    private function createTestimonialPrompt(string $productName, string $description): string
    {
        return "Buatkan percakapan testimoni WhatsApp yang natural antara pelanggan dan bisnis untuk produk '{$productName}' dengan deskripsi: '{$description}'.

PENTING: Pelanggan harus LANGSUNG memberikan testimoni positif, bukan bertanya tentang produk.

Contoh yang BENAR:
[CUSTOMER|14:30] Kak, {$productName}nya udah sampaiii! Enak banget, nagih!
[BUSINESS|14:31] Alhamdulillah, senang banget dengernya kak! Makasih ya udah order ❤️
[CUSTOMER|14:32] Iya dong! Pasti bakal order lagi nih, recommended banget!

Contoh yang SALAH:
[CUSTOMER|14:30] Hai, apakah ada {$productName} di menu kamu?

Format yang diinginkan:
1. Pelanggan LANGSUNG memberikan testimoni positif (sudah beli/pakai produk)
2. Bisnis merespons dengan senang dan berterima kasih
3. Pelanggan menambahkan komentar positif lagi atau rekomendasi

Buatlah dalam format:
[CUSTOMER|waktu] Testimoni positif pelanggan yang sudah menggunakan produk
[BUSINESS|waktu] Respons bisnis yang berterima kasih
[CUSTOMER|waktu] Komentar tambahan pelanggan

Gunakan waktu yang realistis (selisih 1-3 menit). Buatlah testimoni yang spesifik, natural, dan meyakinkan dalam bahasa Indonesia yang santai seperti chat WhatsApp. Pelanggan harus terdengar seperti sudah menggunakan/membeli produk dan memberikan review positif.";
    }

    private function parseAiResponse(string $response): array
    {
        $messages = [];
        $lines = explode("\n", $response);

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // Parse format: [SENDER|TIME] Message
            if (preg_match('/\[(CUSTOMER|BUSINESS)\|(\d{1,2}:\d{2})\]\s*(.+)/', $line, $matches)) {
                $sender = strtolower($matches[1]);
                $time = $matches[2];
                $text = trim($matches[3]);

                if (!empty($text)) {
                    $messages[] = [
                        'sender' => $sender,
                        'time' => $time,
                        'text' => $text
                    ];
                }
            }
        }

        // If parsing failed, return fallback
        if (empty($messages)) {
            return $this->getFallbackMessages('', '');
        }

        return $messages;
    }

    private function getFallbackMessages(string $productName, string $description): array
    {
        return [
            [
                'sender' => 'customer',
                'time' => '14:30',
                'text' => "Kak, {$productName}nya udah sampaiii! Enak banget, sesuai ekspektasi! 😍",
            ],
            [
                'sender' => 'business',
                'time' => '14:31',
                'text' => 'Alhamdulillah, senang banget dengernya kak! Makasih ya udah order ❤️',
            ],
            [
                'sender' => 'customer',
                'time' => '14:33',
                'text' => 'Iya dong! Pasti bakal order lagi nih, recommended banget buat temen-temen!',
            ]
        ];
    }
}
