import React, { useState, useRef } from 'react';
import {
  ChevronDown,
  ChevronUp,
  Plus,
  X,
  Download,
  Wand2,
  MessageSquare,
  Settings,
  Edit3,
  Smartphone,
  Wifi,
  Battery,
  Signal
} from 'lucide-react';
import html2canvas from 'html2canvas';

interface Message {
  id: string;
  sender: 'customer' | 'business';
  time: string;
  image?: File | null;
  text: string;
  status: 'sent' | 'delivered' | 'read';
}

const WATestimonialGeneratorPage: React.FC = () => {
  // Generate dengan AI
  const [productName, setProductName] = useState('');
  const [description, setDescription] = useState('');
  const [aiLoading, setAiLoading] = useState(false);

  // Pengaturan Tampilan
  const [phoneStyle, setPhoneStyle] = useState<'iphone' | 'samsung'>('iphone');
  const [theme, setTheme] = useState<'light' | 'dark'>('light');

  // Header Chat
  const [contactName, setContactName] = useState('+62 812-9156-2646');
  const [contactSubtext, setContactSubtext] = useState('tap here to add to contacts');
  const [contactPhoto, setContactPhoto] = useState<File | null>(null);
  const [customerPhoto, setCustomerPhoto] = useState<File | null>(null);

  // Status Bar HP
  const [statusTime, setStatusTime] = useState('09:41');
  const [network, setNetwork] = useState<'wifi' | '5g' | '4g'>('wifi');
  const [battery, setBattery] = useState(85);
  const [charging, setCharging] = useState(false);
  const [doNotDisturb, setDoNotDisturb] = useState(false);

  // Isi Chat
  const [dateSeparatorText, setDateSeparatorText] = useState('HARI INI');

  // Editor Pesan
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      sender: 'customer',
      time: '14:30',
      text: 'Halo kak, paketnya sudah sampai! Suka banget sama produknya, hasilnya keliatan!',
      status: 'read'
    },
    {
      id: '2',
      sender: 'business',
      time: '14:31',
      text: 'Alhamdulillah, terima kasih banyak kak! Senang mendengarnya ❤️ Kalau boleh tau, bagian mana yg paling kakak suka?',
      status: 'read'
    },
    {
      id: '3',
      sender: 'customer',
      time: '14:32',
      text: 'Iya, bahannya adem dan jahitannya rapi. Beneran ngebantu banget buat sehari-hari. Recommended!',
      status: 'read'
    }
  ]);

  // Frame HP
  const [includePhoneFrame, setIncludePhoneFrame] = useState(true);

  // Collapsible states
  const [aiCollapsed, setAiCollapsed] = useState(false);
  const [settingsCollapsed, setSettingsCollapsed] = useState(false);
  const [editorCollapsed, setEditorCollapsed] = useState(false);

  // File refs
  const contactPhotoRef = useRef<HTMLInputElement>(null);
  const customerPhotoRef = useRef<HTMLInputElement>(null);

  // Preview URLs
  const [contactPhotoPreview, setContactPhotoPreview] = useState<string | null>(null);
  const [customerPhotoPreview, setCustomerPhotoPreview] = useState<string | null>(null);

  // Ref for screenshot
  const previewRef = useRef<HTMLDivElement>(null);

  const handleGenerateTestimonial = async () => {
    setAiLoading(true);

    try {
      const response = await fetch('http://127.0.0.1:8000/api/generate-testimonial', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productName,
          description
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate testimonial');
      }

      const data = await response.json();

      // Parse the AI response and create messages
      const testimonialMessages: Message[] = data.messages.map((msg: any, index: number) => ({
        id: (Date.now() + index).toString(),
        sender: msg.sender,
        time: msg.time,
        text: msg.text,
        status: 'read' as const
      }));

      setMessages(testimonialMessages);
    } catch (error) {
      console.error('Error generating testimonial:', error);
      // Fallback to default messages if API fails
      const fallbackMessages: Message[] = [
        {
          id: Date.now().toString(),
          sender: 'customer',
          time: '10:30',
          text: `Halo, saya baru saja mencoba ${productName}. ${description}`,
          status: 'read'
        },
        {
          id: (Date.now() + 1).toString(),
          sender: 'business',
          time: '10:32',
          text: 'Terima kasih atas testimoninya! Kami senang produk kami bermanfaat untuk Anda 😊',
          status: 'read'
        },
        {
          id: (Date.now() + 2).toString(),
          sender: 'customer',
          time: '10:35',
          text: 'Pasti! Saya akan rekomendasikan ke teman-teman juga. Kualitasnya benar-benar bagus!',
          status: 'read'
        }
      ];
      setMessages(fallbackMessages);
    } finally {
      setAiLoading(false);
    }
  };

  const addMessage = () => {
    const newMessage: Message = {
      id: Date.now().toString(),
      sender: 'customer',
      time: '10:00',
      text: '',
      status: 'sent'
    };
    setMessages([...messages, newMessage]);
  };

  const updateMessage = (id: string, field: keyof Message, value: any) => {
    setMessages(messages.map(msg =>
      msg.id === id ? { ...msg, [field]: value } : msg
    ));
  };

  const getMessageImagePreview = (message: Message) => {
    if (message.image) {
      return URL.createObjectURL(message.image);
    }
    return null;
  };

  const deleteMessage = (id: string) => {
    setMessages(messages.filter(msg => msg.id !== id));
  };

  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownloadScreenshot = async () => {
    if (!previewRef.current || isDownloading) return;

    try {
      setIsDownloading(true);

      // Configure html2canvas options for better quality
      const canvas = await html2canvas(previewRef.current, {
        backgroundColor: null,
        scale: 2, // Higher resolution
        useCORS: true,
        allowTaint: true,
        logging: false,
        width: previewRef.current.scrollWidth,
        height: previewRef.current.scrollHeight,
      });

      // Convert canvas to blob
      canvas.toBlob((blob) => {
        if (blob) {
          // Create download link
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;

          // Generate filename with timestamp
          const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
          link.download = `wa-testimonial-${timestamp}.png`;

          // Trigger download
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Clean up
          URL.revokeObjectURL(url);
        }

        // Reset loading state after download
        setIsDownloading(false);
      }, 'image/png', 1.0);

    } catch (error) {
      console.error('Error capturing screenshot:', error);
      alert('Gagal mengunduh screenshot. Silakan coba lagi.');
      setIsDownloading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
            <MessageSquare className="w-6 h-6 text-green-600" />
            WA Testimonial Generator
          </h1>
          <p className="text-gray-600 mt-1">Buat screenshot testimoni WhatsApp yang menarik untuk promosi bisnis Anda</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Panel - Controls */}
          <div className="space-y-4">
            {/* Generate dengan AI */}
            <div className="bg-white border border-gray-200 rounded-lg">
              <button
                onClick={() => setAiCollapsed(!aiCollapsed)}
                className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
              >
                <div className="flex items-center gap-2">
                  <Wand2 className="w-4 h-4 text-purple-600" />
                  <span className="font-medium text-gray-800">Generate dengan AI</span>
                </div>
                {aiCollapsed ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
              </button>

              {!aiCollapsed && (
                <div className="p-4 pt-0 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nama Produk/Layanan
                    </label>
                    <input
                      type="text"
                      value={productName}
                      onChange={(e) => setProductName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Contoh: Kursus Digital Marketing"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Deskripsi Singkat
                    </label>
                    <textarea
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Jelaskan manfaat atau keunggulan produk Anda..."
                    />
                  </div>

                  <button
                    onClick={handleGenerateTestimonial}
                    disabled={aiLoading || !productName || !description}
                    className={`w-full flex items-center justify-center gap-2 px-4 py-2 rounded-md text-white ${aiLoading || !productName || !description
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-purple-600 hover:bg-purple-700'
                      }`}
                  >
                    <Wand2 className={`w-4 h-4 ${aiLoading ? 'animate-spin' : ''}`} />
                    {aiLoading ? 'Membuat Testimoni...' : 'Buatkan Testimoni'}
                  </button>
                </div>
              )}
            </div>

            {/* Pengaturan Tampilan */}
            <div className="bg-white border border-gray-200 rounded-lg">
              <button
                onClick={() => setSettingsCollapsed(!settingsCollapsed)}
                className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
              >
                <div className="flex items-center gap-2">
                  <Settings className="w-4 h-4 text-blue-600" />
                  <span className="font-medium text-gray-800">Pengaturan Tampilan</span>
                </div>
                {settingsCollapsed ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
              </button>

              {!settingsCollapsed && (
                <div className="p-4 pt-0 space-y-6">
                  {/* Gaya HP & Tema */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Gaya HP</label>
                      <div className="space-y-2">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="phoneStyle"
                            value="iphone"
                            checked={phoneStyle === 'iphone'}
                            onChange={(e) => setPhoneStyle(e.target.value as 'iphone')}
                            className="mr-2"
                          />
                          iPhone
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="phoneStyle"
                            value="samsung"
                            checked={phoneStyle === 'samsung'}
                            onChange={(e) => setPhoneStyle(e.target.value as 'samsung')}
                            className="mr-2"
                          />
                          Samsung
                        </label>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Tema</label>
                      <div className="space-y-2">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="theme"
                            value="light"
                            checked={theme === 'light'}
                            onChange={(e) => setTheme(e.target.value as 'light')}
                            className="mr-2"
                          />
                          Light
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="theme"
                            value="dark"
                            checked={theme === 'dark'}
                            onChange={(e) => setTheme(e.target.value as 'dark')}
                            className="mr-2"
                          />
                          Dark
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Header Chat */}
                  <div>
                    <h4 className="font-medium text-gray-800 mb-3">Header Chat</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Nama/Nomor Kontak
                        </label>
                        <input
                          type="text"
                          value={contactName}
                          onChange={(e) => setContactName(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                          placeholder="Nama kontak"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Subteks Kontak
                        </label>
                        <input
                          type="text"
                          value={contactSubtext}
                          onChange={(e) => setContactSubtext(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                          placeholder="online, terakhir dilihat, dll"
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Foto Profil Kontak
                          </label>
                          <input
                            ref={contactPhotoRef}
                            type="file"
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0] || null;
                              setContactPhoto(file);
                              if (file) {
                                setContactPhotoPreview(URL.createObjectURL(file));
                              } else {
                                setContactPhotoPreview(null);
                              }
                            }}
                            className="hidden"
                          />
                          <button
                            onClick={() => contactPhotoRef.current?.click()}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-600 hover:bg-gray-50"
                          >
                            {contactPhoto ? contactPhoto.name : 'Pilih foto'}
                          </button>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Foto Profil Pelanggan
                          </label>
                          <input
                            ref={customerPhotoRef}
                            type="file"
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0] || null;
                              setCustomerPhoto(file);
                              if (file) {
                                setCustomerPhotoPreview(URL.createObjectURL(file));
                              } else {
                                setCustomerPhotoPreview(null);
                              }
                            }}
                            className="hidden"
                          />
                          <button
                            onClick={() => customerPhotoRef.current?.click()}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-600 hover:bg-gray-50"
                          >
                            {customerPhoto ? customerPhoto.name : 'Pilih foto'}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Status Bar HP */}
                  <div>
                    <h4 className="font-medium text-gray-800 mb-3">Status Bar HP</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Waktu
                        </label>
                        <input
                          type="time"
                          value={statusTime}
                          onChange={(e) => setStatusTime(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Jaringan</label>
                        <div className="flex gap-4">
                          <label className="flex items-center">
                            <input
                              type="radio"
                              name="network"
                              value="wifi"
                              checked={network === 'wifi'}
                              onChange={(e) => setNetwork(e.target.value as 'wifi')}
                              className="mr-2"
                            />
                            WiFi
                          </label>
                          <label className="flex items-center">
                            <input
                              type="radio"
                              name="network"
                              value="5g"
                              checked={network === '5g'}
                              onChange={(e) => setNetwork(e.target.value as '5g')}
                              className="mr-2"
                            />
                            5G
                          </label>
                          <label className="flex items-center">
                            <input
                              type="radio"
                              name="network"
                              value="4g"
                              checked={network === '4g'}
                              onChange={(e) => setNetwork(e.target.value as '4g')}
                              className="mr-2"
                            />
                            4G
                          </label>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Baterai (%)
                        </label>
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={battery}
                          onChange={(e) => setBattery(Number(e.target.value))}
                          className="w-full"
                        />
                        <div className="text-sm text-gray-600 mt-1">{battery}%</div>
                      </div>

                      <div className="space-y-2">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={charging}
                            onChange={(e) => setCharging(e.target.checked)}
                            className="mr-2"
                          />
                          Isi Daya?
                        </label>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={doNotDisturb}
                            onChange={(e) => setDoNotDisturb(e.target.checked)}
                            className="mr-2"
                          />
                          Jangan Ganggu?
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Isi Chat */}
                  <div>
                    <h4 className="font-medium text-gray-800 mb-3">Isi Chat</h4>
                    <div>
                      <label className="block text-sm text-gray-700 mb-1">Pemisah Tanggal</label>
                      <input
                        type="text"
                        value={dateSeparatorText}
                        onChange={(e) => setDateSeparatorText(e.target.value)}
                        placeholder="Contoh: HARI INI, KEMARIN"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Editor Pesan */}
            <div className="bg-white border border-gray-200 rounded-lg">
              <button
                onClick={() => setEditorCollapsed(!editorCollapsed)}
                className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
              >
                <div className="flex items-center gap-2">
                  <Edit3 className="w-4 h-4 text-green-600" />
                  <span className="font-medium text-gray-800">Editor Pesan</span>
                </div>
                {editorCollapsed ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
              </button>

              {!editorCollapsed && (
                <div className="p-4 pt-0 space-y-4">
                  {messages.map((message, index) => (
                    <div key={message.id} className="border border-gray-200 rounded-lg p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">Pesan {index + 1}</span>
                        {messages.length > 1 && (
                          <button
                            onClick={() => deleteMessage(message.id)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        )}
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Pengirim
                          </label>
                          <select
                            value={message.sender}
                            onChange={(e) => updateMessage(message.id, 'sender', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                          >
                            <option value="customer">Pelanggan</option>
                            <option value="business">Bisnis</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Waktu
                          </label>
                          <input
                            type="time"
                            value={message.time}
                            onChange={(e) => updateMessage(message.id, 'time', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Gambar Pesan (Opsional)
                        </label>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => updateMessage(message.id, 'image', e.target.files?.[0] || null)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Teks Pesan
                        </label>
                        <textarea
                          value={message.text}
                          onChange={(e) => updateMessage(message.id, 'text', e.target.value)}
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                          placeholder="Tulis pesan di sini..."
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Status
                        </label>
                        <select
                          value={message.status}
                          onChange={(e) => updateMessage(message.id, 'status', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        >
                          <option value="sent">Dikirim</option>
                          <option value="delivered">Diterima</option>
                          <option value="read">Dibaca</option>
                        </select>
                      </div>
                    </div>
                  ))}

                  <button
                    onClick={addMessage}
                    className="w-full flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-gray-600 hover:bg-gray-50"
                  >
                    <Plus className="w-4 h-4" />
                    Tambah Pesan
                  </button>
                </div>
              )}
            </div>

            {/* Frame HP & Download */}
            <div className="space-y-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={includePhoneFrame}
                  onChange={(e) => setIncludePhoneFrame(e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700">Sertakan frame HP</span>
              </label>

              <button
                onClick={handleDownloadScreenshot}
                disabled={isDownloading}
                className={`w-full flex items-center justify-center gap-2 px-4 py-3 text-white rounded-md ${isDownloading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-green-600 hover:bg-green-700'
                  }`}
              >
                {isDownloading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Mengunduh...
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4" />
                    Unduh Screenshot
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Right Panel - Preview */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="text-center mb-4">
              <h3 className="text-lg font-medium text-gray-800">Preview WhatsApp</h3>
            </div>

            {/* Phone Frame */}
            <div ref={previewRef} className="mx-auto flex justify-center">
              {includePhoneFrame && (
                <div
                  className={`${phoneStyle === 'iphone' ? 'bg-black' : 'bg-gray-800'} ${phoneStyle === 'iphone' ? 'rounded-[2.5rem]' : 'rounded-[2rem]'} p-2 shadow-2xl`}
                  style={{ width: '375px', height: '812px' }}
                >
                  <div className={`${phoneStyle === 'iphone' ? 'rounded-[2.25rem]' : 'rounded-[1.75rem]'} overflow-hidden ${theme === 'dark' ? 'bg-gray-900' : 'bg-white'} relative w-full h-full flex flex-col`}>
                    {/* iPhone Top Area with Notch */}
                    {phoneStyle === 'iphone' && (
                      <div
                        className={`${theme === 'dark' ? 'bg-gray-900' : 'bg-white'} relative flex-shrink-0`}
                        style={{ height: '44px' }}
                      >
                        {/* Notch */}
                        <div
                          className="absolute bg-black z-10"
                          style={{
                            top: '0px',
                            left: '50%',
                            transform: 'translateX(-50%)',
                            width: '180px',
                            height: '30px',
                            borderRadius: '0 0 20px 20px'
                          }}
                        ></div>

                        {/* Left Status */}
                        <div className={`absolute left-6 top-3 ${theme === 'dark' ? 'text-white' : 'text-black'} text-sm font-semibold z-0`}>
                          {statusTime}
                        </div>

                        {/* Right Status */}
                        <div className={`absolute right-6 top-3 flex items-center gap-1 ${theme === 'dark' ? 'text-white' : 'text-black'} text-sm z-0`}>
                          {doNotDisturb && <span className="text-xs">🌙</span>}
                          {network === 'wifi' && <Wifi className="w-4 h-4" />}
                          {network === '5g' && <span className="text-xs font-semibold">5G</span>}
                          {network === '4g' && <span className="text-xs font-semibold">4G</span>}
                          <Signal className="w-4 h-4" />
                          <div className="flex items-center gap-1">
                            <span className="text-xs font-semibold">{battery}%</span>
                            <Battery className={`w-4 h-2 ${charging ? 'text-green-500' : (theme === 'dark' ? 'text-white' : 'text-black')}`} />
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Samsung Status Bar */}
                    {phoneStyle === 'samsung' && (
                      <div className={`${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-black'} px-4 py-2 flex justify-between items-center text-sm font-medium flex-shrink-0 relative`}>
                        {/* Samsung Punch Hole */}
                        <div
                          className="samsung-punch-hole absolute"
                          style={{
                            top: '15px',
                            left: '50%',
                            transform: 'translateX(-50%)',
                            width: '10px',
                            height: '10px',
                            background: '#050505',
                            borderRadius: '50%',
                            zIndex: 20
                          }}
                        ></div>

                        <span className="font-medium">{statusTime}</span>
                        <div className="flex items-center gap-1">
                          {doNotDisturb && <span className="text-sm">🌙</span>}
                          {network === 'wifi' && <Wifi className="w-4 h-4" />}
                          {network === '5g' && <span className="text-xs font-medium">5G</span>}
                          {network === '4g' && <span className="text-xs font-medium">4G</span>}
                          <Signal className="w-4 h-4" />
                          <span className="text-sm font-medium">{battery}</span>
                          <Battery className={`w-5 h-3 ${charging ? 'text-green-500' : ''}`} />
                        </div>
                      </div>
                    )}

                    {/* WhatsApp Header */}
                    <div className={`px-4 py-3 flex items-center gap-3 flex-shrink-0 text-white`}
                      style={{ backgroundColor: theme === 'dark' ? 'rgb(31, 44, 52)' : '#075e54' }}>
                      <button className="text-white">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                      </button>
                      <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center overflow-hidden">
                        {contactPhotoPreview ? (
                          <img src={contactPhotoPreview} alt="Contact" className="w-full h-full object-cover" />
                        ) : (
                          <span className="text-gray-600 text-xs">👤</span>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-base">{contactName}</div>
                        <div className="text-sm opacity-90">{contactSubtext}</div>
                      </div>
                      <div className="flex gap-4">
                        <button className="text-white">
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </button>
                        <button className="text-white">
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* Chat Area */}
                    <div
                      className={`${theme === 'dark' ? 'bg-gray-900' : ''} flex-1 p-4 space-y-2 overflow-y-auto`}
                      style={{
                        backgroundColor: theme === 'light' ? '#e5ddd5' : '#0f172a',
                        backgroundImage: theme === 'light' ? 'url("https://user-images.githubusercontent.com/15075759/28719144-86dc0f70-73b1-11e7-911d-60d70fcded21.png")' : 'none',
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        backgroundRepeat: 'no-repeat'
                      }}
                    >
                      {dateSeparatorText && (
                        <div className="text-center mb-4">
                          <span className={`${theme === 'dark' ? 'bg-gray-700 text-gray-300' : 'bg-white text-gray-600'} px-3 py-1 rounded-lg text-xs shadow-sm`}>
                            {dateSeparatorText.toUpperCase()}
                          </span>
                        </div>
                      )}

                      {messages.map((message) => (
                        <div key={message.id} className={`flex ${message.sender === 'business' ? 'justify-end' : 'justify-start'} mb-1`}>
                          <div className={`max-w-xs relative ${message.sender === 'business'
                            ? (theme === 'dark' ? 'text-white' : 'text-black')
                            : (theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-white text-gray-800')
                            } rounded-lg p-3 shadow-sm`}
                            style={{
                              backgroundColor: message.sender === 'business'
                                ? (theme === 'dark' ? 'rgb(0, 92, 75)' : 'rgb(220, 248, 198)')
                                : undefined,
                              borderRadius: message.sender === 'business'
                                ? '18px 18px 4px 18px'
                                : '18px 18px 18px 4px'
                            }}>
                            {message.image && (
                              <div className="mb-2">
                                <img src={getMessageImagePreview(message)} alt="Message" className="w-full rounded-md" />
                              </div>
                            )}
                            <div className="text-sm leading-relaxed">{message.text}</div>
                            <div className={`text-xs mt-1 flex items-center justify-end gap-1 ${message.sender === 'business'
                              ? (theme === 'dark' ? 'text-green-100' : 'text-green-700')
                              : 'text-gray-500'
                              }`}>
                              <span>{message.time}</span>
                              {message.sender === 'business' && (
                                <span className="text-xs flex items-center">
                                  {message.status === 'sent' && (
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-3 h-3 text-gray-400" aria-hidden="true">
                                      <path d="M20 6 9 17l-5-5"></path>
                                    </svg>
                                  )}
                                  {message.status === 'delivered' && (
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 text-gray-400" aria-hidden="true">
                                      <path d="M18 6 7 17l-5-5"></path>
                                      <path d="m22 10-7.5 7.5L13 16"></path>
                                    </svg>
                                  )}
                                  {message.status === 'read' && (
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 text-[#53bdeb]" aria-hidden="true">
                                      <path d="M18 6 7 17l-5-5"></path>
                                      <path d="m22 10-7.5 7.5L13 16"></path>
                                    </svg>
                                  )}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Input Area */}
                    <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'} p-3 flex items-center gap-3 flex-shrink-0`}>
                      <button className="text-2xl">+</button>
                      <div className={`flex-1 ${theme === 'dark' ? 'bg-gray-700' : 'bg-white'} rounded-full px-4 py-2 text-sm flex items-center gap-2`}>
                        <span className="text-gray-500">Tulis pesan</span>
                        <div className="flex-1"></div>
                        <button className="text-gray-500">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                          </svg>
                        </button>
                        <button className="text-gray-500">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                        </button>
                      </div>
                      <button className="bg-teal-600 text-white rounded-full p-2">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {!includePhoneFrame && (
                <div
                  className="border border-gray-300 rounded-lg overflow-hidden mx-auto flex flex-col"
                  style={{ width: '375px', height: '812px' }}
                >
                  {/* Status Bar */}
                  <div className={`${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-black'} ${phoneStyle === 'samsung' ? 'px-4 py-2' : 'px-6 py-3'} flex justify-between items-center text-sm ${phoneStyle === 'samsung' ? 'font-medium' : 'font-semibold'} border-b flex-shrink-0 relative`}>
                    {/* Samsung Punch Hole */}
                    {phoneStyle === 'samsung' && (
                      <div
                        className="samsung-punch-hole absolute"
                        style={{
                          top: '15px',
                          left: '50%',
                          transform: 'translateX(-50%)',
                          width: '10px',
                          height: '10px',
                          background: '#050505',
                          borderRadius: '50%',
                          zIndex: 20
                        }}
                      ></div>
                    )}

                    <span className={phoneStyle === 'samsung' ? 'font-medium' : 'font-semibold'}>{statusTime}</span>
                    <div className="flex items-center gap-1">
                      {doNotDisturb && <span className="text-sm">🌙</span>}
                      {network === 'wifi' && <Wifi className="w-4 h-4" />}
                      {network === '5g' && <span className={`text-xs ${phoneStyle === 'samsung' ? 'font-medium' : 'font-semibold'}`}>5G</span>}
                      {network === '4g' && <span className={`text-xs ${phoneStyle === 'samsung' ? 'font-medium' : 'font-semibold'}`}>4G</span>}
                      <Signal className="w-4 h-4" />
                      <div className="flex items-center gap-1">
                        <span className={`text-sm ${phoneStyle === 'samsung' ? 'font-medium' : 'font-semibold'}`}>
                          {phoneStyle === 'samsung' ? battery : `${battery}%`}
                        </span>
                        <Battery className={`w-5 h-3 ${charging ? 'text-green-500' : ''}`} />
                      </div>
                    </div>
                  </div>

                  {/* WhatsApp Header */}
                  <div className={`px-4 py-3 flex items-center gap-3 flex-shrink-0 text-white`}
                    style={{ backgroundColor: theme === 'dark' ? 'rgb(31, 44, 52)' : '#075e54' }}>
                    <button className="text-white">
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center overflow-hidden">
                      {contactPhotoPreview ? (
                        <img src={contactPhotoPreview} alt="Contact" className="w-full h-full object-cover" />
                      ) : (
                        <span className="text-gray-600 text-xs">👤</span>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-base">{contactName}</div>
                      <div className="text-sm opacity-90">{contactSubtext}</div>
                    </div>
                    <div className="flex gap-4">
                      <button className="text-white">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </button>
                      <button className="text-white">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                      </button>
                    </div>
                  </div>

                  {/* Chat Area */}
                  <div
                    className={`${theme === 'dark' ? 'bg-gray-900' : ''} flex-1 p-4 space-y-2 overflow-y-auto`}
                    style={{
                      backgroundColor: theme === 'light' ? '#e5ddd5' : '#0f172a',
                      backgroundImage: theme === 'light' ? 'url("https://user-images.githubusercontent.com/15075759/28719144-86dc0f70-73b1-11e7-911d-60d70fcded21.png")' : 'none',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      backgroundRepeat: 'no-repeat'
                    }}
                  >
                    {dateSeparatorText && (
                      <div className="text-center mb-4">
                        <span className={`${theme === 'dark' ? 'bg-gray-700 text-gray-300' : 'bg-white text-gray-600'} px-3 py-1 rounded-lg text-xs shadow-sm`}>
                          {dateSeparatorText.toUpperCase()}
                        </span>
                      </div>
                    )}

                    {messages.map((message) => (
                      <div key={message.id} className={`flex ${message.sender === 'business' ? 'justify-end' : 'justify-start'} mb-1`}>
                        <div className={`max-w-xs relative ${message.sender === 'business'
                          ? (theme === 'dark' ? 'text-white' : 'text-black')
                          : (theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-white text-gray-800')
                          } rounded-lg p-3 shadow-sm`}
                          style={{
                            backgroundColor: message.sender === 'business'
                              ? (theme === 'dark' ? 'rgb(0, 92, 75)' : 'rgb(220, 248, 198)')
                              : undefined,
                            borderRadius: message.sender === 'business'
                              ? '18px 18px 4px 18px'
                              : '18px 18px 18px 4px'
                          }}>
                          {message.image && (
                            <div className="mb-2">
                              <img src={getMessageImagePreview(message)} alt="Message" className="w-full rounded-md" />
                            </div>
                          )}
                          <div className="text-sm leading-relaxed">{message.text}</div>
                          <div className={`text-xs mt-1 flex items-center justify-end gap-1 ${message.sender === 'business'
                            ? (theme === 'dark' ? 'text-green-100' : 'text-green-700')
                            : 'text-gray-500'
                            }`}>
                            <span>{message.time}</span>
                            {message.sender === 'business' && (
                              <span className="text-xs flex items-center">
                                {message.status === 'sent' && (
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-3 h-3 text-gray-400" aria-hidden="true">
                                    <path d="M20 6 9 17l-5-5"></path>
                                  </svg>
                                )}
                                {message.status === 'delivered' && (
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 text-gray-400" aria-hidden="true">
                                    <path d="M18 6 7 17l-5-5"></path>
                                    <path d="m22 10-7.5 7.5L13 16"></path>
                                  </svg>
                                )}
                                {message.status === 'read' && (
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 text-[#53bdeb]" aria-hidden="true">
                                    <path d="M18 6 7 17l-5-5"></path>
                                    <path d="m22 10-7.5 7.5L13 16"></path>
                                  </svg>
                                )}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Input Area */}
                  <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-100'} p-3 flex items-center gap-3 flex-shrink-0`}>
                    <button className="text-2xl">+</button>
                    <div className={`flex-1 ${theme === 'dark' ? 'bg-gray-700' : 'bg-white'} rounded-full px-4 py-2 text-sm flex items-center gap-2`}>
                      <span className="text-gray-500">Tulis pesan</span>
                      <div className="flex-1"></div>
                      <button className="text-gray-500">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                        </svg>
                      </button>
                      <button className="text-gray-500">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      </button>
                    </div>
                    <button className="bg-teal-600 text-white rounded-full p-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                      </svg>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WATestimonialGeneratorPage;
