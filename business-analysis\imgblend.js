import OpenAI, { toFile } from "openai";
import fs from "fs";

const openai = new OpenAI({ apiKey: '***************************************************' });

async function generateWithReference() {
    const imageFiles = [
        "keyboard.png",
        "model.jpg"
    ];

    const images = await Promise.all(
        imageFiles.map(async (file) =>
            await toFile(fs.createReadStream(file), null, {
                type: "image/png",
            })
        ),
    );

    const result = await openai.images.edit({
        model: "gpt-image-1",
        prompt: `
🧩 Integrasi Gambar: Model manusia + produk

Instruksi:
- Integrasikan produk yang ditempel di tangan model agar menyatu alami.
- Tambahkan bayangan kontak dan refleksi realistis antara tangan & produk.
- Samakan tone warna, pen<PERSON><PERSON><PERSON>, dan perspektif produk dengan tubuh model.

🎭 Gaya Model & Pose:
- Model berdiri tegak dengan pose percaya diri.
- Tatapan model terfokus kagum pada produk di tangannya.
- Ekspresi wajah positif, penuh keyakinan.

🎥 Sudut Kamera:
- Mid-shot (setengah badan) dari depan, sedikit low angle untuk kesan dominan.
- Fokus tajam pada wajah & produk.

🎨 Gaya Artistik:
- Clean editorial style, pencahayaan studio lembut.
- Warna natural dengan kontras elegan.
- Sentuhan modern dan estetika premium ala iklan majalah.
    `,
        size: "1024x1024",
        n: 1,
        image: images,
    });

    const imageBase64 = result.data[0].b64_json;
    const buffer = Buffer.from(imageBase64, "base64");
    fs.writeFileSync("output-imgblend.png", buffer);
}

generateWithReference();
