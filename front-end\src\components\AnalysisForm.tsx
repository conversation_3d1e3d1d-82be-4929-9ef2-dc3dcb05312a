import React, { useState } from 'react';
import { TrendingUp } from 'lucide-react';

const AnalysisForm: React.FC = () => {
  const [formData, setFormData] = useState({
    businessType: '',
    targetCustomers: '',
    estimatedCapital: '',
    businessScale: '',
    advantages: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="font-semibold text-gray-800 mb-6"><PERSON><PERSON></h3>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <PERSON><PERSON>
          </label>
          <input
            type="text"
            placeholder="e.g., <PERSON><PERSON>, <PERSON><PERSON>, Bengkel Motor"
            value={formData.businessType}
            onChange={(e) => handleInputChange('businessType', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Target Pelanggan (Optional)
          </label>
          <textarea
            placeholder="e.g., Mahasiswa dan pekerja kantoran, usia 20-35 tahun, suka nongkrong."
            value={formData.targetCustomers}
            onChange={(e) => handleInputChange('targetCustomers', e.target.value)}
            rows={3}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors resize-none"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Estimasi Modal Anda (Optional)
          </label>
          <input
            type="text"
            placeholder="e.g., 50.000.000"
            value={formData.estimatedCapital}
            onChange={(e) => handleInputChange('estimatedCapital', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
          />
          <p className="text-xs text-gray-500 mt-1">
            AI akan menyesuaikan analisis modal sesuai budget Anda.
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Skala/Ukuran Usaha (Optional)
          </label>
          <input
            type="text"
            placeholder="e.g., Ruko 2 lantai, Kios kecil, Kafe 30 kursi"
            value={formData.businessScale}
            onChange={(e) => handleInputChange('businessScale', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
          />
          <p className="text-xs text-gray-500 mt-1">
            Membantu AI memberikan estimasi sewa & omset yang lebih akurat.
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Keunggulan Utama / Pembeda Anda (Optional)
          </label>
          <textarea
            placeholder="e.g., Biji kopi organik, Pelayanan super cepat, Harga paling murah"
            value={formData.advantages}
            onChange={(e) => handleInputChange('advantages', e.target.value)}
            rows={3}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors resize-none"
          />
          <p className="text-xs text-gray-500 mt-1">
            Membuat analisis kompetitor dan rekomendasi strategi lebih tajam.
          </p>
        </div>

        <button
          type="submit"
          className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-4 px-6 rounded-lg transition-colors flex items-center justify-center gap-2 shadow-sm"
        >
          <TrendingUp className="w-5 h-5" />
          Analisis Potensi Cuan
        </button>
      </form>
    </div>
  );
};

export default AnalysisForm;