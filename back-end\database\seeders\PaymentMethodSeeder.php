<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PaymentMethod;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = [
            [
                'name' => 'Bank Transfer BCA',
                'type' => 'bank',
                'description' => 'Transfer melalui Bank Central Asia',
                'details' => [
                    'bank_name' => 'Bank Central Asia (BCA)',
                    'account_number' => '**********',
                    'account_name' => 'PT Santuy Grow',
                    'bank_code' => 'BCA'
                ],
                'icon' => 'bca-logo.png',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Bank Transfer Mandiri',
                'type' => 'bank',
                'description' => 'Transfer melalui Bank Mandiri',
                'details' => [
                    'bank_name' => 'Bank Mandiri',
                    'account_number' => '**********',
                    'account_name' => 'PT Santuy Grow',
                    'bank_code' => 'MANDIRI'
                ],
                'icon' => 'mandiri-logo.png',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'GoPay',
                'type' => 'ewallet',
                'description' => 'Pembayaran melalui GoPay',
                'details' => [
                    'phone_number' => '************',
                    'account_name' => 'PT Santuy Grow'
                ],
                'icon' => 'gopay-logo.png',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'OVO',
                'type' => 'ewallet',
                'description' => 'Pembayaran melalui OVO',
                'details' => [
                    'phone_number' => '************',
                    'account_name' => 'PT Santuy Grow'
                ],
                'icon' => 'ovo-logo.png',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'DANA',
                'type' => 'ewallet',
                'description' => 'Pembayaran melalui DANA',
                'details' => [
                    'phone_number' => '************',
                    'account_name' => 'PT Santuy Grow'
                ],
                'icon' => 'dana-logo.png',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Cash on Delivery',
                'type' => 'cash',
                'description' => 'Bayar tunai saat barang diterima',
                'details' => [
                    'note' => 'Pembayaran dilakukan saat barang diterima'
                ],
                'icon' => 'cod-icon.png',
                'is_active' => true,
                'sort_order' => 6,
            ],
        ];

        foreach ($paymentMethods as $method) {
            PaymentMethod::updateOrCreate(
                ['name' => $method['name']],
                $method
            );
        }
    }
}
