import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  Users,
  CreditCard,
  Package,
  TrendingUp,
  Calendar,
  Clock,
  Star,
  Activity,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  DollarSign,
  UserCheck,
  <PERSON>rkles,
  ArrowRight,
  CheckCircle,
  Lock
} from 'lucide-react';

const API_BASE = import.meta.env.VITE_API_BASE || 'http://127.0.0.1:8000';

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalPackages: number;
  activePackages: number;
  totalPaymentMethods: number;
  activePaymentMethods: number;
}

interface DashboardPageProps {
  onNavigate?: (menuId: string) => void;
  onLockedItemClick?: (itemId: string) => void;
}

const DashboardPage: React.FC<DashboardPageProps> = ({ onNavigate, onLockedItemClick }) => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalPackages: 0,
    activePackages: 0,
    totalPaymentMethods: 0,
    activePaymentMethods: 0
  });
  const [loading, setLoading] = useState(true);

  // Check if user has active subscription
  const hasActiveSubscription = user?.has_subscription &&
    user?.subscription_status === 'active' &&
    user?.subscription_end &&
    new Date(user.subscription_end) > new Date();

  // Check if item is locked for non-subscribed users
  const isItemLocked = (itemId: string) => {
    if (user?.role === 'admin') return false; // Admin never locked
    if (itemId === 'dashboard') return false; // Dashboard always accessible

    // Lock all tools and business management features for non-subscribers
    const lockedItems = [
      'wa-testimonial-generator',
      'ads-image-generator',
      'marketing-content-generator',
      'peta-cuan',
      'kalkulator-hpp'
    ];

    return lockedItems.includes(itemId) && !hasActiveSubscription;
  };

  // Helper function to get auth headers
  const getAuthHeaders = () => {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    };
  };

  // Fetch dashboard stats for admin
  const fetchDashboardStats = async () => {
    if (user?.role !== 'admin') {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Fetch users, packages, and payment methods in parallel
      const [usersResponse, packagesResponse, paymentMethodsResponse] = await Promise.all([
        fetch(`${API_BASE}/api/users`, { headers: getAuthHeaders() }),
        fetch(`${API_BASE}/api/subscription-packages`, { headers: getAuthHeaders() }),
        fetch(`${API_BASE}/api/payment-methods`, { headers: getAuthHeaders() })
      ]);

      const [usersData, packagesData, paymentMethodsData] = await Promise.all([
        usersResponse.json(),
        packagesResponse.json(),
        paymentMethodsResponse.json()
      ]);

      if (usersData.success && packagesData.success && paymentMethodsData.success) {
        const users = usersData.users || [];
        const packages = packagesData.packages || [];
        const paymentMethods = paymentMethodsData.payment_methods || [];

        setStats({
          totalUsers: users.length,
          activeUsers: users.filter((u: any) => u.is_active).length,
          totalPackages: packages.length,
          activePackages: packages.filter((p: any) => p.is_active).length,
          totalPaymentMethods: paymentMethods.length,
          activePaymentMethods: paymentMethods.filter((pm: any) => pm.is_active).length,
        });
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardStats();
  }, [user]);

  const getCurrentGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Selamat Pagi';
    if (hour < 15) return 'Selamat Siang';
    if (hour < 18) return 'Selamat Sore';
    return 'Selamat Malam';
  };

  const formatDate = () => {
    return new Date().toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const quickActions = user?.role === 'admin' ? [
    {
      title: 'Kelola User',
      description: 'Tambah, edit, atau kelola status user',
      icon: Users,
      color: 'bg-blue-500',
      action: 'user-management'
    },
    {
      title: 'Kelola Paket',
      description: 'Atur harga dan fitur paket langganan',
      icon: Package,
      color: 'bg-emerald-500',
      action: 'subscription-packages'
    },
    {
      title: 'Payment Methods',
      description: 'Kelola metode pembayaran',
      icon: CreditCard,
      color: 'bg-purple-500',
      action: 'payment-methods'
    }
  ] : [
    {
      title: 'WA Testimonial Generator',
      description: 'Buat testimonial WhatsApp yang menarik',
      icon: Sparkles,
      color: 'bg-green-500',
      action: 'wa-testimonial-generator'
    },
    {
      title: 'Ads Image Generator',
      description: 'Generate gambar iklan yang eye-catching',
      icon: Activity,
      color: 'bg-blue-500',
      action: 'ads-image-generator'
    },
    {
      title: 'Kalkulator HPP',
      description: 'Hitung Harga Pokok Penjualan otomatis',
      icon: BarChart3,
      color: 'bg-orange-500',
      action: 'kalkulator-hpp'
    }
  ];

  return (
    <div className="p-6">
      {/* Welcome Header */}
      <div className="mb-8">
        <div className="bg-gradient-to-r from-emerald-500 to-green-600 rounded-xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">
                {getCurrentGreeting()}, {user?.name}! 👋
              </h1>
              <p className="text-emerald-100 text-lg">
                Selamat datang kembali di SantuyGrow
              </p>
              <p className="text-emerald-200 text-sm mt-1">
                {formatDate()}
              </p>
            </div>
            <div className="hidden md:block">
              <div className="bg-white/10 rounded-lg p-4">
                <Sparkles className="w-12 h-12 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Role-specific Content */}
      {user?.role === 'admin' ? (
        <>
          {/* Admin Stats */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <BarChart3 className="w-5 h-5 mr-2 text-emerald-600" />
              Statistik Sistem
            </h2>

            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Users Stats */}
                <div className="bg-white rounded-lg shadow p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Users</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
                      <p className="text-sm text-green-600">
                        <CheckCircle className="w-4 h-4 inline mr-1" />
                        {stats.activeUsers} aktif
                      </p>
                    </div>
                    <div className="bg-blue-100 rounded-lg p-3">
                      <Users className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                </div>

                {/* Packages Stats */}
                <div className="bg-white rounded-lg shadow p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Paket Langganan</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.totalPackages}</p>
                      <p className="text-sm text-green-600">
                        <CheckCircle className="w-4 h-4 inline mr-1" />
                        {stats.activePackages} aktif
                      </p>
                    </div>
                    <div className="bg-emerald-100 rounded-lg p-3">
                      <Package className="w-6 h-6 text-emerald-600" />
                    </div>
                  </div>
                </div>

                {/* Payment Methods Stats */}
                <div className="bg-white rounded-lg shadow p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Payment Methods</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.totalPaymentMethods}</p>
                      <p className="text-sm text-green-600">
                        <CheckCircle className="w-4 h-4 inline mr-1" />
                        {stats.activePaymentMethods} aktif
                      </p>
                    </div>
                    <div className="bg-purple-100 rounded-lg p-3">
                      <CreditCard className="w-6 h-6 text-purple-600" />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </>
      ) : (
        <>
          {/* User Welcome Message */}
          <div className="mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="text-center">
                <div className="bg-emerald-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Star className="w-8 h-8 text-emerald-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Siap untuk mengembangkan bisnis Anda?
                </h2>
                <p className="text-gray-600">
                  Gunakan tools yang tersedia untuk meningkatkan penjualan dan mengoptimalkan strategi marketing Anda.
                </p>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Quick Actions */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <Activity className="w-5 h-5 mr-2 text-emerald-600" />
          {user?.role === 'admin' ? 'Quick Actions' : 'Tools Populer'}
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {quickActions.map((action, index) => {
            const isLocked = isItemLocked(action.action);

            return (
              <div
                key={index}
                onClick={() => {
                  if (isLocked && onLockedItemClick) {
                    onLockedItemClick(action.action);
                  } else if (onNavigate) {
                    onNavigate(action.action);
                  }
                }}
                className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer group"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`${isLocked ? 'bg-gray-400' : action.color} rounded-lg p-3 group-hover:scale-110 transition-transform relative`}>
                      <action.icon className="w-6 h-6 text-white" />
                      {isLocked && (
                        <div className="absolute -top-1 -right-1 bg-yellow-500 rounded-full p-1">
                          <Lock className="w-3 h-3 text-white" />
                        </div>
                      )}
                    </div>
                    {isLocked ? (
                      <Lock className="w-5 h-5 text-gray-400" />
                    ) : (
                      <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-emerald-600 transition-colors" />
                    )}
                  </div>
                  <h3 className={`text-lg font-semibold mb-2 ${isLocked ? 'text-gray-500' : 'text-gray-900'}`}>
                    {action.title}
                    {isLocked && <span className="text-yellow-600 ml-2">🔒</span>}
                  </h3>
                  <p className={`text-sm ${isLocked ? 'text-gray-400' : 'text-gray-600'}`}>
                    {isLocked ? 'Berlangganan untuk mengakses fitur ini' : action.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Recent Activity or Tips */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <Clock className="w-5 h-5 mr-2 text-emerald-600" />
          {user?.role === 'admin' ? 'Tips Admin' : 'Tips Bisnis'}
        </h2>

        <div className="space-y-4">
          {user?.role === 'admin' ? (
            <>
              <div className="flex items-start space-x-3">
                <div className="bg-blue-100 rounded-full p-2 mt-1">
                  <Users className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Kelola User Secara Berkala</h4>
                  <p className="text-gray-600 text-sm">Pastikan untuk mereview status user dan menghapus akun yang tidak aktif.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="bg-emerald-100 rounded-full p-2 mt-1">
                  <Package className="w-4 h-4 text-emerald-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Update Paket Langganan</h4>
                  <p className="text-gray-600 text-sm">Sesuaikan harga dan fitur paket berdasarkan feedback user dan kondisi pasar.</p>
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-start space-x-3">
                <div className="bg-green-100 rounded-full p-2 mt-1">
                  <Sparkles className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Gunakan WA Testimonial Generator</h4>
                  <p className="text-gray-600 text-sm">Buat testimonial yang menarik untuk meningkatkan kepercayaan customer.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="bg-orange-100 rounded-full p-2 mt-1">
                  <BarChart3 className="w-4 h-4 text-orange-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Hitung HPP dengan Akurat</h4>
                  <p className="text-gray-600 text-sm">Pastikan pricing produk Anda profitable dengan kalkulator HPP otomatis.</p>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
