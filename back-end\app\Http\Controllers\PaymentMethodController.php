<?php

namespace App\Http\Controllers;

use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PaymentMethodController extends Controller
{
    /**
     * Display a listing of payment methods
     */
    public function index(Request $request)
    {
        try {
            // Check if user is admin
            if (!Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }

            $query = PaymentMethod::query();

            // Filter by type if specified
            if ($request->has('type') && $request->type !== '') {
                $query->where('type', $request->type);
            }

            // Filter by status if specified
            if ($request->has('is_active') && $request->is_active !== '') {
                $query->where('is_active', $request->is_active === 'true');
            }

            // Search by name
            if ($request->has('search') && $request->search !== '') {
                $search = $request->search;
                $query->where('name', 'like', "%{$search}%");
            }

            $paymentMethods = $query->ordered()->get();

            return response()->json([
                'success' => true,
                'payment_methods' => $paymentMethods
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch payment methods',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created payment method
     */
    public function store(Request $request)
    {
        try {
            // Check if user is admin
            if (!Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:payment_methods,name',
                'type' => 'required|string|in:bank,card,ewallet,cash',
                'description' => 'nullable|string',
                'details' => 'nullable|array',
                'icon' => 'nullable|string|max:255',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $paymentMethod = PaymentMethod::create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Payment method created successfully',
                'payment_method' => $paymentMethod
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment method',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified payment method
     */
    public function show($id)
    {
        try {
            // Check if user is admin
            if (!Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }

            $paymentMethod = PaymentMethod::find($id);

            if (!$paymentMethod) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment method not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'payment_method' => $paymentMethod
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch payment method',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified payment method
     */
    public function update(Request $request, $id)
    {
        try {
            // Check if user is admin
            if (!Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }

            $paymentMethod = PaymentMethod::find($id);

            if (!$paymentMethod) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment method not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:payment_methods,name,' . $id,
                'type' => 'required|string|in:bank,card,ewallet,cash',
                'description' => 'nullable|string',
                'details' => 'nullable|array',
                'icon' => 'nullable|string|max:255',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $paymentMethod->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Payment method updated successfully',
                'payment_method' => $paymentMethod->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment method',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified payment method
     */
    public function destroy($id)
    {
        try {
            // Check if user is admin
            if (!Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }

            $paymentMethod = PaymentMethod::find($id);

            if (!$paymentMethod) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment method not found'
                ], 404);
            }

            $paymentMethod->delete();

            return response()->json([
                'success' => true,
                'message' => 'Payment method deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete payment method',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle payment method status (active/inactive)
     */
    public function toggleStatus($id)
    {
        try {
            // Check if user is admin
            if (!Auth::user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }

            $paymentMethod = PaymentMethod::find($id);

            if (!$paymentMethod) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment method not found'
                ], 404);
            }

            $paymentMethod->is_active = !$paymentMethod->is_active;
            $paymentMethod->save();

            return response()->json([
                'success' => true,
                'message' => 'Payment method status updated successfully',
                'payment_method' => $paymentMethod
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment method status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get active payment methods for public use
     */
    public function getActive()
    {
        try {
            $paymentMethods = PaymentMethod::active()->ordered()->get();

            return response()->json([
                'success' => true,
                'payment_methods' => $paymentMethods
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch active payment methods',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
