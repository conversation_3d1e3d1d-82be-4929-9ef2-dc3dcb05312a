import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';

// Allow using google maps types loosely
declare global {
  interface Window {
    google: any;
  }
}

interface SelectedLocation {
  lat: number;
  lng: number;
  address?: string;
}

const DEFAULT_CENTER = { lat: -7.9543379, lng: 112.5779014 };

const API_BASE = (import.meta as any).env?.VITE_BACKEND_URL || 'http://localhost:8000';

function loadGoogleMaps(apiKey?: string): Promise<void> {
  return new Promise((resolve, reject) => {
    if (window.google && window.google.maps) {
      resolve();
      return;
    }

    if (!apiKey) {
      // No key: resolve softly so UI can still render, but map won't initialize
      resolve();
      return;
    }

    const existing = document.querySelector<HTMLScriptElement>('script[data-google-maps]');
    if (existing) {
      existing.addEventListener('load', () => resolve());
      existing.addEventListener('error', () => reject(new Error('Failed to load Google Maps script')));
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
    script.async = true;
    script.defer = true;
    script.setAttribute('data-google-maps', 'true');
    script.onload = () => resolve();
    script.onerror = () => reject(new Error('Failed to load Google Maps script'));
    document.head.appendChild(script);
  });
}

type LocationAnalyzePayload = {
  lat: number; lng: number; address?: string;
  businessType?: string; targetCustomers?: string; estimatedCapital?: string;
  businessScale?: string; advantages?: string;
};

type Props = {
  onAnalyze?: (payload: LocationAnalyzePayload) => Promise<void> | void;
  analysisLoading?: boolean;
};

const LocationForm: React.FC<Props> = ({ onAnalyze, analysisLoading }) => {
  const [formData, setFormData] = useState({
    businessType: '',
    targetCustomers: '',
    estimatedCapital: '',
    businessScale: '',
    advantages: ''
  });

  const [selected, setSelected] = useState<SelectedLocation | null>(null);
  const [mapError, setMapError] = useState<string | null>(null);

  type Idea = { nama?: string; keterangan?: string; ringkasan?: string; alasan?: string[]; skor_total?: number };
  const [ideas, setIdeas] = useState<Idea[]>([]);
  const [ideasLoading, setIdeasLoading] = useState(false);
  const [ideasError, setIdeasError] = useState<string | null>(null);
  const [selectedIdeaIdx, setSelectedIdeaIdx] = useState<number | null>(null);

  const loadingMsgs = [
    'Mengumpulkan data peta...',
    'Menganalisis kepadatan penduduk...',
    'Menilai kompetisi sekitar...',
    'Menghitung potensi cuan...',
    'Menyusun rekomendasi awal...'
  ];
  const [loadingMsgIdx, setLoadingMsgIdx] = useState(0);
  useEffect(() => {
    if (!analysisLoading) return;
    setLoadingMsgIdx(0);
    const id = setInterval(() => {
      setLoadingMsgIdx((i) => (i + 1) % loadingMsgs.length);
    }, 1200);
    return () => clearInterval(id);
  }, [analysisLoading]);


  const mapRef = useRef<HTMLDivElement | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);
  const mapInstanceRef = useRef<any>(null);
  const markerRef = useRef<any>(null);
  const geocoderRef = useRef<any>(null);

  useEffect(() => {
    const apiKey = (import.meta as any).env?.VITE_GOOGLE_MAPS_API_KEY as string | undefined;

    loadGoogleMaps(apiKey)
      .then(() => {
        if (!window.google || !mapRef.current) return;

        const map = new window.google.maps.Map(mapRef.current, {
          center: DEFAULT_CENTER,
          zoom: 14,
          mapTypeControl: false,
          streetViewControl: false,
          fullscreenControl: false,
        });
        mapInstanceRef.current = map;
        geocoderRef.current = new window.google.maps.Geocoder();

        // Marker lazy init
        const getMarker = () => {
          if (!markerRef.current) {
            markerRef.current = new window.google.maps.Marker({ map });
          }
          return markerRef.current;
        };

        // Click to set marker
        map.addListener('click', (e: any) => {
          const lat = e.latLng.lat();
          const lng = e.latLng.lng();
          const marker = getMarker();
          marker.setPosition({ lat, lng });
          setSelected({ lat, lng });

          if (geocoderRef.current) {
            geocoderRef.current.geocode({ location: { lat, lng } }, (results: any, status: string) => {
              if (status === 'OK' && results && results[0]) {
                setSelected({ lat, lng, address: results[0].formatted_address });
              }
            });
          }
        });

        // Places Autocomplete
        if (inputRef.current && window.google.maps.places) {
          const ac = new window.google.maps.places.Autocomplete(inputRef.current, {
            fields: ['geometry', 'formatted_address', 'name'],
          });
          ac.addListener('place_changed', () => {
            const place = ac.getPlace();
            if (!place || !place.geometry || !place.geometry.location) return;
            const lat = place.geometry.location.lat();
            const lng = place.geometry.location.lng();
            map.panTo({ lat, lng });
            map.setZoom(16);
            const marker = getMarker();
            marker.setPosition({ lat, lng });
            setSelected({ lat, lng, address: place.formatted_address || place.name });

          });
        }
      })
      .catch((err) => {
        setMapError(err?.message || 'Gagal memuat peta');
      });
  }, []);

  // Reset daftar ide saat pengguna memilih lokasi baru
  useEffect(() => {
    setIdeas([]);
    setIdeasError(null);
    setSelectedIdeaIdx(null);
  }, [selected?.lat, selected?.lng]);


  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selected || !onAnalyze) return;
    await onAnalyze({
      lat: selected.lat,
      lng: selected.lng,
      address: selected.address,
      businessType: formData.businessType,
      targetCustomers: formData.targetCustomers,
      estimatedCapital: formData.estimatedCapital,
      businessScale: formData.businessScale,
      advantages: formData.advantages,
    });
  };




  const hasApiKey = Boolean((import.meta as any).env?.VITE_GOOGLE_MAPS_API_KEY);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* Lokasi Usaha + Map */}
      <div className="mb-6">
        <h3 className="font-semibold text-gray-800 mb-2">Lokasi Usaha</h3>

        {!hasApiKey && (
          <div className="text-xs text-yellow-700 bg-yellow-50 border border-yellow-200 rounded p-2 mb-2">
            Tip: set VITE_GOOGLE_MAPS_API_KEY agar peta dan pencarian aktif.
          </div>
        )}

        <div className="relative">
          <div ref={mapRef} className="h-[480px] md:h-[600px] w-full rounded-md bg-gradient-to-br from-green-100 to-blue-100" />
          <div className="absolute top-3 left-3 z-10">
            <div className="flex items-center gap-2 bg-white border border-gray-200 rounded-sm shadow px-3 py-2">
              <MapPin className="w-4 h-4 text-gray-600" />
              <input
                ref={inputRef}
                type="text"
                placeholder="Cari tempat atau alamat"
                className="w-64 sm:w-80 bg-transparent focus:outline-none"
              />
            </div>
          </div>
        </div>

        <div className="mt-3 text-sm text-gray-700">
          <div className="font-medium mb-1">Lokasi terpilih</div>
          {selected ? (
            <div className="space-y-0.5">
              {selected.address && <div>{selected.address}</div>}
              <div>Lat: {selected.lat.toFixed(6)}, Lng: {selected.lng.toFixed(6)}</div>
            </div>
          ) : (
            <div className="text-gray-500">Belum ada lokasi terpilih</div>
          )}
        </div>

        {mapError && (
          <div className="mt-2 text-xs text-red-700 bg-red-50 border border-red-200 rounded p-2">
            {mapError}
          </div>
        )}
      </div>

      {/* Saran Ide Bisnis dari AI */}
      <div className="mb-6">
        <h3 className="font-semibold text-gray-800 mb-2">Saran Ide Bisnis dari AI</h3>
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-5 text-center">
          {ideas.length === 0 && (
            <>
              <p className="text-sm text-gray-600 mb-3">
                Pilih lokasi di peta untuk mendapatkan saran ide bisnis.
              </p>
              <button
                type="button"
                disabled={!selected || ideasLoading}
                onClick={async () => {
                  if (!selected) return;
                  try {
                    setIdeasError(null);
                    setIdeasLoading(true);
                    const res = await fetch(`${API_BASE}/api/business-ideas/fast`, {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({
                        lat: selected.lat,
                        lng: selected.lng,
                        address: selected.address,
                        businessType: formData.businessType,
                        targetCustomers: formData.targetCustomers,
                        estimatedCapital: formData.estimatedCapital,
                        businessScale: formData.businessScale,
                        advantages: formData.advantages,
                      }),
                    });
                    const data = await res.json();
                    if (!res.ok) throw new Error(data?.error || 'Gagal mengambil saran');
                    const arr = Array.isArray(data?.ide_bisnis) ? data.ide_bisnis : (Array.isArray(data?.ideas) ? data.ideas : []);
                    setIdeas(arr);
                    setSelectedIdeaIdx(null);
                  } catch (err: any) {
                    setIdeasError(err?.message || 'Terjadi kesalahan');
                  } finally {
                    setIdeasLoading(false);
                  }
                }}
                className="inline-flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white font-medium text-sm py-1.5 px-3 rounded-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Sparkles className="w-4 h-4" />
                {ideasLoading ? 'Meminta...' : 'Dapatkan Ide Bisnis AI'}
              </button>
            </>
          )}

          {ideasError && (
            <div className="mt-3 text-xs text-red-700 bg-red-50 border border-red-200 rounded p-2">{ideasError}</div>
          )}

          {ideas.length > 0 && (
            <div className="space-y-3 text-left">
              {ideas.map((it, idx) => (
                <div
                  key={idx}
                  className={`rounded-md p-3 cursor-pointer transition ${selectedIdeaIdx === idx ? 'border border-green-500 ring-1 ring-green-200 bg-green-50/40' : 'border border-gray-200 bg-white hover:bg-slate-50 hover:border-slate-300'}`}
                  onClick={() => {
                    const idea: any = it as any;
                    const nama = idea?.nama || '';
                    const alasanArr: string[] = Array.isArray(idea?.alasan) ? idea.alasan : [];
                    const target = idea?.ringkasan || (alasanArr.length ? alasanArr.join(', ') : '');
                    const biaya = idea?.estimasi?.biaya_awal_idr;
                    const min = typeof biaya?.min === 'number' ? biaya.min : undefined;
                    const max = typeof biaya?.max === 'number' ? biaya.max : undefined;
                    const estNumber: any = typeof min === 'number' ? min : (typeof max === 'number' ? max : '');
                    const pivot = typeof min === 'number' && typeof max === 'number' ? (min + max) / 2 : (typeof estNumber === 'number' ? estNumber : undefined);
                    let scale = '';
                    if (typeof pivot === 'number') {
                      if (pivot <= 50_000_000) scale = 'kecil';
                      else if (pivot <= 200_000_000) scale = 'menengah';
                      else scale = 'besar';
                    }
                    const advantages = alasanArr.slice(0, 2).join('; ');

                    setFormData((prev) => ({
                      ...prev,
                      businessType: nama || prev.businessType,
                      targetCustomers: target || prev.targetCustomers,
                      estimatedCapital: estNumber !== '' ? String(estNumber) : prev.estimatedCapital,
                      businessScale: scale || prev.businessScale,
                      advantages: advantages || prev.advantages,
                    }));
                    setSelectedIdeaIdx(idx);

                  }}
                >
                  <div className="flex items-center justify-between">
                    <div className="font-medium text-gray-800">{(it as any).nama || 'Ide Bisnis'}</div>
                    {typeof (it as any).skor_total === 'number' && (
                      <span className="text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-700 font-medium">Skor {(it as any).skor_total}</span>
                    )}
                  </div>
                  {(it as any).keterangan && (
                    <div className="text-sm text-gray-600 mt-1">{(it as any).keterangan}</div>
                  )}

                  {(it as any).ringkasan && (
                    <div className="text-xs text-gray-600 mt-1">{(it as any).ringkasan}</div>
                  )}
                  {!((it as any).ringkasan) && (it as any).alasan?.length && (
                    <div className="text-xs text-gray-600 mt-1">{(it as any).alasan.join(' • ')}</div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Form Detail */}
      <form onSubmit={handleSubmit} className="space-y-5">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Jenis Usaha</label>
          <input
            type="text"
            placeholder="e.g., Kedai Kopi, Toko Baju, Bengkel Motor"
            value={formData.businessType}
            onChange={(e) => handleInputChange('businessType', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Target Pelanggan (opsional)</label>
          <textarea
            placeholder="e.g., Mahasiswa dan pekerja kantoran, usia 20-35 tahun, suka nongkrong."
            value={formData.targetCustomers}
            onChange={(e) => handleInputChange('targetCustomers', e.target.value)}
            rows={3}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors resize-none"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Estimasi Modal Anda (opsional)</label>
          <input
            type="number"
            placeholder="e.g., 50000000"
            value={formData.estimatedCapital}
            onChange={(e) => handleInputChange('estimatedCapital', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
          />
          <p className="text-xs text-gray-500 mt-1">AI akan menyesuaikan analisis modal sesuai budget Anda.</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Skala/Ukuran Usaha (opsional)</label>
          <input
            type="text"
            placeholder="e.g., Ruko 2 lantai, Kios kecil, Kafe 30 kursi"
            value={formData.businessScale}
            onChange={(e) => handleInputChange('businessScale', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
          />
          <p className="text-xs text-gray-500 mt-1">Membantu AI memberikan estimasi sewa & omset yang lebih akurat.</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Keunggulan Utama / Pembeda Anda (opsional)</label>
          <textarea
            placeholder="e.g., Biji kopi organik, Pelayanan super cepat, Harga paling murah"
            value={formData.advantages}
            onChange={(e) => handleInputChange('advantages', e.target.value)}
            rows={3}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors resize-none"
          />
          <p className="text-xs text-gray-500 mt-1">Membuat analisis kompetitor dan rekomendasi strategi lebih tajam.</p>
        </div>

        <button
          type="submit"
          disabled={analysisLoading || !selected}
          className="w-full bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-medium py-4 px-6 rounded-lg transition-colors flex items-center justify-center gap-2 shadow-sm disabled:cursor-not-allowed"
        >
          {analysisLoading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>{loadingMsgs[loadingMsgIdx]}</span>
            </>
          ) : (
            <>
              <Sparkles className="w-4 h-4" />
              <span>Analisis Potensi Cuan</span>
            </>
          )}
        </button>

      </form>



      {/* Analisis dipindahkan ke RightInfoPanel */}

    </div>
  );


};
export default LocationForm;

