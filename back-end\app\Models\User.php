<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'google_id',
        'avatar',
        'role',
        'is_active',
        'has_subscription',
        'subscription_start',
        'subscription_end',
        'subscription_package_id',
        'subscription_status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'has_subscription' => 'boolean',
            'subscription_start' => 'datetime',
            'subscription_end' => 'datetime',
        ];
    }

    /**
     * Relationships
     */
    public function subscriptionPackage()
    {
        return $this->belongsTo(SubscriptionPackage::class);
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is active
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Check if user has active subscription
     */
    public function hasActiveSubscription(): bool
    {
        return $this->has_subscription &&
            $this->subscription_status === 'active' &&
            $this->subscription_end &&
            $this->subscription_end > now();
    }

    /**
     * Check if subscription is expired
     */
    public function isSubscriptionExpired(): bool
    {
        return $this->has_subscription &&
            ($this->subscription_status === 'expired' ||
                ($this->subscription_end && $this->subscription_end <= now()));
    }

    /**
     * Get days until subscription expires
     */
    public function getDaysUntilExpiration(): int
    {
        if (!$this->subscription_end) {
            return 0;
        }

        return max(0, now()->diffInDays($this->subscription_end, false));
    }

    /**
     * Activate subscription
     */
    public function activateSubscription(SubscriptionPackage $package)
    {
        $this->update([
            'has_subscription' => true,
            'subscription_package_id' => $package->id,
            'subscription_start' => now(),
            'subscription_end' => now()->addMonths($package->duration_months),
            'subscription_status' => 'active',
        ]);
    }
}
