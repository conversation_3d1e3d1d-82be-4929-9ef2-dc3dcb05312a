<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestVertexAIAuth extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:vertex-ai-auth';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Vertex AI authentication methods';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Vertex AI Authentication Methods...');
        $this->newLine();

        // Test 1: Environment token
        $this->info('1. Testing environment token...');
        $envToken = env('VERTEX_AI_ACCESS_TOKEN');
        if ($envToken) {
            $this->info('✅ Environment token found: ' . substr($envToken, 0, 20) . '...');
        } else {
            $this->warn('❌ No environment token found');
        }
        $this->newLine();

        // Test 2: Service account file
        $this->info('2. Testing service account file...');
        $keyFile = env('GOOGLE_APPLICATION_CREDENTIALS');
        
        if (!$keyFile) {
            $this->error('❌ GOOGLE_APPLICATION_CREDENTIALS not set');
            return;
        }

        if (!file_exists($keyFile)) {
            $this->error('❌ Service account file not found: ' . $keyFile);
            return;
        }

        $this->info('✅ Service account file found: ' . $keyFile);

        try {
            $client = new \Google\Client();
            $client->setAuthConfig($keyFile);
            $client->addScope([
                'https://www.googleapis.com/auth/cloud-platform',
                'https://www.googleapis.com/auth/generative-language'
            ]);

            $this->info('Attempting to get access token...');
            $token = $client->fetchAccessTokenWithAssertion();

            if (isset($token['access_token'])) {
                $this->info('✅ Successfully obtained access token from service account');
                $this->info('Token preview: ' . substr($token['access_token'], 0, 20) . '...');
                
                // Test the token with a simple API call
                $this->testTokenWithAPI($token['access_token']);
            } else {
                $this->error('❌ Failed to get access token');
                if (isset($token['error'])) {
                    $this->error('Error: ' . $token['error']);
                }
                if (isset($token['error_description'])) {
                    $this->error('Description: ' . $token['error_description']);
                }
            }
        } catch (\Exception $e) {
            $this->error('❌ Exception: ' . $e->getMessage());
        }
        $this->newLine();

        // Test 3: gcloud command
        $this->info('3. Testing gcloud command...');
        try {
            $output = shell_exec('gcloud auth print-access-token 2>&1');
            $token = trim($output);

            if (!empty($token) && !str_contains($token, 'ERROR') && !str_contains($token, 'gcloud')) {
                $this->info('✅ Successfully obtained access token from gcloud');
                $this->info('Token preview: ' . substr($token, 0, 20) . '...');
            } else {
                $this->warn('❌ gcloud command failed or not available');
                $this->warn('Output: ' . $output);
            }
        } catch (\Exception $e) {
            $this->warn('❌ gcloud command error: ' . $e->getMessage());
        }
        $this->newLine();

        // Test 4: Check service account permissions
        $this->info('4. Checking service account details...');
        try {
            $keyData = json_decode(file_get_contents($keyFile), true);
            $this->info('Project ID: ' . ($keyData['project_id'] ?? 'Not found'));
            $this->info('Client Email: ' . ($keyData['client_email'] ?? 'Not found'));
            $this->info('Service Account Type: ' . ($keyData['type'] ?? 'Not found'));
        } catch (\Exception $e) {
            $this->error('❌ Failed to read service account file: ' . $e->getMessage());
        }

        $this->newLine();
        $this->info('Authentication test completed.');
    }

    private function testTokenWithAPI($accessToken)
    {
        $this->info('Testing token with Vertex AI API...');
        
        $projectId = env('VERTEX_AI_PROJECT_ID');
        $location = env('VERTEX_AI_LOCATION', 'us-central1');
        $model = env('VERTEX_AI_MODEL', 'gemini-1.5-flash');

        if (!$projectId) {
            $this->warn('❌ VERTEX_AI_PROJECT_ID not configured');
            return;
        }

        try {
            $endpoint = "https://{$location}-aiplatform.googleapis.com/v1/projects/{$projectId}/locations/{$location}/publishers/google/models/{$model}:generateContent";

            $response = \Illuminate\Support\Facades\Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ])->timeout(30)->post($endpoint, [
                'contents' => [
                    [
                        'role' => 'user',
                        'parts' => [
                            ['text' => 'Hello, this is a test message. Please respond with "API test successful".']
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.1,
                    'maxOutputTokens' => 100,
                ],
            ]);

            if ($response->successful()) {
                $this->info('✅ Vertex AI API test successful!');
                $data = $response->json();
                $text = $data['candidates'][0]['content']['parts'][0]['text'] ?? 'No response text';
                $this->info('Response: ' . $text);
            } else {
                $this->error('❌ Vertex AI API test failed');
                $this->error('Status: ' . $response->status());
                $this->error('Response: ' . $response->body());
            }
        } catch (\Exception $e) {
            $this->error('❌ API test exception: ' . $e->getMessage());
        }
    }
}
