<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\SubscriptionPackage;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class InvoiceController extends Controller
{
    /**
     * Create invoice for subscription
     */
    public function createSubscriptionInvoice(Request $request)
    {
        try {
            $user = Auth::user();

            $validator = Validator::make($request->all(), [
                'subscription_package_id' => 'required|exists:subscription_packages,id',
                'payment_method_id' => 'required|exists:payment_methods,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $package = SubscriptionPackage::findOrFail($request->subscription_package_id);
            $paymentMethod = PaymentMethod::findOrFail($request->payment_method_id);

            // Create invoice
            $invoice = Invoice::create([
                'user_id' => $user->id,
                'subscription_package_id' => $package->id,
                'payment_method_id' => $paymentMethod->id,
                'amount' => $package->price,
                'due_date' => now()->addDays(7), // 7 days to pay
                'notes' => "Subscription for {$package->name} - {$package->duration_label}",
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Invoice created successfully',
                'invoice' => $invoice->load(['subscriptionPackage', 'paymentMethod'])
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create invoice',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's invoices
     */
    public function getUserInvoices()
    {
        try {
            $user = Auth::user();

            $invoices = $user->invoices()
                ->with(['subscriptionPackage', 'paymentMethod'])
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'invoices' => $invoices
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch invoices',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific invoice
     */
    public function show($id)
    {
        try {
            $user = Auth::user();

            $invoice = $user->invoices()
                ->with(['subscriptionPackage', 'paymentMethod'])
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'invoice' => $invoice
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invoice not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Submit payment proof for manual verification
     */
    public function submitPayment(Request $request, $id)
    {
        try {
            $user = Auth::user();
            $invoice = $user->invoices()->findOrFail($id);

            if ($invoice->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Invoice is not pending'
                ], 400);
            }

            $validator = Validator::make($request->all(), [
                'payment_proof' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
                'notes' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Store payment proof file
            $paymentProofPath = null;
            if ($request->hasFile('payment_proof')) {
                $file = $request->file('payment_proof');
                $filename = 'payment_proof_' . $invoice->id . '_' . time() . '.' . $file->getClientOriginalExtension();
                $paymentProofPath = $file->storeAs('payment_proofs', $filename, 'public');
            }

            // Update invoice with payment proof
            $invoice->update([
                'status' => 'pending_verification',
                'payment_details' => [
                    'payment_proof' => $paymentProofPath,
                    'notes' => $request->notes,
                    'submitted_at' => now(),
                ],
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment proof submitted successfully. Waiting for admin verification.',
                'invoice' => $invoice->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit payment proof',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Simulate payment (for demo purposes)
     */
    public function simulatePayment(Request $request, $id)
    {
        try {
            $user = Auth::user();

            $invoice = $user->invoices()->findOrFail($id);

            if ($invoice->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Invoice is not pending'
                ], 400);
            }

            // Mark invoice as paid
            $invoice->markAsPaid();

            // Activate user subscription
            $user->activateSubscription($invoice->subscriptionPackage);

            return response()->json([
                'success' => true,
                'message' => 'Payment successful! Subscription activated.',
                'invoice' => $invoice->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pending verification invoices (Admin only)
     */
    public function getPendingVerificationInvoices()
    {
        try {
            $user = Auth::user();

            if ($user->role !== 'admin') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $invoices = Invoice::with(['user', 'subscriptionPackage', 'paymentMethod'])
                ->where('status', 'pending_verification')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'invoices' => $invoices
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch invoices',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify payment (Admin only)
     */
    public function verifyPayment(Request $request, $id)
    {
        try {
            $user = Auth::user();

            if ($user->role !== 'admin') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'action' => 'required|in:approve,reject',
                'reason' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $invoice = Invoice::with(['user', 'subscriptionPackage'])->findOrFail($id);

            if ($invoice->status !== 'pending_verification') {
                return response()->json([
                    'success' => false,
                    'message' => 'Invoice is not pending verification'
                ], 400);
            }

            if ($request->action === 'approve') {
                // Mark invoice as paid
                $invoice->markAsPaid();

                // Activate user subscription
                $invoice->user->activateSubscription($invoice->subscriptionPackage);

                $message = 'Payment approved and subscription activated';
            } else {
                // Mark invoice as failed
                $invoice->update([
                    'status' => 'failed',
                    'payment_details' => array_merge(
                        $invoice->payment_details ?? [],
                        [
                            'rejection_reason' => $request->reason,
                            'rejected_at' => now(),
                            'rejected_by' => $user->id,
                        ]
                    ),
                ]);

                $message = 'Payment rejected';
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'invoice' => $invoice->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to verify payment',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
