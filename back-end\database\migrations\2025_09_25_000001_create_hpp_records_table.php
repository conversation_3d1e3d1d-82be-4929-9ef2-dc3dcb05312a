<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('hpp_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('worksheet_id')->constrained('worksheets')->cascadeOnDelete();
            $table->string('model', 50); // e.g. 'ritel-fnb'
            $table->string('title');
            $table->json('data');
            $table->timestamps();
            $table->index(['worksheet_id', 'created_at']);
            $table->index(['model', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('hpp_records');
    }
};
