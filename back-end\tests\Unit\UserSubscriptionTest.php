<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\SubscriptionPackage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class UserSubscriptionTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_activate_subscription_with_integer_duration()
    {
        // Create a subscription package
        $package = SubscriptionPackage::create([
            'name' => 'Test Package',
            'slug' => 'test-package',
            'description' => 'Test package description',
            'price' => 100000,
            'duration_months' => 3, // Integer value
            'duration_label' => 'per 3 bulan',
            'features' => ['Feature 1', 'Feature 2'],
            'color' => 'blue',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Create a user
        $user = User::factory()->create();

        // Activate subscription
        $user->activateSubscription($package);

        // Refresh user to get updated data
        $user->refresh();

        // Assert subscription is activated
        $this->assertTrue($user->has_subscription);
        $this->assertEquals($package->id, $user->subscription_package_id);
        $this->assertEquals('active', $user->subscription_status);
        $this->assertNotNull($user->subscription_start);
        $this->assertNotNull($user->subscription_end);

        // Assert subscription end date is correct (3 months from start)
        $expectedEndDate = $user->subscription_start->addMonths(3);
        $this->assertEquals(
            $expectedEndDate->format('Y-m-d'),
            $user->subscription_end->format('Y-m-d')
        );
    }

    public function test_user_can_activate_subscription_with_string_duration()
    {
        // Create a subscription package with string duration (simulating database issue)
        $package = SubscriptionPackage::create([
            'name' => 'Test Package',
            'slug' => 'test-package',
            'description' => 'Test package description',
            'price' => 100000,
            'duration_months' => '6', // String value to test type casting
            'duration_label' => 'per 6 bulan',
            'features' => ['Feature 1', 'Feature 2'],
            'color' => 'blue',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Create a user
        $user = User::factory()->create();

        // This should not throw an error even with string duration
        $user->activateSubscription($package);

        // Refresh user to get updated data
        $user->refresh();

        // Assert subscription is activated
        $this->assertTrue($user->has_subscription);
        $this->assertEquals($package->id, $user->subscription_package_id);
        $this->assertEquals('active', $user->subscription_status);
        $this->assertNotNull($user->subscription_start);
        $this->assertNotNull($user->subscription_end);

        // Assert subscription end date is correct (6 months from start)
        $expectedEndDate = $user->subscription_start->addMonths(6);
        $this->assertEquals(
            $expectedEndDate->format('Y-m-d'),
            $user->subscription_end->format('Y-m-d')
        );
    }

    public function test_subscription_package_duration_months_is_cast_to_integer()
    {
        // Create a subscription package
        $package = SubscriptionPackage::create([
            'name' => 'Test Package',
            'slug' => 'test-package',
            'description' => 'Test package description',
            'price' => 100000,
            'duration_months' => '12', // String value
            'duration_label' => 'per tahun',
            'features' => ['Feature 1', 'Feature 2'],
            'color' => 'blue',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Assert that duration_months is cast to integer
        $this->assertIsInt($package->duration_months);
        $this->assertEquals(12, $package->duration_months);
    }
}
