import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';
import {
  FileText,
  Calendar,
  Package,
  CreditCard,
  CheckCircle,
  XCircle,
  AlertCircle,
  DollarSign,
  User,
  Eye,
  Download,
  Loader2,
  RefreshCw
} from 'lucide-react';

const API_BASE = import.meta.env.VITE_API_BASE || 'http://127.0.0.1:8000';

interface Invoice {
  id: number;
  invoice_number: string;
  user_id: number;
  amount: number;
  status: 'pending' | 'pending_verification' | 'paid' | 'failed' | 'cancelled';
  due_date: string;
  paid_at: string | null;
  payment_details: {
    payment_proof?: string;
    notes?: string;
    submitted_at?: string;
  } | null;
  created_at: string;
  updated_at: string;
  user: {
    id: number;
    name: string;
    email: string;
  };
  subscription_package: {
    id: number;
    name: string;
    duration_label: string;
    color: string;
  } | null;
  payment_method: {
    id: number;
    name: string;
    type: string;
  } | null;
}

const AdminPaymentVerificationPage: React.FC = () => {
  const { user } = useAuth();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState('');

  // Helper function to get auth headers
  const getAuthHeaders = () => {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      throw new Error('No authentication token found');
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    };
  };

  const fetchPendingInvoices = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await fetch(`${API_BASE}/api/admin/invoices/pending-verification`, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      const data = await response.json();

      if (data.success) {
        setInvoices(data.invoices);
      } else {
        setError(data.message || 'Failed to fetch invoices');
      }

    } catch (error) {
      console.error('Error fetching invoices:', error);
      setError('Failed to fetch invoices');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyPayment = async (invoiceId: number, action: 'approve' | 'reject', reason?: string) => {
    try {
      setProcessing(true);
      setError('');

      const response = await fetch(`${API_BASE}/api/admin/invoices/${invoiceId}/verify`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          action,
          reason
        }),
      });

      const data = await response.json();

      if (data.success) {
        await fetchPendingInvoices();
        setSelectedInvoice(null);
        if (action === 'approve') {
          toast.success('Pembayaran Dikonfirmasi!\nPembayaran berhasil dikonfirmasi dan subscription user telah diaktifkan.', {
            duration: 6000,
          });
        } else {
          toast.success('Pembayaran Ditolak\nPembayaran telah ditolak dan user akan diberitahu.', {
            duration: 6000,
          });
        }
      } else {
        setError(data.message || 'Failed to verify payment');
      }

    } catch (error) {
      console.error('Error verifying payment:', error);
      setError('Failed to verify payment');
    } finally {
      setProcessing(false);
    }
  };

  useEffect(() => {
    if (user?.role === 'admin') {
      fetchPendingInvoices();
    }
  }, [user]);

  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending_verification':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <AlertCircle className="w-3 h-3 mr-1" />
            Menunggu Verifikasi
          </span>
        );
      case 'paid':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Approved
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="w-3 h-3 mr-1" />
            Rejected
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  // Show access denied if not admin
  if (user?.role !== 'admin') {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
          <p className="text-gray-500">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Verifikasi Pembayaran</h1>
            <p className="text-gray-600 mt-1">Kelola konfirmasi pembayaran dari user</p>
          </div>
          <button
            onClick={fetchPendingInvoices}
            disabled={loading}
            className="flex items-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-gray-300 transition-colors"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Invoice List */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Pembayaran Menunggu Verifikasi ({invoices.length})
            </h2>
          </div>

          <div className="divide-y divide-gray-200">
            {loading ? (
              <div className="p-6 text-center">
                <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2 text-gray-400" />
                <p className="text-gray-500">Loading invoices...</p>
              </div>
            ) : invoices.length === 0 ? (
              <div className="p-6 text-center">
                <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500">Tidak ada pembayaran yang menunggu verifikasi</p>
              </div>
            ) : (
              invoices.map((invoice) => (
                <div
                  key={invoice.id}
                  onClick={() => setSelectedInvoice(invoice)}
                  className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${selectedInvoice?.id === invoice.id ? 'bg-emerald-50 border-r-4 border-emerald-500' : ''
                    }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-gray-900">
                      {invoice.invoice_number}
                    </h3>
                    {getStatusBadge(invoice.status)}
                  </div>

                  <div className="flex items-center text-sm text-gray-600 mb-2">
                    <User className="w-4 h-4 mr-1" />
                    <span>{invoice.user.name}</span>
                    <span className="mx-2">•</span>
                    <span>{invoice.user.email}</span>
                  </div>

                  <div className="flex items-center text-sm text-gray-600 mb-2">
                    <Package className="w-4 h-4 mr-1" />
                    <span>{invoice.subscription_package?.name || 'Paket tidak tersedia'}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="w-4 h-4 mr-1" />
                      <span>{formatDate(invoice.payment_details?.submitted_at || invoice.created_at)}</span>
                    </div>
                    <div className="text-sm font-medium text-emerald-600">
                      {formatPrice(invoice.amount)}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Invoice Detail */}
        <div className="bg-white rounded-lg shadow">
          {selectedInvoice ? (
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-gray-900">Detail Pembayaran</h2>
                {getStatusBadge(selectedInvoice.status)}
              </div>

              {/* Invoice Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Nomor Invoice</label>
                  <p className="text-sm text-gray-900">{selectedInvoice.invoice_number}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">User</label>
                  <p className="text-sm text-gray-900">{selectedInvoice.user.name}</p>
                  <p className="text-xs text-gray-500">{selectedInvoice.user.email}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Paket</label>
                  <p className="text-sm text-gray-900">
                    {selectedInvoice.subscription_package?.name || 'Tidak tersedia'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {selectedInvoice.subscription_package?.duration_label || ''}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Total</label>
                  <p className="text-lg font-bold text-emerald-600">{formatPrice(selectedInvoice.amount)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Tanggal Submit</label>
                  <p className="text-sm text-gray-900">
                    {formatDate(selectedInvoice.payment_details?.submitted_at || selectedInvoice.created_at)}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Metode Pembayaran</label>
                  <p className="text-sm text-gray-900">
                    {selectedInvoice.payment_method?.name || 'Tidak tersedia'}
                  </p>
                </div>
              </div>

              {/* Payment Proof */}
              {selectedInvoice.payment_details?.payment_proof && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Bukti Transfer</label>
                  <div className="border border-gray-200 rounded-lg p-4">
                    <img
                      src={`${API_BASE}/storage/${selectedInvoice.payment_details.payment_proof}`}
                      alt="Payment proof"
                      className="max-w-full max-h-96 mx-auto rounded-lg"
                    />
                    <div className="mt-2 text-center">
                      <a
                        href={`${API_BASE}/storage/${selectedInvoice.payment_details.payment_proof}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-sm text-emerald-600 hover:text-emerald-700"
                      >
                        <Download className="w-4 h-4 mr-1" />
                        Download
                      </a>
                    </div>
                  </div>
                </div>
              )}

              {/* Notes */}
              {selectedInvoice.payment_details?.notes && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Catatan User</label>
                  <div className="bg-gray-50 rounded-lg p-3">
                    <p className="text-sm text-gray-700">{selectedInvoice.payment_details.notes}</p>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              {selectedInvoice.status === 'pending_verification' && (
                <div className="flex flex-col sm:flex-row gap-3">
                  <button
                    onClick={() => handleVerifyPayment(selectedInvoice.id, 'reject')}
                    disabled={processing}
                    className="flex-1 flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-300 transition-colors"
                  >
                    {processing ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    ) : (
                      <XCircle className="w-4 h-4 mr-2" />
                    )}
                    Tolak
                  </button>
                  <button
                    onClick={() => handleVerifyPayment(selectedInvoice.id, 'approve')}
                    disabled={processing}
                    className="flex-1 flex items-center justify-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-gray-300 transition-colors"
                  >
                    {processing ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    ) : (
                      <CheckCircle className="w-4 h-4 mr-2" />
                    )}
                    Setujui
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="p-6">
              <div className="text-center text-gray-500">
                <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Pilih pembayaran untuk melihat detail</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminPaymentVerificationPage;
