import { useState, useEffect } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import toast, { Toaster } from 'react-hot-toast';
import { Menu } from 'lucide-react';
import Sidebar from './components/Sidebar';
import LocationForm from './components/LocationForm';
import RightInfoPanel from './components/RightInfoPanel';
import HppCalculatorPage from './components/HppCalculatorPage';
import AdsImageGeneratorPage from './components/AdsImageGeneratorPage';
import WATestimonialGeneratorPage from './components/WATestimonialGeneratorPage';
import MarketingContentGeneratorPage from './components/MarketingContentGeneratorPage';
import UserManagementPage from './components/UserManagementPage';
import PaymentMethodsPage from './components/PaymentMethodsPage';
import SubscriptionPackagesPage from './components/SubscriptionPackagesPage';
import DashboardPage from './components/DashboardPage';
import InvoicesPage from './components/InvoicesPage';
import AdminPaymentVerificationPage from './components/AdminPaymentVerificationPage';
import SubscriptionModal from './components/SubscriptionModal';
import GoogleAuthCallback from './components/GoogleAuthCallback';
import LoginPage from './components/LoginPage';



const API_BASE = (import.meta as any).env?.VITE_BACKEND_URL || 'http://localhost:8000';



// Main App Component (wrapped with AuthProvider)
function AppContent() {
  const { user, isAuthenticated, isLoading, stopSubscriptionPolling } = useAuth();
  const [activeMenuItem, setActiveMenuItem] = useState('dashboard');
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [lockedFeature, setLockedFeature] = useState<string>('');
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<number | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);


  type AnalysisData = {
    lokasi: { nama: string; lat: number; lng: number; radius_km: number };
    ringkasan: { karakter: string; temuan_kunci: string[]; asumsi: string[] };
    ide_bisnis: Array<any>;
    rekomendasi_final: { pilihan: string; alasan: string[]; metrik_keberhasilan_90_hari: string[] };
  } | null;

  const [analysis, setAnalysis] = useState<AnalysisData>(null);
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'ringkasan' | 'data' | 'kompetitor' | 'swot' | 'modal' | 'rekomendasi'>('ringkasan');

  const handleAnalyze = async (payload: any) => {
    try {
      setAnalysisError(null);
      setAnalysisLoading(true);
      const res = await fetch(`${API_BASE}/api/business-ideas`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data?.error || 'Gagal mengambil analisis');
      setAnalysis(data);
      setActiveTab('ringkasan');
    } catch (err: any) {
      setAnalysisError(err?.message || 'Terjadi kesalahan');
    } finally {
      setAnalysisLoading(false);
    }
  };



  const handleLoginSuccess = () => {
    setActiveMenuItem('dashboard');
  };

  const handleLockedItemClick = (itemId: string) => {
    setLockedFeature(itemId);
    setShowSubscriptionModal(true);
  };

  const handleInvoiceCreated = (invoiceId: number) => {
    setSelectedInvoiceId(invoiceId);
    setActiveMenuItem('invoices');
  };

  const handleMobileMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMenuItemClick = (itemId: string) => {
    setActiveMenuItem(itemId);
    setIsMobileMenuOpen(false); // Close mobile menu when item is clicked
  };

  // Listen for subscription activation events
  useEffect(() => {
    const handleSubscriptionActivated = () => {
      stopSubscriptionPolling();
      toast.success('Subscription Diaktifkan!\nPembayaran Anda telah dikonfirmasi. Sekarang Anda dapat mengakses semua fitur premium!', {
        duration: 6000,
        position: 'top-right',
      });
    };

    window.addEventListener('subscriptionActivated', handleSubscriptionActivated);

    return () => {
      window.removeEventListener('subscriptionActivated', handleSubscriptionActivated);
    };
  }, [stopSubscriptionPolling]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-green-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <div className="text-gray-600">Loading...</div>
        </div>
      </div>
    );
  }

  // Check if this is Google auth callback
  if (window.location.pathname === '/auth/google/callback') {
    return <GoogleAuthCallback />;
  }

  // Show login page if not authenticated
  if (!isAuthenticated) {
    return <LoginPage onLoginSuccess={handleLoginSuccess} />;
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col lg:flex-row overflow-hidden">
      {/* Mobile Header */}
      <div className="lg:hidden bg-white border-b border-gray-200 p-4 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full overflow-hidden flex items-center justify-center">
            <img
              src="/logo.png"
              alt="Santuy Grow Logo"
              className="w-full h-full object-contain"
            />
          </div>
          <span className="font-semibold text-gray-800">GooAp</span>
        </div>
        <button
          onClick={handleMobileMenuToggle}
          className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
        >
          <Menu className="w-6 h-6" />
        </button>
      </div>

      <Sidebar
        activeItem={activeMenuItem}
        onItemClick={handleMenuItemClick}
        onLockedItemClick={handleLockedItemClick}
        isMobileMenuOpen={isMobileMenuOpen}
        onMobileMenuToggle={handleMobileMenuToggle}
      />

      <div className="flex-1 flex min-h-0">
        <main className="flex-1 p-4 lg:p-6 overflow-y-auto">
          {activeMenuItem === 'dashboard' ? (
            <DashboardPage
              onNavigate={handleMenuItemClick}
              onLockedItemClick={handleLockedItemClick}
            />
          ) : activeMenuItem === 'invoices' ? (
            <InvoicesPage
              selectedInvoiceId={selectedInvoiceId || undefined}
              onInvoiceSelect={() => setSelectedInvoiceId(null)}
            />
          ) : activeMenuItem === 'wa-testimonial-generator' ? (
            <WATestimonialGeneratorPage />
          ) : activeMenuItem === 'ads-image-generator' ? (
            <AdsImageGeneratorPage />
          ) : activeMenuItem === 'marketing-content-generator' ? (
            <MarketingContentGeneratorPage />
          ) : activeMenuItem === 'kalkulator-hpp' ? (
            <HppCalculatorPage />
          ) : activeMenuItem === 'user-management' ? (
            <UserManagementPage />
          ) : activeMenuItem === 'payment-methods' ? (
            <PaymentMethodsPage />
          ) : activeMenuItem === 'subscription-packages' ? (
            <SubscriptionPackagesPage />
          ) : activeMenuItem === 'payment-verification' ? (
            <AdminPaymentVerificationPage />
          ) : (
            <>
              <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-800 mb-2">
                  Peta Cuan Lokasi (AI)
                </h1>
                <p className="text-gray-600">
                  Dapatkan analisis potensi lokasi bisnis Anda sebelum membuka cabang baru.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <LocationForm onAnalyze={handleAnalyze} analysisLoading={analysisLoading} />
                </div>
                <div>
                  <RightInfoPanel analysis={analysis} analysisLoading={analysisLoading} analysisError={analysisError} activeTab={activeTab} onTabChange={setActiveTab} />
                </div>
              </div>
            </>
          )}
        </main>
      </div>

      {/* Subscription Modal */}
      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={() => setShowSubscriptionModal(false)}
        lockedFeature={lockedFeature}
        onInvoiceCreated={handleInvoiceCreated}
      />

      {/* React Hot Toast */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 5000,
          style: {
            borderRadius: '8px',
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
            fontWeight: '500',
          },
          success: {
            style: {
              background: '#10b981',
              color: '#fff',
              border: '1px solid #059669',
            },
            iconTheme: {
              primary: '#fff',
              secondary: '#10b981',
            },
          },
          error: {
            style: {
              background: '#ef4444',
              color: '#fff',
              border: '1px solid #dc2626',
            },
            iconTheme: {
              primary: '#fff',
              secondary: '#ef4444',
            },
          },
        }}
      />


    </div>
  );
}

// Main App wrapper with AuthProvider
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;