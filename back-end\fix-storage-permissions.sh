#!/bin/bash

# Fix Storage Permissions Script
# This script fixes common storage permission issues in Laravel

echo "🔧 Fixing Laravel Storage Permissions..."

# Set proper permissions for storage directory
echo "📁 Setting storage directory permissions..."
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/

# Set proper permissions for public storage link
echo "🔗 Checking storage link..."
if [ -L "public/storage" ]; then
    echo "✅ Storage link exists"
    ls -la public/storage
else
    echo "❌ Storage link missing, creating..."
    php artisan storage:link
fi

# Set proper permissions for payment_proofs directory
echo "💳 Setting payment_proofs permissions..."
if [ -d "storage/app/public/payment_proofs" ]; then
    chmod -R 755 storage/app/public/payment_proofs/
    echo "✅ Payment proofs directory permissions set"
else
    echo "📁 Creating payment_proofs directory..."
    mkdir -p storage/app/public/payment_proofs/
    chmod -R 755 storage/app/public/payment_proofs/
fi

# Set proper ownership (for production servers)
echo "👤 Setting ownership (if running as root)..."
if [ "$EUID" -eq 0 ]; then
    # Change ownership to web server user (adjust as needed)
    # Common web server users: www-data, apache, nginx
    WEB_USER="www-data"
    if id "$WEB_USER" &>/dev/null; then
        chown -R $WEB_USER:$WEB_USER storage/
        chown -R $WEB_USER:$WEB_USER bootstrap/cache/
        echo "✅ Ownership set to $WEB_USER"
    else
        echo "⚠️  Web server user $WEB_USER not found, skipping ownership change"
    fi
else
    echo "ℹ️  Not running as root, skipping ownership change"
fi

# Test storage access
echo "🧪 Testing storage access..."
TEST_FILE="storage/app/public/test-access.txt"
echo "test" > $TEST_FILE
if [ -f "$TEST_FILE" ]; then
    echo "✅ Storage write test passed"
    rm $TEST_FILE
else
    echo "❌ Storage write test failed"
fi

# Check if files exist in payment_proofs
echo "📋 Checking payment proof files..."
if [ -d "storage/app/public/payment_proofs" ]; then
    FILE_COUNT=$(find storage/app/public/payment_proofs -type f | wc -l)
    echo "📊 Found $FILE_COUNT payment proof files"
    
    if [ $FILE_COUNT -gt 0 ]; then
        echo "📄 Sample files:"
        ls -la storage/app/public/payment_proofs/ | head -5
    fi
else
    echo "❌ Payment proofs directory not found"
fi

echo ""
echo "🎉 Storage permissions fix completed!"
echo ""
echo "📝 Next steps for production:"
echo "1. Ensure web server has read access to storage/app/public/"
echo "2. Verify symbolic link works: public/storage -> storage/app/public"
echo "3. Check web server configuration allows following symlinks"
echo "4. Test URL: https://your-domain.com/storage/payment_proofs/filename.jpg"
echo ""
