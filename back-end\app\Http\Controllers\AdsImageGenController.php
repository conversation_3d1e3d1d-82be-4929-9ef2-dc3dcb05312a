<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class AdsImageGenController extends Controller
{
    public function generate(Request $request)
    {
        // Remove script execution time limit for long-running image generation
        if (function_exists('set_time_limit')) {
            @set_time_limit(0);
        }
        @ini_set('max_execution_time', '0');

        $validated = $request->validate([
            'desc' => 'required|string|min:10',
            'styleName' => 'nullable|string',
            'styleImageUrl' => 'nullable|url',
            'headline' => 'nullable|string',
            'subHeadline' => 'nullable|string',
            'cta' => 'nullable|string',
            'urgency' => 'nullable|string',
            'aspect' => 'required|string|in:1:1,4:5,3:4,16:9,9:16',
            'count' => 'required|integer|min:1|max:4',
        ]);

        $apiKey = config('services.openai.key') ?: env('OPENAI_API_KEY');
        if (!$apiKey) {
            return response()->json([
                'error' => 'OPENAI_API_KEY is not configured in environment.'
            ], 500);
        }

        // Map aspect ratio to supported OpenAI image sizes
        $size = match ($validated['aspect']) {
            '1:1' => '1024x1024',
            '16:9' => '1792x1024',
            '9:16' => '1024x1792',
            // OpenAI may not support these exactly; use closest safe fallback (square)
            '4:5' => '1024x1024',
            '3:4' => '1024x1024',
            default => '1024x1024',
        };

        $desc = trim($validated['desc']);
        $styleName = $validated['styleName'] ?? null;
        $styleImageUrl = $validated['styleImageUrl'] ?? null;
        $headline = $validated['headline'] ?? '';
        $subHeadline = $validated['subHeadline'] ?? '';
        $cta = $validated['cta'] ?? '';
        $urgency = $validated['urgency'] ?? '';
        $count = (int) $validated['count'];

        // Build prompt similar to business-analysis/imggen.js
        $prompt = "Buatkan visual iklan produk berikut.\n\n";
        $prompt .= "Narasi Produk, Target Audiens & Konteks:\n" . $desc . "\n\n";
        if ($styleName || $styleImageUrl) {
            $prompt .= "Gaya Visual:";
            if ($styleName) {
                $prompt .= " Gunakan gaya/tema: {$styleName}.";
            }
            if ($styleImageUrl) {
                $prompt .= " Rujuk mood & tone dari gambar referensi: {$styleImageUrl}.";
            }
            $prompt .= " Nuansa clean dan profesional, cocok untuk materi iklan digital.\n\n";
        }
        if ($headline || $subHeadline || $cta || $urgency) {
            $prompt .= "Copywriting di Visual:\n";
            if ($headline) $prompt .= "- Headline: \"{$headline}\"\n";
            if ($subHeadline) $prompt .= "- Subheadline: \"{$subHeadline}\"\n";
            if ($cta) $prompt .= "- Call to Action: \"{$cta}\"\n";
            if ($urgency) $prompt .= "- Urgency: \"{$urgency}\"\n";
            $prompt .= "\n";
        }
        $prompt .= "Hasilkan komposisi yang menarik secara visual dengan tipografi yang rapi.\n";

        try {
            $response = Http::withToken($apiKey)
                ->timeout(0)
                ->connectTimeout(0)
                ->post('https://api.openai.com/v1/images/generations', [
                    'model' => 'gpt-image-1',
                    'prompt' => $prompt,
                    'n' => $count,
                    'size' => $size,
                ]);

            if (!$response->successful()) {
                return response()->json([
                    'error' => 'OpenAI request failed',
                    'details' => $response->json(),
                ], $response->status());
            }

            $json = $response->json();
            $images = [];
            if (!empty($json['data']) && is_array($json['data'])) {
                foreach ($json['data'] as $item) {
                    if (!empty($item['b64_json'])) {
                        $images[] = 'data:image/png;base64,' . $item['b64_json'];
                    } elseif (!empty($item['url'])) {
                        $images[] = $item['url'];
                    }
                }
            }

            return response()->json([
                'images' => $images,
                'size' => $size,
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Server error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
