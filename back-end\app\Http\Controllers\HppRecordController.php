<?php

namespace App\Http\Controllers;

use App\Models\HppRecord;
use Illuminate\Http\Request;

class HppRecordController extends Controller
{
    public function index(Request $request)
    {
        $model = $request->query('model');
        $worksheetId = $request->query('worksheet_id');
        $q = HppRecord::query();
        if ($model) $q->where('model', $model);
        if ($worksheetId) $q->where('worksheet_id', $worksheetId);
        return response()->json($q->orderBy('created_at', 'desc')->get());
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'worksheet_id' => 'required|integer|exists:worksheets,id',
            'model' => 'required|string|max:50',
            'title' => 'nullable|string|max:255',
            'data' => 'required|array',
        ]);
        if (!isset($validated['title']) || $validated['title'] === null || $validated['title'] === '') {
            $validated['title'] = 'Hasil HPP ' . now()->format('Y-m-d H:i');
        }
        $rec = HppRecord::create($validated);
        return response()->json($rec, 201);
    }

    public function destroy(HppRecord $record)
    {
        $record->delete();
        return response()->json(['deleted' => true]);
    }
}
